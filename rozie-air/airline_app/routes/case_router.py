import traceback
from typing import Dict, Optional
from fastapi import APIRouter, HTTPException, Depends
from airline_app.adapters.auth_adapter import get_api_key

from airline_app.model.models import CaseCreation

from airline_app.helpers.logging_helper import print_info_log


from airline_app.services.case import create_case, get_case_by_customer_or_case_id

case_router = APIRouter(prefix="/case")

@case_router.post("/create_case", dependencies=[Depends(get_api_key)])
async def create_case_route(case_details: CaseCreation):
    try:
        result = create_case(case_details)
        if not result:
            raise HTTPException(status_code=404, detail="Error while creating case")
        return result
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})

@case_router.post("/get_case_details", dependencies=[Depends(get_api_key)])
async def get_case_details(data : Dict):
    try:
        id_input = data.get("id_input", "")
        input_type = data.get("input_type", "")
        case_details = get_case_by_customer_or_case_id(id_input, input_type)

        if not case_details:
            raise HTTPException(status_code=404, detail="Case not found")
        return case_details
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})