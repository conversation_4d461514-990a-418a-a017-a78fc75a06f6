const {ApiResp} = require("../helpers/api-helper")
const {createCase, listCases, updateCase} = require("../helpers/management-helper")

const handler = async (event) => {
    console.log("event => ", event)
    const path = event.resource
    const body = JSON.parse(event.body)
    try {
        switch (path){
            case "/createCase":
                const caseCreation = await createCase(body)
                return caseCreation

            case "/listCases":
                const caseList = await listCases(body)
                return caseList

            case "/updateCase":
                const caseUpdation = await updateCase(body)
                return caseUpdation
            default:
                return ApiResp({
                    status: "Error",
                    message: "Invalid resource path"
                }, 400)
        }
    } catch (error) {
        console.log(error)
        return ApiResp({
            status: "Error",
            message: "Internal Server Error"
        }, 500)
    }
}

module.exports = {
handler
}