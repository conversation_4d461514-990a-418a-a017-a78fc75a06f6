from fastapi import APIRouter, HTTPException, Query
from typing import Dict, Any
from services.flight_offer_service import get_flight_offer_service

router = APIRouter()

@router.get("/flight-offers-search")
async def get_flight_offers(
    origin: str = Query(..., description="Origin airport code (e.g., LON)"),
    destination: str = Query(..., description="Destination airport code (e.g., JFK)"),
    departure_date: str = Query(..., description="Departure date (YYYY-MM-DD)"),
    return_date: str = Query(None, description="Return date for round trips (YYYY-MM-DD)"),
    adults: int = Query(1, description="Number of adult passengers"),
    children: int = Query(0, description="Number of child passengers"),
    infants: int = Query(0, description="Number of infant passengers"),
    max_offers: int = Query(3, description="Maximum number of offers to return")
) -> Dict[str, Any]:
    """
    Search for flight offers.
    """
    try:
        service = get_flight_offer_service()
        response = service.get_flight_offers(
            origin=origin,
            destination=destination,
            departure_date=departure_date,
            return_date=return_date,
            adults=adults,
            children=children,
            infants=infants,
            max_offers=max_offers
        )
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 