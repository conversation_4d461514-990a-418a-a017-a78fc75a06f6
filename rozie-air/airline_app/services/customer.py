from airline_app.helpers.dynamo_helper import (
    query_dynamodb_table_item_gsi,
    get_item_by_primary_key,
    scan_table_with_filter,
    update_ddb_table_item,
)
from airline_app.common.constants import (
    PNR_TABLE,
    PNR_TABLE_CUSTOMER_INDEX,
    CUSTOMER_TABLE,
    CUSTOMER_TABLE,
    CUSTOMER_TABLE_PHONE_NUMBER_INDEX,
)

from airline_app.helpers.time_helper import get_rounded_relative_time
from airline_app.helpers.airline_code_helper import get_airline_name


def get_customer_id(phone_number=None, last_name=None):
    if phone_number:
        customer_details = query_dynamodb_table_item_gsi(
            CUSTOMER_TABLE,
            CUSTOMER_TABLE_PHONE_NUMBER_INDEX,
            {"phone_number": phone_number},
        )
        if customer_details:
            return [
                {
                    "customer_id": customer["customer_id"],
                    "first_name": customer["first_name"],
                    "last_name": customer["last_name"],
                }
                for customer in customer_details
            ]
    if last_name:
        items = scan_table_with_filter(
            CUSTOMER_TABLE,
            {"last_name": last_name.capitalize()},
            ["customer_id", "first_name", "last_name"],
        )
        if items:
            return items
    return None

def get_customer_details_by_id(customer_id):
    customer_details = get_item_by_primary_key(CUSTOMER_TABLE, "customer_id", customer_id)
    if customer_details:
        return {
            "customer_id": customer_details["customer_id"],
            "first_name": customer_details["first_name"],
            "last_name": customer_details["last_name"],
            "phone_number": customer_details["phone_number"],
            "email": customer_details["email"],
        }
    return None