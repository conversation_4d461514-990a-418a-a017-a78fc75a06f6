const { ApiResp } = require("../helpers/api-helper.js")
const { axiosPostRequest } = require("../helpers/axios-helper.js")

const getCustomerPNREndPoint = process.env.GET_CUSTOMER_PNR_ENDPOINT
const getCustomerPNRApikey = process.env.GET_CUSTOMER_PNR_APIKEY
const getBaggageEndPoint = process.env.GET_BAGGAGE_ENDPOINT
const getBaggageApikey = process.env.GET_BAGGAGE_APIKEY

/**
 * @function baggageWrapper
 * @description This function retrieves the PNR details of a customer, extracts the `pnr_id` from the first PNR record, 
 * and then makes an additional request to fetch baggage details using that `pnr_id`. The response will include a maximum 
 * of 3 baggage objects if more are present.
 * 
 * @param {string} customer_id - The ID of the customer for whom to retrieve PNR and baggage details.
 * 
 * @returns {Promise<Object>} - A response object containing the baggage details or an error message.
 * 
 * @throws {<PERSON>rror} - Throws an error if there is an issue with retrieving the PNR or baggage details.
 */
const baggageWrapper = async (customer_id) => {
	const headersPNR = {
		"Content-Type": "application/json",
		"x-api-key": getCustomerPNRApikey
	}

	const requestBodyPNR = {
		"customer_id": customer_id, 
		"pnr_status_flag":"completed" //for fetching completed pnr's for customer id, if not provided confirmed pnr's wil get fetched.
	}

	try {
		let baggageDetails = []
		const pnrResponse = await axiosPostRequest(getCustomerPNREndPoint, requestBodyPNR, headersPNR)
		
		if (pnrResponse.status === 404) {
			console.log("no pnr found for customer id")
			return ApiResp({
				status: "Error",
				message: "No baggage details found (no PNR found for customer_id)",
				response: baggageDetails
			}, 200)
		}

		if (pnrResponse && pnrResponse.response && pnrResponse.response.length > 0) {
			const pnrList = pnrResponse.response

			
			const completedPNRs = pnrList.filter(pnr => pnr.pnr_status === "completed")
			
			if (completedPNRs.length > 0) {
				const mostRecentCompletedPNR = completedPNRs.sort((a, b) => new Date(b.created_date) - new Date(a.created_date))[0]
				const pnrId = mostRecentCompletedPNR.pnr_id

				const headersBaggage = {
					"Content-Type": "application/json",
					"x-api-key": getBaggageApikey
				}

				const requestBodyBaggage = {
					"pnr_id": pnrId
				}

				const baggageResponse = await axiosPostRequest(getBaggageEndPoint, requestBodyBaggage, headersBaggage)
				baggageDetails = baggageResponse.baggage || []
				
				console.log("baggageDetails", baggageDetails)
				
				if (baggageResponse.status === 404) {
					console.log("For given pnr_id, no baggageDetails found")
					return ApiResp({
						status: "Error",
						message: "No baggage details found (no baggage for PNR)",
						response: baggageDetails
					}, 200)
				}
		
				if (baggageDetails.length > 3) {
					baggageDetails = baggageDetails.slice(0, 3) // Limit the baggage details to max 3
				}

				return ApiResp({
					status: "Success",
					message: "Baggage details retrieved successfully",
					response: baggageDetails
				})
			} else {
				console.log("No completed PNR found for customer id")
				return ApiResp({
					status: "Error",
					message: "No baggage details found (no completed PNR for customer)",
					response: baggageDetails
				}, 200)
			}

		} else {
			console.log("No PNR records found for the customer id")
			return ApiResp({
				status: "Error",
				message: "No PNR records found for the customer id",
				response: baggageDetails
			}, 200)
		}

	} catch (error) {
		console.error("Error:", error)
		return ApiResp({
			status: "Error",
			message: "Internal Server Error"
		}, 500)
	}
}

module.exports = {
	baggageWrapper
}
