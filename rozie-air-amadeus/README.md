# Flight Status API

A FastAPI application that uses the Amadeus Python SDK to provide on-demand flight status information.

## Setup

1. Clone the repository
2. Create a virtual environment and activate it:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
4. Create a `.env` file with your Amadeus API credentials:
   ```
   AMADEUS_CLIENT_ID=your_api_key
   AMADEUS_CLIENT_SECRET=your_api_secret
   AMADEUS_HOST=test  # Use 'test' for the Test environment or 'production' for Production
   ```

   To get your Amadeus API credentials:
   - Create an account at [Amadeus for Developers](https://developers.amadeus.com/)
   - Register a new application to get your API key and secret
   - Start with the Test environment and move to Production when ready

## Running the API

Start the API server with:

```
python main.py
```

Or use uvicorn directly:

```
uvicorn main:app --reload
```

The API will be available at http://localhost:8000

## API Endpoints

### GET /flight-status

Get the current status of a flight.

**Query Parameters:**
- `carrier_code` (required): The airline/carrier code (e.g., AA for American Airlines)
- `flight_number` (required): The flight number without carrier code (e.g., 1234)
- `scheduled_departure_date` (required): The scheduled departure date in YYYY-MM-DD format

**Example Request:**
```
GET /flight-status?carrier_code=BA&flight_number=1326&scheduled_departure_date=2023-12-25
```

## Interactive Documentation

FastAPI provides automatic interactive documentation:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc 