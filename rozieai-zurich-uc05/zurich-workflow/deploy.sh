#!/bin/bash

# =============================================================================
# ZURICH WORKFLOW DEPLOYMENT SCRIPT
# =============================================================================
# This script deploys the Zurich Workflow system to AWS
# Usage: ./deploy.sh [stage] [region]
# Example: ./deploy.sh dev ca-central-1

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================

STAGE=${1:-dev}
REGION=${2:-ca-central-1}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🚀 Starting Zurich Workflow deployment..."
echo "📍 Stage: $STAGE"
echo "🌍 Region: $REGION"
echo "📁 Working directory: $SCRIPT_DIR"

# =============================================================================
# ENVIRONMENT VALIDATION
# =============================================================================

echo "🔍 Validating environment..."

# Check if .env file exists
if [ ! -f "$SCRIPT_DIR/.env" ]; then
    echo "❌ Error: .env file not found!"
    echo "📝 Please copy .env.example to .env and configure your values"
    exit 1
fi

# Load environment variables
source "$SCRIPT_DIR/.env"

# Validate required environment variables
REQUIRED_VARS=(
    "AWS_REGION"
    "BACKEND_ECR_REPO_URI"
    "DASHBOARD_ECR_REPO_URI"
    "OPENAI_API_KEY"
    "SUPABASE_URL"
    "SUPABASE_ANON_KEY"
)

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Error: Required environment variable $var is not set"
        exit 1
    fi
done

echo "✅ Environment validation passed"

# =============================================================================
# DEPENDENCY CHECKS
# =============================================================================

echo "🔧 Checking dependencies..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Error: Docker is not running"
    exit 1
fi

# Check if AWS CLI is installed and configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ Error: AWS CLI is not configured"
    exit 1
fi

# Check if Serverless Framework is installed
if ! command -v sls &> /dev/null; then
    echo "📦 Installing Serverless Framework..."
    npm install -g serverless@3.38.0
fi

# Check if BAML CLI is installed
if ! command -v baml-cli &> /dev/null; then
    echo "📦 Installing BAML CLI..."
    npm install -g @boundaryml/baml
fi

echo "✅ Dependencies check passed"

# =============================================================================
# BUILD PROCESS
# =============================================================================

echo "🏗️ Building application..."

# Generate BAML client
echo "🤖 Generating BAML client..."
cd "$SCRIPT_DIR/baml_models"
baml-cli generate
cd "$SCRIPT_DIR"

# Get AWS account ID and ECR registry
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
ECR_REGISTRY="$AWS_ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com"

echo "🏷️ AWS Account ID: $AWS_ACCOUNT_ID"
echo "🏷️ ECR Registry: $ECR_REGISTRY"

# Login to ECR
echo "🔐 Logging into ECR..."
aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ECR_REGISTRY

# =============================================================================
# ECR REPOSITORY CREATION
# =============================================================================

echo "📦 Creating ECR repositories..."

# Backend repository
BACKEND_REPO_NAME="zurich-workflow-backend-$STAGE"
echo "Creating backend repository: $BACKEND_REPO_NAME"
aws ecr describe-repositories --repository-names $BACKEND_REPO_NAME --region $REGION > /dev/null 2>&1 || \
aws ecr create-repository --repository-name $BACKEND_REPO_NAME --region $REGION

# Dashboard repository
DASHBOARD_REPO_NAME="zurich-workflow-dashboard-$STAGE"
echo "Creating dashboard repository: $DASHBOARD_REPO_NAME"
aws ecr describe-repositories --repository-names $DASHBOARD_REPO_NAME --region $REGION > /dev/null 2>&1 || \
aws ecr create-repository --repository-name $DASHBOARD_REPO_NAME --region $REGION

# =============================================================================
# DOCKER IMAGE BUILDING
# =============================================================================

IMAGE_TAG="v$(date +%Y%m%d%H%M%S)"
echo "🏷️ Image tag: $IMAGE_TAG"

# Build backend image
echo "🐳 Building backend Docker image..."
BACKEND_IMAGE_URI="$ECR_REGISTRY/$BACKEND_REPO_NAME:$IMAGE_TAG"
docker build \
    --build-arg FASTAPI_ENV=$STAGE \
    --build-arg AWS_REGION=$REGION \
    --build-arg OPENAI_API_KEY=$OPENAI_API_KEY \
    --build-arg SUPABASE_URL=$SUPABASE_URL \
    --build-arg SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY \
    --build-arg SUPPORT_TOKEN=$SUPPORT_TOKEN \
    -t $BACKEND_IMAGE_URI \
    -f Dockerfile .

echo "📤 Pushing backend image to ECR..."
docker push $BACKEND_IMAGE_URI

# Build dashboard image
echo "🐳 Building dashboard Docker image..."
DASHBOARD_IMAGE_URI="$ECR_REGISTRY/$DASHBOARD_REPO_NAME:$IMAGE_TAG"
cd "$SCRIPT_DIR/zurich-dashboard"
docker build \
    --build-arg NODE_ENV=production \
    --build-arg SUPABASE_URL=$SUPABASE_URL \
    --build-arg SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY \
    --build-arg SUPPORT_TOKEN=$SUPPORT_TOKEN \
    -t $DASHBOARD_IMAGE_URI \
    -f Dockerfile .

echo "📤 Pushing dashboard image to ECR..."
docker push $DASHBOARD_IMAGE_URI

cd "$SCRIPT_DIR"

# =============================================================================
# INFRASTRUCTURE DEPLOYMENT
# =============================================================================

echo "☁️ Deploying infrastructure with Serverless..."

# Export environment variables for serverless
export BACKEND_ECR_REPO_URI=$BACKEND_IMAGE_URI
export DASHBOARD_ECR_REPO_URI=$DASHBOARD_IMAGE_URI

# Deploy with serverless
sls deploy \
    --stage $STAGE \
    --region $REGION \
    --verbose

# =============================================================================
# FRONTEND DEPLOYMENT
# =============================================================================

echo "🌐 Deploying frontend to S3..."

# Sync frontend files to S3
if [ -n "$frontend_s3_bucket" ]; then
    aws s3 sync frontend/ s3://$frontend_s3_bucket/ --delete --region $REGION
    echo "✅ Frontend deployed to S3 bucket: $frontend_s3_bucket"
else
    echo "⚠️ Frontend S3 bucket not configured, skipping frontend deployment"
fi

# =============================================================================
# DEPLOYMENT COMPLETION
# =============================================================================

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Deployment Summary:"
echo "   Stage: $STAGE"
echo "   Region: $REGION"
echo "   Backend Image: $BACKEND_IMAGE_URI"
echo "   Dashboard Image: $DASHBOARD_IMAGE_URI"
echo ""
echo "🔗 Service URLs:"
if [ -n "$backend_domain_name" ]; then
    echo "   Backend API: https://$backend_domain_name"
fi
if [ -n "$dashboard_domain_name" ]; then
    echo "   Dashboard: https://$dashboard_domain_name"
fi
if [ -n "$frontend_domain_name" ]; then
    echo "   Frontend: https://$frontend_domain_name"
fi
echo ""
echo "📚 Next steps:"
echo "   1. Test the API endpoints"
echo "   2. Verify the dashboard is accessible"
echo "   3. Check CloudWatch logs for any issues"
echo "   4. Update DNS records if needed"
echo ""
echo "✨ Happy deploying!"
