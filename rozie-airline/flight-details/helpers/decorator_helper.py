"""
This module contains decorator functions used in the lambda functions.

Functions:
- exception_handler

"""

from botocore.exceptions import ClientError,EndpointConnectionError
from .logging_helper import log_exception
def exception_handler(func):
    '''
    This function is a decorator that wraps the decorated function with a try-except block
    for handling exceptions. If the exception is a ClientError, the function logs the error
    message and returns a dictionary with a status code of 400. If the exception is any 
    other type, the function logs the error message and stack trace and returns a dictionary 
    with a status code of 400
    '''
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ClientError as exception:
            log_exception(exception.response["Error"]["Message"])
            return {"statusCode": 400}
        except EndpointConnectionError as exception:
            log_exception(exception)
            return {"statusCode": 400}
        except Exception as exception:
            log_exception(exception)
            return {"statusCode": 400}
    return wrapper
