"""
Module for handling Customer Profile CRUD operations
"""

import json
import os
from helpers.logging_helper import get_log_level, set_logging_level, print_info_log
from helpers.decorator_helper import exception_handler
from helpers.dynamo_helper import (
    get_item_by_keys,
    put_table_item,
    scan_table_with_filter,
    query_table_with_gsi,
    upsert_item,
    delete_item,
)
from helpers.response_helper import format_response
from helpers.utils import (get_next_number_in_sequence, check_if_item_already_present)

@exception_handler
def lambda_handler(event, context):
    """
    Lambda handler for Customer Profile CRUD operation APIs
    """
    set_logging_level(get_log_level())
    print_info_log("", "Customer Profile api invoked", event)
    http_method = event.get("httpMethod", "")
    path = event.get("path", "")

    if http_method == "GET" and path == "/customerProfile":
        return get_profile(event.get("queryStringParameters"))
    if http_method == "POST" and path == "/getCustomerProfile":
        return get_profile(json.loads(event.get("body", "{}")))
    if http_method == "POST" and path == "/customerProfile":
        return create_profile(event)
    if http_method == "PUT" and path == "/customerProfile": 
        return update_profile(event)
    if http_method == "DELETE" and path == "/customerProfile":
        return delete_profile(event)
    return format_response(400, None, "Invalid request")


def create_profile(event):
    """
    Create a new Customer Profile record in the database
    """
    profile_data = json.loads(event.get("body", "{}"))
    existance = check_if_item_already_present(os.environ.get("TABLE_NAME"), "PhoneNumberIndex", "phone_number", profile_data['phone_number'])

    if existance["isAvailable"]:
        return format_response(200, existance["data"], f"Customer profile already exists with phone number {profile_data['phone_number']}")
    customer_id= get_next_number_in_sequence(os.environ.get("TABLE_NAME"), "customer_id")
    profile_data["customer_id"] = customer_id
    status, _ = put_table_item(
        os.environ.get("TABLE_NAME"), profile_data, "customer_id"
    )
    if status == "Update":
        return format_response(400, None, "Unique customer_id is required")
    elif status == "Error":
        return format_response(500, None, "Encountered Technical Issue")
    else:
        return format_response(
            200, None, f"Profile created successfully, customer_id: {customer_id}"
        )


def get_profile(event):
    """
    Get Customer Profile by customer_id or phone_number
    """
    query_params = event or {}
    profile_data = []
    if query_params:
        if query_params.get("customer_id"):
            profile_data = get_item_by_keys(os.environ.get("TABLE_NAME"), "customer_id", query_params.get("customer_id"))
        elif query_params.get("phone_number"):
            phone_number = query_params.get("phone_number")
            if phone_number.startswith(" "):
                phone_number = "+" + phone_number.strip()
            profile_data = query_table_with_gsi(
                os.environ.get("TABLE_NAME"), "PhoneNumberIndex", "phone_number", phone_number
            )
            if profile_data:
                profile_data = profile_data[0]
    else:
        profile_data = scan_table_with_filter(
            os.environ.get("TABLE_NAME"), None, None
        )
    if profile_data:
        return format_response(200, profile_data, "profile found")
    return format_response(404, None, "profile not found")


def update_profile(event):
    """
    Update Customer Profile by phone number
    """
    profile_data = json.loads(event.get("body", "{}"))
    status = upsert_item(
        os.environ.get("TABLE_NAME"), profile_data, "customer_id"
    )
    if status == "Error":
        return format_response(500, None, "Encountered Technical Issue")
    else:
        return format_response(200, None, "Customer Profile Updated Successfully")


def delete_profile(event):
    """
    Delete Customer Profile by Customer ID
    """
    query_params = event.get("queryStringParameters", {})
    status = delete_item(
        os.environ.get("TABLE_NAME"),
        {"customer_id": query_params.get("customer_id")}
    )
    if status == "Error":
        return format_response(500, None, "Encountered Technical Issue")
    else:
        return format_response(200, None, "Customer Profile Deleted Successfully")
