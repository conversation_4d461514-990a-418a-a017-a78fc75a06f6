// Dashboard Module for Zurich Claims Management
class DashboardManager {
    constructor() {
        this.supabaseClient = null;
        this.claims = [];
        this.filteredClaims = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.init();
    }

    async init() {
        console.log('📊 Initializing Dashboard Manager...');
        
        // Wait for auth to be ready
        this.waitForAuth();
    }

    waitForAuth() {
        if (window.authManager && window.authManager.getSupabaseClient()) {
            this.supabaseClient = window.authManager.getSupabaseClient();
            this.setupEventListeners();
            console.log('✅ Dashboard Manager ready');
        } else {
            setTimeout(() => this.waitForAuth(), 100);
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterClaims(e.target.value);
            });
        }

        // Status filter
        const statusFilter = document.querySelector('.filter-select');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterClaimsByStatus(e.target.value);
            });
        }

        // Refresh button (if added)
        const refreshBtn = document.querySelector('.refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadDashboardData();
            });
        }
    }

    async loadDashboardData() {
        console.log('📊 Loading dashboard data...');
        
        try {
            // Load claims data
            await this.loadClaims();
            
            // Update metrics
            this.updateMetrics();
            
            // Render claims table
            this.renderClaimsTable();
            
            console.log('✅ Dashboard data loaded successfully');
            
        } catch (error) {
            console.error('❌ Error loading dashboard data:', error);
            this.showError('Failed to load dashboard data. Please refresh the page.');
        }
    }

    async loadClaims() {
        try {
            const { data: claims, error } = await this.supabaseClient
                .from('claims')
                .select('*')
                .order('created_at', { ascending: false })
                .limit(100);

            if (error) {
                throw error;
            }

            this.claims = claims || [];
            this.filteredClaims = [...this.claims];
            
            console.log(`📋 Loaded ${this.claims.length} claims`);
            
        } catch (error) {
            console.error('❌ Error loading claims:', error);
            this.claims = [];
            this.filteredClaims = [];
        }
    }

    updateMetrics() {
        const totalClaims = this.claims.length;
        const pendingClaims = this.claims.filter(claim => 
            ['NEW', 'PROCESSING', 'UNDER_REVIEW', 'PENDING_DOCUMENTS'].includes(claim.status?.toUpperCase())
        ).length;
        const approvedClaims = this.claims.filter(claim => 
            claim.status?.toUpperCase() === 'APPROVED'
        ).length;
        
        // Calculate average processing time (mock calculation)
        const avgProcessingTime = this.calculateAverageProcessingTime();

        // Update metric cards
        this.updateMetricCard('total-claims', totalClaims.toLocaleString());
        this.updateMetricCard('pending-claims', pendingClaims.toLocaleString());
        this.updateMetricCard('approved-claims', approvedClaims.toLocaleString());
        this.updateMetricCard('avg-processing-time', avgProcessingTime);
    }

    updateMetricCard(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    calculateAverageProcessingTime() {
        const completedClaims = this.claims.filter(claim => 
            ['APPROVED', 'DENIED', 'COMPLETED'].includes(claim.status?.toUpperCase())
        );

        if (completedClaims.length === 0) return '0 days';

        const totalDays = completedClaims.reduce((sum, claim) => {
            const created = new Date(claim.created_at);
            const updated = new Date(claim.updated_at);
            const diffTime = Math.abs(updated - created);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return sum + diffDays;
        }, 0);

        const avgDays = Math.round(totalDays / completedClaims.length);
        return `${avgDays} days`;
    }

    renderClaimsTable() {
        const tableBody = document.getElementById('claims-table-body');
        if (!tableBody) return;

        if (this.filteredClaims.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 2rem; color: var(--zd-color-grey-600);">
                        No claims found
                    </td>
                </tr>
            `;
            return;
        }

        // Paginate results
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedClaims = this.filteredClaims.slice(startIndex, endIndex);

        tableBody.innerHTML = paginatedClaims.map(claim => this.createClaimRow(claim)).join('');
    }

    createClaimRow(claim) {
        const status = claim.status || claim.workflow_status || 'unknown';
        const statusClass = status.toLowerCase().replace('_', '-');
        const priority = this.calculatePriority(claim);
        const assignedAgent = claim.assigned_agent || 'Unassigned';
        const submittedDate = this.formatDate(claim.created_at);

        return `
            <tr data-claim-id="${claim.id}">
                <td>
                    <a href="#" class="claim-link" data-claim-ref="${claim.claim_reference}">
                        ${claim.claim_reference}
                    </a>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">
                        ${this.formatStatus(status)}
                    </span>
                </td>
                <td>${submittedDate}</td>
                <td>${assignedAgent}</td>
                <td>
                    <span class="priority-badge ${priority.toLowerCase()}">
                        ${priority}
                    </span>
                </td>
                <td>
                    <button class="action-btn" onclick="dashboardManager.viewClaim('${claim.claim_reference}')">
                        View
                    </button>
                    <button class="action-btn" onclick="dashboardManager.editClaim('${claim.claim_reference}')">
                        Edit
                    </button>
                </td>
            </tr>
        `;
    }

    calculatePriority(claim) {
        // Simple priority calculation based on claim age and status
        const created = new Date(claim.created_at);
        const now = new Date();
        const daysDiff = Math.floor((now - created) / (1000 * 60 * 60 * 24));

        if (daysDiff > 30) return 'High';
        if (daysDiff > 14) return 'Medium';
        return 'Low';
    }

    formatStatus(status) {
        const statusMap = {
            'NEW': 'New',
            'PROCESSING': 'Processing',
            'UNDER_REVIEW': 'Under Review',
            'PENDING_DOCUMENTS': 'Pending Docs',
            'APPROVED': 'Approved',
            'DENIED': 'Denied',
            'COMPLETED': 'Completed',
            'CANCELLED': 'Cancelled'
        };

        return statusMap[status?.toUpperCase()] || status || 'Unknown';
    }

    formatDate(dateString) {
        if (!dateString) return 'N/A';
        
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    filterClaims(searchTerm) {
        if (!searchTerm.trim()) {
            this.filteredClaims = [...this.claims];
        } else {
            const term = searchTerm.toLowerCase();
            this.filteredClaims = this.claims.filter(claim =>
                claim.claim_reference?.toLowerCase().includes(term) ||
                claim.assigned_agent?.toLowerCase().includes(term) ||
                claim.status?.toLowerCase().includes(term)
            );
        }
        
        this.currentPage = 1;
        this.renderClaimsTable();
    }

    filterClaimsByStatus(status) {
        if (!status) {
            this.filteredClaims = [...this.claims];
        } else {
            this.filteredClaims = this.claims.filter(claim =>
                claim.status?.toLowerCase() === status.toLowerCase() ||
                claim.workflow_status?.toLowerCase() === status.toLowerCase()
            );
        }
        
        this.currentPage = 1;
        this.renderClaimsTable();
    }

    viewClaim(claimReference) {
        console.log('👁️ Viewing claim:', claimReference);
        
        // Switch to ticket view
        if (window.ticketManager) {
            window.ticketManager.loadTicket(claimReference);
            this.switchToPage('tickets');
        }
    }

    editClaim(claimReference) {
        console.log('✏️ Editing claim:', claimReference);
        
        // For now, just view the claim
        this.viewClaim(claimReference);
    }

    switchToPage(pageName) {
        // Hide all content pages
        document.querySelectorAll('.content-page').forEach(page => {
            page.classList.remove('active');
        });
        
        // Show target page
        const targetPage = document.getElementById(`${pageName}-content`);
        if (targetPage) {
            targetPage.classList.add('active');
        }
        
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const targetNav = document.querySelector(`[data-page="${pageName}"]`);
        if (targetNav) {
            targetNav.classList.add('active');
        }
    }

    showError(message) {
        console.error('Dashboard Error:', message);
        
        // You could implement a toast notification system here
        alert(message);
    }

    // Public methods
    refresh() {
        this.loadDashboardData();
    }

    getClaims() {
        return this.claims;
    }

    getFilteredClaims() {
        return this.filteredClaims;
    }
}

// Initialize dashboard manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardManager = new DashboardManager();
});
