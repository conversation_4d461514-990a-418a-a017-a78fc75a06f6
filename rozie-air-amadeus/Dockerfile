# Use an official Python image as the base
FROM python:3.12.5-slim

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file into the container
COPY airline_app/requirements.txt /app/requirements.txt

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY airline_app /app/airline_app

# Create directory for sample responses
RUN mkdir -p /app/airline_app/services/sample_responses

# Set environment variables
ENV PYTHONPATH=/app/airline_app
ENV AMADEUS_HOST=test

# Expose port 8000 (FastAPI default)
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "airline_app.main:app", "--host", "0.0.0.0", "--port", "8000"]