service: rozie-mock-company-salon-appointment


provider:
  name: aws
  runtime: python3.9
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:Scan
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
      Resource: "*"
  apiGateway:
    apiKeys:
      - ${self:service}-${self:provider.stage}-Api-Key

functions:
  salon-appointment:
    name: ${self:service}-${self:provider.stage}
    handler: index.lambda_handler
    description: function to manage the appointment
    environment:
      ENABLE_LOG: TRUE
      TABLE_NAME: ${self:service}-${self:provider.stage}
    events:
      - http:
          path: /appointment
          method: get
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /appointment
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      # - http:
      #     path: /customerProfile
      #     method: put
      #     private: true
      #     cors:
      #       origin: "*"
      #       headers:
      #         - Content-Type
      #         - X-Amz-Date
      #         - Authorization
      #         - X-Amz-Security-Token
      #         - X-Amz-User-Agent
      #         - x-api-key
      #         - version
      #         - sessionId
      #         - Accept
      #         - Referer
      #         - sec-ch-ua
      #         - sec-ch-ua-mobile
      #         - sec-ch-ua-platform
      #         - sentry-trace
      #         - User-Agent
      #         - x-ffp
      #         - x-uuid
      #         - x-uid
      #         - x-agent-id
      #         - x-lang
      # - http:
      #     path: /customerProfile
      #     method: delete
      #     private: true
      #     cors:
      #       origin: "*"
      #       headers:
      #         - Content-Type
      #         - X-Amz-Date
      #         - Authorization
      #         - X-Amz-Security-Token
      #         - X-Amz-User-Agent
      #         - x-api-key
      #         - version
      #         - sessionId
      #         - Accept
      #         - Referer
      #         - sec-ch-ua
      #         - sec-ch-ua-mobile
      #         - sec-ch-ua-platform
      #         - sentry-trace
      #         - User-Agent
      #         - x-ffp
      #         - x-uuid
      #         - x-uid
      #         - x-agent-id
      #         - x-lang
      
resources:
  extensions:
    ServerlessDeploymentBucket:
      Properties:
        PublicAccessBlockConfiguration:
          BlockPublicAcls: True
          BlockPublicPolicy: True
          IgnorePublicAcls: True
          RestrictPublicBuckets: True
  Resources:
    salonAppointmentTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: branch_id
            AttributeType: S
          - AttributeName: start_time_client_id
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: branch_id
          - KeyType: RANGE
            AttributeName: start_time_client_id
        TimeToLiveSpecification:
          AttributeName: expiresAt
          Enabled: true
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True