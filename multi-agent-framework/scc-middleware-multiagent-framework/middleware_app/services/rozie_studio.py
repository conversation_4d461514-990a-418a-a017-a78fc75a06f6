import time
from middleware_app.helpers.request_helper import post_request
from middleware_app.helpers.concept_helper import get_previous_interaction_concept
from middleware_app.concept_convertor import ExtractAgent


def rozie_studio_get_initiate_request(
    chat_id,
    customer_details,
    channel_id,
    channel_name,
    customer_message=None,
    skill_id=None,
    concept_name=None,
):
    """
    This function calls Rozie Studio API to get the response from LLM
    """
    user_id = {"id": chat_id, "id_type": "channel_user_id", "id_resource": "chat"}

    base_request_body = {
        "version": "1.0",
        "user_info": {"user_id": user_id},
        "channel": {
            "channel_id": channel_id,
            "channel_name": channel_name,
            "ui_info": {
                "should_consolidate_buttons": True,
            },
        },
        "incoming_events": [
            {
                "event_id": "event_0",
                "event_creator": {"event_creator_type": "user", "user_id": user_id},
            },
        ],
    }
    base_request_body["incoming_events"][0]["event_template"] = {
        "event_type": "skillconcept",
        "skill": {"skill_id": skill_id},
        "concepts": [
            {
                "is_valid": True,
                "is_checked": True,
                "concept_value": {
                    "entity_type": "PhoneNumber",
                    "entity_value": customer_details.get("phone_number", ""),
                },
            },
        ],
    }

    # if customer_message:
    #     base_request_body["incoming_events"][0]["event_template"] = {
    #         "event_type": "text",
    #         "text": customer_message,
    #     }
    # elif skill_id and concept_name:
    #     base_request_body["incoming_events"][0]["event_template"] = {
    #         "event_type": "skillconcept",
    #         "skill": {"skill_id": skill_id},
    #         "concepts": [
    #             {
    #                 "concept_value": {
    #                     "entity_type": concept_name,
    #                     "entity_value": customer_message,
    #                 }
    #             }
    #         ],
    #     }

    return base_request_body


def rozie_studio_text_request(
    chat_id,
    customer_details,
    channel_id,
    channel_name,
    customer_message,
):
    """
    This function calls Rozie Studio API to get the response from LLM
    """
    user_id = {"id": chat_id, "id_type": "channel_user_id", "id_resource": "chat"}

    base_request_body = {
        "version": "1.0",
        "user_info": {"user_id": user_id},
        "channel": {
            "channel_id": channel_id,
            "channel_name": channel_name,
            "ui_info": {
                "should_consolidate_buttons": True,
            },
        },
        "incoming_events": [
            {
                "event_id": "event_0",
                "event_creator": {"event_creator_type": "user", "user_id": user_id},
            },
        ],
    }

    if customer_message:
        base_request_body["incoming_events"][0]["event_template"] = {
            "event_type": "text",
            "text": customer_message,
        }

    return base_request_body


def rozie_studio_make_call(params, lang="en_us"):
    """
    This function calls Rozie Studio API to make a call
    """
    url = "https://api-manager-sandbox.rozie.ai/event-adapter/v1/adapters-event"
    headers = {
        "Content-Type": "application/json",
        "api-key": "5b5fadd08ddc4295abfa854244cbfbb2",
        "application-id": "application_0423b3ae-998b-4071-9add-593b0941f209",
        "language": lang,
        "rozie-correlation-id": str(int(time.time())),
    }

    status, response = post_request(url, headers, json_body=params)
    print("response", response)
    if status == "Success":
        rozieResponseData = response["data"]
        print("", "rozie_response_data", rozieResponseData, "")
        return rozieResponseData
    else:
        return None


def initiate_rozie_studio_skill(
    chat_id,
    channel_id,
    channel_name,
    customer_details,
    customer_message,
    skill_id,
    concept_name,
):
    """
    This function calls Rozie Studio API to get the response from LLM
    """
    param = rozie_studio_get_initiate_request(
        chat_id,
        customer_details,
        channel_id,
        channel_name,
        customer_message,
        skill_id,
        concept_name,
    )
    print(param)
    result = rozie_studio_make_call(param)
    if result:
        return result
    return {"error": "Failed to Initiate chat"}


def message_rozie_studio_skill(
    chat_id, channel_id, channel_name, customer_details, customer_message
):
    """
    This function calls Rozie Studio API to get the response from LLM
    """
    entity_configs = {
        "CustomerId": {
            "Key": "CustomerID",
            "Description": "7 digit Customer ID",
            "DataType": "Integer",
        },
        "OTP": {
            "Key": "OTP",
            "Description": "6 digit OTP",
            "DataType": "Integer",
        },
        "NoOfTravellers": {
            "Key": "NoOfTravellers",
            "Description": "count of travelers",
            "DataType": "Integer",
        },
    }
    previous_concept_id = get_previous_interaction_concept(
        chat_id
    )
    print("previous_concept_id", previous_concept_id)
    print("Before customer_message", customer_message)
    if entity_configs.get(previous_concept_id):

        customer_message = ExtractAgent(chatId=channel_id).extract(
            customer_message, entity_configs.get(previous_concept_id)
        )
        print("After customer_message", customer_message)
    param = rozie_studio_text_request(
        chat_id,
        customer_details,
        channel_id,
        channel_name,
        customer_message,
    )
    print(param)
    result = rozie_studio_make_call(param)
    if result:
        return result
    return {"error": "Failed to Initiate chat"}
