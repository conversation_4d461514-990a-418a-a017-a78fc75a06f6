import random

from airline_app.helpers.sendgrid_helper import send_email
from airline_app.services.customer import get_customer_details_by_id
from airline_app.common.constants import BCC_EMAIL

OTP_DATA = {}

def generate_otp(chat_id, customer_id, length=6):
    """Generate a numeric OTP of the given length."""
    otp = ''.join(str(random.randint(0, 9)) for _ in range(length))
    OTP_DATA[chat_id] = otp

    customer_details = get_customer_details_by_id(customer_id)

    body = f"""
    <html>
    <body>
        <h1>OTP Verification</h1>
        <p>Your One-Time Password (OTP) is: {otp}</p>
        <p>This OTP is valid for only a short period. Do not share it with anyone.</p>
        <p>If you did not request this, please ignore this email.</p>
    </body>
    </html>
    """
    bcc_list = BCC_EMAIL
    if customer_details["email"] in bcc_list:
        bcc_list.remove(customer_details["email"])
    email_config = {
        "reference_id": otp+customer_id,
        "from": "Nina<<EMAIL>>",
        "to": customer_details["email"],
        "bcc": bcc_list,
        "subject": "One-Time Password",
        "html_body": body,
        "send_separate": True
    }
    send_email(email_config)

    return otp

def validate_otp(chat_id, input_otp):
    """Validate if the entered OTP matches the generated one."""
    generated_otp = OTP_DATA[chat_id]
    is_valid = generated_otp == input_otp
    if is_valid:
        del OTP_DATA[chat_id]
    return is_valid
