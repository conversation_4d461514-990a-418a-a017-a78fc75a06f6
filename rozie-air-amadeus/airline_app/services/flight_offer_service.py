import json
import requests
import logging
from typing import Dict, Any, Optional
from airline_app.core import config
from airline_app.services.base_service import BaseApiService

logger = logging.getLogger(__name__)

class FlightOfferService(BaseApiService):
    def __init__(self):
        super().__init__()

    def get_flight_offers(self, origin: str, destination: str, departure_date: str,
                         return_date: Optional[str] = None, adults: int = 1,
                         children: int = 0, infants: int = 0, max_offers: int = 5) -> Dict[str, Any]:
        """
        Search for flight offers from Amadeus API.
        
        Args:
            origin: Origin location code (e.g., 'PAR')
            destination: Destination location code (e.g., 'ICN')
            departure_date: Departure date in YYYY-MM-DD format
            return_date: Optional return date in YYYY-MM-DD format
            adults: Number of adult passengers
            children: Number of child passengers
            infants: Number of infant passengers
            max_offers: Maximum number of offers to return
        
        Returns:
            Flight offers data in Amadeus API format
        """
        try:
            endpoint = f"{self.base_url}/v2/shopping/flight-offers"
            
            # Build query parameters
            query_params = {
                'originLocationCode': origin,
                'destinationLocationCode': destination,
                'departureDate': departure_date,
                'adults': adults,
                'max': max_offers
            }
            
            # Add optional parameters
            if return_date:
                query_params['returnDate'] = return_date
            if children > 0:
                query_params['children'] = children
            if infants > 0:
                query_params['infants'] = infants

            logger.info(f"Making request to Amadeus API: {endpoint}")
            logger.info(f"Query parameters: {json.dumps(query_params, indent=2)}")
            
            headers = self._get_headers()
            logger.info(f"Request headers: {json.dumps({k: v for k, v in headers.items() if k != 'Authorization'}, indent=2)}")

            response = requests.get(
                endpoint,
                headers=headers,
                params=query_params
            )
            
            if response.status_code == 401:
                logger.warning("Received 401 Unauthorized, refreshing token and retrying")
                self._handle_unauthorized()
                return self.get_flight_offers(
                    origin=origin,
                    destination=destination,
                    departure_date=departure_date,
                    return_date=return_date,
                    adults=adults,
                    children=children,
                    infants=infants,
                    max_offers=max_offers
                )
            
            if response.status_code != 200:
                logger.error(f"Amadeus API returned error: {response.status_code}")
                logger.error(f"Response content: {response.text}")
                response.raise_for_status()
            
            data = response.json()
            logger.info(f"Successfully retrieved flight offers: {json.dumps(data, indent=2)}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting flight offers from Amadeus: {str(e)}")
            if hasattr(e.response, 'text'):
                logger.error(f"Response content: {e.response.text}")
            raise

# Create singleton instance
flight_offer_service = FlightOfferService()

def get_flight_offer_service():
    """
    Get the singleton flight offer service instance.
    """
    return flight_offer_service 