#!/usr/bin/env python3
"""
Test script for Gemini Vision integration in Zurich OCR Engine

This script tests the Gemini Vision OCR functionality with fallback to Tesseract
for unsupported file formats.
"""

import asyncio
import os
import sys
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from zurich_ocr.core.config import settings
from zurich_ocr.core.gemini_vision_engine import gemini_vision_engine
from zurich_ocr.core.ocr_engine_router import ocr_engine_router

async def test_gemini_availability():
    """Test if Gemini Vision is properly configured and available"""
    print("🔍 Testing Gemini Vision availability...")

    if not settings.GOOGLE_PROJECT_ID:
        print("❌ GOOGLE_PROJECT_ID not configured")
        return False

    if not settings.GOOGLE_CREDENTIALS_JSON:
        print("❌ GOOGLE_CREDENTIALS_JSON not configured")
        return False

    if gemini_vision_engine.is_available():
        print(f"✅ Gemini Vision available with model: {settings.GEMINI_MODEL}")
        print(f"✅ Using Vertex AI with project: {settings.GOOGLE_PROJECT_ID}")
        return True
    else:
        print("❌ Gemini Vision not available")
        return False

async def test_supported_formats():
    """Test supported file format detection"""
    print("\n🔍 Testing supported file formats...")
    
    test_formats = [
        ("test.png", b'\x89PNG\r\n\x1a\n'),  # PNG signature
        ("test.jpg", b'\xff\xd8\xff'),       # JPEG signature
        ("test.webp", b'RIFF\x00\x00\x00\x00WEBP'),  # WEBP signature
        ("test.pdf", b'%PDF-1.4'),           # PDF signature (not supported)
    ]
    
    for filename, file_data in test_formats:
        is_supported = gemini_vision_engine.is_file_supported(file_data, filename)
        status = "✅ Supported" if is_supported else "❌ Not supported"
        print(f"  {filename}: {status}")

async def test_ocr_engine_router():
    """Test OCR engine router with Gemini"""
    print("\n🔍 Testing OCR engine router...")

    # Test available engines
    engines = ocr_engine_router.get_available_engines()
    print("Available engines:")
    for engine_name, engine_info in engines.items():
        status = "✅ Available" if engine_info["available"] else "❌ Not available"
        print(f"  {engine_name}: {status}")

        if engine_name.startswith("gemini") and engine_info["available"]:
            print(f"    Model: {engine_info['model']}")
            if 'model_info' in engine_info:
                model_info = engine_info['model_info']
                print(f"    Description: {model_info.get('description', 'N/A')}")
                print(f"    Speed: {model_info.get('speed', 'N/A')}")
                print(f"    Cost: {model_info.get('cost', 'N/A')}")
            print(f"    Supported formats: {len(engine_info['supported_formats'])}")
            print(f"    Fallback: {engine_info['fallback']}")

async def test_gemini_with_sample_image():
    """Test Gemini Vision with a simple test image (if available)"""
    print("\n🔍 Testing Gemini Vision with sample image...")

    # Create a simple test image data (PNG signature + minimal data)
    # This is just for testing the pipeline, not actual OCR
    test_image_data = (
        b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01'
        b'\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13'
        b'\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8'
        b'\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00IEND\xaeB`\x82'
    )

    # Test different Gemini models
    test_models = ["gemini", "gemini1.5", "gemini2.5pro", "gemini2.5flash"]

    for model in test_models:
        print(f"  Testing {model}...")
        try:
            router_result = await ocr_engine_router.process_with_engine(
                test_image_data, "test.png", model
            )

            if router_result.success:
                print(f"    ✅ Success: {len(router_result.text)} characters extracted")
                print(f"    Engine: {router_result.engine_used}")
                print(f"    Processor: {router_result.processor_used}")
            else:
                print(f"    ❌ Failed: {router_result.error_message}")

        except Exception as e:
            print(f"    ❌ Error during testing: {str(e)}")

async def test_fallback_mechanism():
    """Test fallback to Tesseract for unsupported formats"""
    print("\n🔍 Testing fallback mechanism...")
    
    # Create test data that looks like an unsupported format
    unsupported_data = b'%PDF-1.4\n%test data'  # PDF-like data
    
    try:
        result = await ocr_engine_router.process_with_engine(
            unsupported_data, "test.pdf", "gemini"
        )
        
        if result.success:
            print(f"    ✅ Fallback worked: {result.engine_used}")
            print(f"    Processor: {result.processor_used}")
        else:
            print(f"    ❌ Fallback failed: {result.error_message}")
            
    except Exception as e:
        print(f"    ❌ Error during fallback test: {str(e)}")

async def main():
    """Main test function"""
    print("🚀 Zurich OCR Engine - Gemini Vision Integration Test")
    print("=" * 60)
    
    # Test configuration
    print(f"Configuration:")
    print(f"  GOOGLE_PROJECT_ID: {'✅ Set' if settings.GOOGLE_PROJECT_ID else '❌ Not set'}")
    print(f"  GOOGLE_CREDENTIALS_JSON: {'✅ Set' if settings.GOOGLE_CREDENTIALS_JSON else '❌ Not set'}")
    print(f"  GOOGLE_LOCATION: {settings.GOOGLE_LOCATION}")
    print(f"  GEMINI_MODEL: {settings.GEMINI_MODEL}")
    print(f"  GEMINI_TEMPERATURE: {settings.GEMINI_TEMPERATURE}")
    print(f"  DEFAULT_OCR_ENGINE: {settings.DEFAULT_OCR_ENGINE}")
    
    # Run tests
    await test_gemini_availability()
    await test_supported_formats()
    await test_ocr_engine_router()
    
    # Only run OCR tests if Gemini is available
    if gemini_vision_engine.is_available():
        await test_gemini_with_sample_image()
        await test_fallback_mechanism()
    else:
        print("\n⚠️  Skipping OCR tests - Gemini Vision not available")
        print("   Please configure Google Cloud service account credentials:")
        print("   - GOOGLE_PROJECT_ID")
        print("   - GOOGLE_CREDENTIALS_JSON")

    print("\n✅ Test completed!")
    print("\nNext steps:")
    print("1. Configure Google Cloud service account with Vertex AI access")
    print("2. Set GOOGLE_PROJECT_ID and GOOGLE_CREDENTIALS_JSON environment variables")
    print("3. Test with real images using the API endpoints")
    print("4. Verify preprocessing and postprocessing work as expected")

if __name__ == "__main__":
    asyncio.run(main())
