from twilio.rest import Client

# Your Account SID and Auth Token from twilio.com/console
account_sid = '**********************************'
auth_token = '85d8d421691fc609f67e8ebe9f014be8'
client = Client(account_sid, auth_token)

# The Queue SID
queue_sid = 'QU37bcabc91f6f3ae022143b905af8220e'

# List calls in the queue
calls = client.queues(queue_sid).members.list()

for call in calls:
    print(call.call_sid)


# The Call SID you want to transfer
call_sid = "CA4891bbc642fd411521d8b6585fd0ab84"

# Update the call to redirect it
call = client.calls(call_sid).update(
    url='https://eso47k9ppj.execute-api.ca-central-1.amazonaws.com/dev/processInputFlow',
    method='POST'
)

print(call.to)
