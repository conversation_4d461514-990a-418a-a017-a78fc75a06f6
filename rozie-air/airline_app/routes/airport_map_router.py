import traceback
from typing import Dict, List, Literal
from fastapi import APIRouter, HTTPException, Depends
from airline_app.adapters.auth_adapter import get_api_key

from airline_app.services.airport_map import get_all_airport_map, get_airport_map_details

airport_map_router = APIRouter(prefix="/airport_map")

@airport_map_router.get("/get_all_airport_map", dependencies=[Depends(get_api_key)])
async def get_all_airport_map_route():
    try:
        airport_map = get_all_airport_map()
        return airport_map
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})
    
@airport_map_router.post("/get_airport_map_details", dependencies=[Depends(get_api_key)])
async def get_airport_map_details_route(data: Dict):
    """
    Retrieve airport map data based on the provided input type.

    Args:
        data (dict): A dictionary containing the following keys:
            - airport_map_input (List[str]): List of city names or airport codes.
            - input_type (str): Specifies if the input is 'city' or 'code'.

    Returns:
        List of matching airport map data.
    """
    try:
        airport_map_input = data.get("airport_map_input", []) # must be an list
        input_type = data.get("input_type", "")
        airport_maps = get_airport_map_details(airport_map_input, input_type)
        return airport_maps
    except Exception as e:
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})