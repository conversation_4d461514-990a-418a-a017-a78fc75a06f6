"""Contains utility functions"""

def parse_flight_details(flight_data):
    """
    Parses and cleans incoming flight data to include only necessary fields.

    Args:
        flight_data (dict): The incoming flight data.

    Returns:
        dict: The cleaned flight data with only required fields.
    """

    parsed_details = {
        "flight_id": flight_data.get("flight_id"),
        "airline": flight_data.get("airline"),
        "origin": flight_data.get("origin"),
        "destination": flight_data.get("destination"),
        "departure_time": flight_data.get("departure_time"),
        "arrival_time": flight_data.get("arrival_time"),
        "duration": flight_data.get("duration"),
        "price": {
            "currency": flight_data.get("price", {}).get("currency"),
            "amount": flight_data.get("price", {}).get("amount")
        },
        "available_seats": flight_data.get("available_seats"),
        "aircraft": flight_data.get("aircraft"),
        "cabin_classes": flight_data.get("cabin_classes", {}),
        "delayed": flight_data.get("delayed", False),
        "original_departure_time": flight_data.get("original_departure_time"),
        "original_arrival_time": flight_data.get("original_arrival_time"),
        "delay_reason": flight_data.get("delay_reason")
    }

    return parsed_details
