"""This module contains helper function for SendGrid"""

from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import (
    Mail,
    Attachment,
    FileContent,
    FileName,
    FileType,
    Disposition,
    Bcc,
    Personalization
)


def create_mail(
    from_email, to_emails, subject, html_content, send_separate=False
) -> Mail:
    """
    This function creates a base Mail object,
    used for adding attachments, cc, bcc and sending email
    """
    mail = Mail(
        from_email=from_email,
        to_emails=to_emails,
        subject=subject,
        html_content=html_content,
        is_multiple=send_separate,
    )
    return mail


def send_mail(mail, sendgird_api_key):
    """
    Send the email to recipients
    """
    sg = SendGridAPIClient(sendgird_api_key)
    response = sg.send(mail)
    return response


def create_attachments(attachments_config):
    """
    Creates objects of Attachments to be appended to mail
    """
    attachments_list = []
    for attachment_config in attachments_config:
        attachments_list.append(
            Attachment(
                FileContent(attachment_config.get("file_content")),
                FileName(attachment_config.get("file_name")),
                FileType(attachment_config.get("file_type")),
                Disposition("attachment"),
            )
        )
    return attachments_list


def add_attachments(mail, attachments_list) -> Mail:
    """Adds attachments to the mail"""
    mail.add_attachment(attachments_list)
    return mail

def create_bcc(bcc_email):
    """Creates bcc object"""
    bcc = Bcc(bcc_email)
    print("BCC :- ", bcc)
    return bcc

def add_bcc(mail, bcc) -> Mail:
    """Adds bcc to the mail"""
    mail.add_bcc(bcc)
    return mail