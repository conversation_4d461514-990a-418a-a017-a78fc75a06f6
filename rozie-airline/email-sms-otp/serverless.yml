service: rozie-mock-airline-email-sms-otp

custom:
  dev:
    LayerBucket: scc-layers
    
provider:
  name: aws
  runtime: python3.9
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:Scan
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - ses:*
        - mobiletargeting:SendMessages
      Resource: "*"
  apiGateway:
    apiKeys:
      - ${self:service}-${self:provider.stage}-Api-Key  

functions:
  email-sms-otp-handler:
    name: ${self:service}-${self:provider.stage}-handler
    handler: index.lambda_handler
    description: function to send and validate otp and send out sms/emails
    layers:
      - !Ref <PERSON>er
      - !Ref Twi<PERSON>bdaLayer
    environment:
      ENABLE_LOG: "True"
      OTP_TABLE: ${self:service}-${self:provider.stage}-table
      CONFIG_TABLE : rozie-mock-global-config-table-${self:provider.stage}
      EMAIL_API_KEY: "*********************************************************************"
      TWILIO_ACC_SID : "**********************************"
      TWILIO_AUTH_TOKEN: "85d8d421691fc609f67e8ebe9f014be8"
      TWILIO_PHONE_NUMBER : "****** 600 7714"
      FROM_EMAIL: "Rozie Helpdesk<<EMAIL>>"

    events:
      - http:
          path: /sendOtp
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /validateOtp
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /sendEmail
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /sendSMS
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang

resources:
  extensions:
    ServerlessDeploymentBucket:
      Properties:
        PublicAccessBlockConfiguration:
          BlockPublicAcls: True
          BlockPublicPolicy: True
          IgnorePublicAcls: True
          RestrictPublicBuckets: True
  
  Resources:
    OtpTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: id
        TimeToLiveSpecification:
          AttributeName: expiresAt
          Enabled: true
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-${self:provider.stage}-table
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True

    ConfigTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: organizationId
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: organizationId
        BillingMode: PAY_PER_REQUEST
        TableName: rozie-mock-global-config-table-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    
    BotoLambdaLayer:
      Type: AWS::Lambda::LayerVersion
      Properties:
        CompatibleRuntimes:
          - python3.8
          - python3.9
          - python3.10
        Content:
          S3Bucket: ${self:custom.${self:provider.stage}.LayerBucket}
          S3Key: sendgrid_layer.zip
        LayerName: sendgrid-python-layer-${self:provider.stage}
      
    TwilioLambdaLayer:
      Type: AWS::Lambda::LayerVersion
      Properties:
        CompatibleRuntimes:
          - python3.8
          - python3.9
          - python3.10
        Content:
          S3Bucket: ${self:custom.${self:provider.stage}.LayerBucket}
          S3Key: Twilio-python.zip
        LayerName: twilio-python-layer-${self:provider.stage}