service: rozie-mock-company-connect-to-agent
frameworkVersion: '3'
custom:
  dev:
    CONNECT_INSTANCE_ID: 710e5888-0655-4bf1-bf5c-23bacfd46d22
    SOURCE_PHONE_NUMBER: "+18557528270"
    OUTBOUND_CONTACT_FLOW_ID: f537ba5f-dac3-43ce-8eec-61f264f5d154
  demo:
    CONNECT_INSTANCE_ID: bd7c05bd-ac34-4b14-9c11-acdb94257e87
    SOURCE_PHONE_NUMBER: "+18557528606"
    OUTBOUND_CONTACT_FLOW_ID: 038d54b9-42b5-4811-98a2-13807dbc794e
provider:
  name: aws
  runtime: nodejs18.x
  endpointType: regional
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 60
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - mobiletargeting:PhoneNumberValidate
        - connect:StartOutboundVoiceContact
        - dynamodb:GetItem
        - dynamodb:PutItem
      Resource: "*"
  apiGateway:
    apiKeys:
      - ${self:service}-${self:provider.stage}-Api-Key

useDotenv: true

functions:
  clickToConnect:
    handler: index.handler
    environment:
      CONNECT_INSTANCE_ID: ${self:custom.${self:provider.stage}.CONNECT_INSTANCE_ID}
      SOURCE_PHONE_NUMBER: ${self:custom.${self:provider.stage}.SOURCE_PHONE_NUMBER}
      OUTBOUND_CONTACT_FLOW_ID: ${self:custom.${self:provider.stage}.OUTBOUND_CONTACT_FLOW_ID}
      REGION: ${aws:region}
      TABLE_NAME: rozie-mock-contact-attributes-${self:provider.stage}
    events:
      - http:
          path: /click-to-connect
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - sessionid
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent              