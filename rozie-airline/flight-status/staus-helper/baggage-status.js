const { queryDynamodbTableItemGSI , updateDynamoTableDataWithParams } = require("../helpers/dynamo-helper.js")

/**
 * @function createPNR
 * @description This function creates a PNR entry in a DynamoDB table based on the provided data.
 * 
 * @param {Object} body - The request body containing customer and booking details.
 * @param {string} body.customer_id - The customer ID for the PNR.
 * @param {string} body.flight_schedule_id - The flight schedule ID.
 * @param {number} body.payment_amount - The payment amount.
 * @param {Array} body.travelers_details - The list of travelers details, including their names and seat numbers.
 * 
 * @returns {Promise<Object>} - The response object indicating the result of the PNR creation.
 * 
 * @throws {Error} - Throws an error if there is an issue during the PNR creation process.
 */
const baggageStatus = async (scheduleId,status) => {
	try {		
		// Process each flight to add the status based on timestamps
		console.log("Flights with baggageItems status:", scheduleId , status )
		let baggageItems = null
		// default status
		if (scheduleId) {
			const params = {
				TableName: process.env.BAGGAGE_TABLE_NAME,
				IndexName: "flightShceduleIndex",
				KeyConditionExpression: "flight_schedule_id = :flight_schedule_id",
				ExpressionAttributeValues: {
					":flight_schedule_id": scheduleId
				},
				ScanIndexForward: false
			}
			baggageItems = await queryDynamodbTableItemGSI(params)
			console.log("Flights with updated status1:", baggageItems)
		}
		// If the status has changed, update it in DynamoDB
		if(baggageItems){
			const baggageItemsWithStatus = await Promise.all(baggageItems.map(async flight => {
				if (flight.baggage_status !== status) {
					const updateParams = {
						TableName: process.env.BAGGAGE_TABLE_NAME,
						Key: { baggage_id: flight.baggage_id }, 
						UpdateExpression: "set baggage_status = :baggage_status",
						ExpressionAttributeValues: {
							":baggage_status": status
						},
						ReturnValues: "UPDATED_NEW"
					}
                
					console.log("baggageItems", updateParams)
					await updateDynamoTableDataWithParams(updateParams)
				}
			}))
			console.log("Flights with updated status:", baggageItemsWithStatus)
		}   
	} catch (error) {
		console.error("Error in baggage_status:", error)
	}
}
module.exports = {
	baggageStatus
}