service: scc-middleware-webhook

custom:
  dev:
    LayerBucket: scc-layers
  demo:
    LayerBucket: scc-layers

provider:
  name: aws
  runtime: python3.10
  stage: ${opt:stage, "dev1"}
  region: ${opt:region, "ca-central-1"}
  endpointType: regional
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:*
      Resource: "*"
  # apiGateway:
  #   apiKeys:
  #     - ${self:service}-${self:provider.stage}-Api-Key

functions:
  agent-guide:
    name: ${self:service}-${self:provider.stage}
    handler: index.lambda_handler
    description: function to detect entity of fucus
    layers:
      - !Ref TwilioLambdaLayer
    environment:
      LOG_LEVEL: INFO
      ENABLE_LOGS: TRUE
      SALON_RESULT_PROXY_TABLE: llm-salon-results-proxy-${self:provider.stage}
      TWILIO_AUTH_TOKEN: 85d8d421691fc609f67e8ebe9f014be8
      TWILIO_CONTACT_ATTRIBUTE_TABLE: twilio-contact-attribute-${self:provider.stage}
      ROZIE_MOCK_SALON_USER_PROFILE_URL: https://wth8egh9q6.execute-api.ca-central-1.amazonaws.com/dev/customerProfile
      ROZIE_MOCK_SALON_USER_PROFILE_KEY: 1Tram2rKaT8qcliH4BhVP9auRCLu4Bpx5ssGMDC8
      WAITING_AUDIO_ASSET: "https://rosewood-lapwing-6582.twil.io/assets/DreamMemory_Trim_Audio.wav"
      AUTOGEN_PROCESS_GATHER_HOOK: https://ru3va8kj6e.execute-api.ca-central-1.amazonaws.com/dev/webhooks/processInputFlow
      AUTOGEN_GATHER_HOOK: https://ru3va8kj6e.execute-api.ca-central-1.amazonaws.com/dev/webhooks/gatherInputFlow
      AUTOGEN_INITIATE_HOOK: https://ru3va8kj6e.execute-api.ca-central-1.amazonaws.com/dev/webhooks/entryFlow
      AUTOGEN_ERROR_HANDLING_HOOK: https://ru3va8kj6e.execute-api.ca-central-1.amazonaws.com/dev/webhooks/errorFlow
      AUTOGEN_WELCOME_HOOK: https://ru3va8kj6e.execute-api.ca-central-1.amazonaws.com/dev/webhooks/errorFlow
      MIDDLEWARE_INITIATE_ULR: http://middleware.dev-scc-demo.rozie.ai/webchat/initiate
      MIDDLEWARE_SEND_MESSAGE_ULR: http://middleware.dev-scc-demo.rozie.ai/webchat/send_message
      AUTOGEN_ACCESS_TOKEN: Test@123
      TWILIO_TEST_TABLE: twilio-test-${self:provider.stage}
      TEST_TABLE_ID: "1"
      CARD_CHECKER_TABLE: twilio-card-checker-${self:provider.stage}
      CONNECT_CONTACT_ATTRIBUTE: rozie-mock-contact-attributes-${self:provider.stage}
      SET_CONNECT_ATTRIBUTE: https://aqrd8vmtye.execute-api.ca-central-1.amazonaws.com/demo/contactAttributes
      SET_CONNECT_ATTRIBUTE_KEY: T40IDIkyLg9I8uT9N2cT2UPGRVyHn4v9z2OatLRa
      CONNECT_TRANSFER_NUMBER: "+18557528606"
      MIDDLEWARE_PROCESS_GATHER_HOOK: https://ru3va8kj6e.execute-api.ca-central-1.amazonaws.com/dev/webhooks_v2/processInputFlow
      MIDDLEWARE_GATHER_HOOK: https://ru3va8kj6e.execute-api.ca-central-1.amazonaws.com/dev/webhooks_v2/gatherInputFlow
      MIDDLEWARE_INITIATE_HOOK: https://ru3va8kj6e.execute-api.ca-central-1.amazonaws.com/dev/webhooks_v2/entryFlow
      MIDDLEWARE_PLAY_PROMPT: https://ru3va8kj6e.execute-api.ca-central-1.amazonaws.com/dev/webhooks_v2/playPromptFlow
      
    events:
      - http:
          path: /webhooks/entryFlow
          method: post
          # private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /webhooks/processInputFlow
          method: post
          # private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /webhooks/gatherInputFlow
          method: post
          # private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /webhooks/errorFlow
          method: post
          # private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /webhooks/greetingFlow
          method: post
          # private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang

      - http:
          path: /webhooks_v2/entryFlow
          method: post
          # private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /webhooks_v2/processInputFlow
          method: post
          # private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /webhooks_v2/gatherInputFlow
          method: post
          # private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /webhooks_v2/playPromptFlow
          method: post
          # private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /webhooks_v2/greetingFlow
          method: post
          # private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang

resources:
  extensions:
    ServerlessDeploymentBucket:
      Properties:
        PublicAccessBlockConfiguration:
          BlockPublicAcls: True
          BlockPublicPolicy: True
          IgnorePublicAcls: True
          RestrictPublicBuckets: True
  Resources:
    TwilioLambdaLayer:
      Type: AWS::Lambda::LayerVersion
      Properties:
        CompatibleRuntimes:
          - python3.8
          - python3.9
          - python3.10
        Content:
          S3Bucket: ${self:custom.${self:provider.stage}.LayerBucket}
          S3Key: Twilio-python.zip
        LayerName: twilio-python-layer-${self:provider.stage}
    TwilioCardCheckerTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: phone_number
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: phone_number
        BillingMode: PAY_PER_REQUEST
        TableName: twilio-card-checker-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True