/* eslint-disable no-case-declarations */
const { retrieveDynamoEntry, queryDynamodbTableItemGSI } = require("../helpers/dynamo-helper")
const { ApiResp } = require("../helpers/api-helper")


/**
 * Function to convert either an airline code to airline name or vice versa.
 * 
 * This version uses a switch case to handle different types of queries (by airportCode or airportName).
 * 
 * @param {Object} body - The request body containing either `airportCode` or `airportName`.
 * @returns {Promise<Object>} - A promise that resolves to the API response with the result of the query.
 */
const convertAirlineCode = async (body) => {

	const { airportCode, airportName } = body
	try {
		switch (true) {
		case Boolean(airportCode):
			console.log(`Querying airportName for airportCode: ${airportCode}`)
			const codeResult = await retrieveDynamoEntry(process.env.AIRPORT_CODE_TABLE, "airportCode", airportCode)

			if (codeResult) {
				return ApiResp({
					status: "Success",
					message: "Airport name found for the provided airport code.",
					response: codeResult
				}, 200)
			} else {
				return ApiResp({
					status: "Error",
					message: "No matching airport found for the provided airport code."
				}, 404)
			}

		case Boolean(airportName):
			console.log(`Querying airportCode for airportName: ${airportName}`)

			const gsiParams = {
				TableName: process.env.AIRPORT_CODE_TABLE,
				IndexName: "AirportNameIndex",
				KeyConditionExpression: "airportName = :airportName",
				ExpressionAttributeValues: {
					":airportName": airportName
				}
			}

			const nameResult = await queryDynamodbTableItemGSI(gsiParams)

			if (nameResult.length > 0) {
				return ApiResp({
					status: "Success",
					message: "Airport code found for the provided airport name.",
					response: nameResult[0] 
				}, 200)
			} else {
				return ApiResp({
					status: "Error",
					message: "No matching airport found for the provided airport name."
				}, 404)
			}

		default:
			return ApiResp({
				status: "Error",
				message: "Invalid request. Either airportCode or airportName must be provided."
			}, 400)
		}
	} catch (error) {
		console.error("Error during conversion:", error)
		return ApiResp({
			status: "Error",
			message: "Internal Server Error while converting airline code."
		}, 500)
	}
}

module.exports = {
	convertAirlineCode
}
