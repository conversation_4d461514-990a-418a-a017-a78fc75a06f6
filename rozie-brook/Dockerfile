# Use an official Python image as the base
FROM python:3.12.5-slim

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file into the container
COPY requirements.txt /app/requirements.txt

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code
COPY ./phorest_mock_app /app/phorest_mock_app

# Expose port 80
EXPOSE 80
CMD ["uvicorn", "phorest_mock_app.main:app", "--host", "0.0.0.0", "--port", "80"]