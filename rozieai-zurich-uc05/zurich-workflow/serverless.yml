service: ${self:custom.serviceName}

provider:
  name: aws
  runtime: nodejs14.x
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - ec2:RunInstances
        - ec2:DescribeInstances
        - ec2:TerminateInstances
        - ec2:StartInstances
        - ec2:StopInstances
        - ecs:CreateCluster
        - ecs:DescribeClusters
        - ecs:CreateService
        - ecs:UpdateService
        - ecs:DescribeServices
        - ecs:RunTask
        - ecs:StopTask
        - ecs:DeleteCluster
        - ecs:DeleteService
        - secretsmanager:GetSecretValue
        - cloudformation:CreateStack
        - cloudformation:UpdateStack
        - cloudformation:DescribeStacks
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
        - ec2:CreateFlowLogs
        - ec2:DescribeFlowLogs
        - ec2:*
      Resource: "*"

custom:
  serviceName: zurich-workflow
  # Backend API Configuration
  backendEcrRepoUri: ${env:BACKEND_ECR_REPO_URI}
  backendDomainName: ${env:backend-domain-name}

  # Dashboard Configuration
  dashboardEcrRepoUri: ${env:DASHBOARD_ECR_REPO_URI}
  dashboardDomainName: ${env:dashboard-domain-name}

  # Frontend Configuration
  frontendS3Bucket: ${env:frontend-s3-bucket}
  frontendDomainName: ${env:frontend-domain-name}

  # Shared Configuration
  awsRegion: ${env:AWS_REGION}
  publicHostedZoneID: ${env:hosted-zone-id}
  acmCertificateArn: ${env:acm-cert-arn}

resources:
  Resources:
    # =============================================================================
    # SHARED INFRASTRUCTURE
    # =============================================================================

    ECSCluster:
      Type: AWS::ECS::Cluster
      Properties:
        ClusterName: ${self:service}-${self:provider.stage}-cluster
        CapacityProviders:
          - FARGATE
        DefaultCapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1

    VPC:
      Type: AWS::EC2::VPC
      Properties:
        CidrBlock: 10.0.0.0/16
        EnableDnsHostnames: true
        EnableDnsSupport: true
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-vpc

    InternetGateway:
      Type: AWS::EC2::InternetGateway
      Properties:
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-igw

    InternetGatewayAttachment:
      Type: AWS::EC2::VPCGatewayAttachment
      Properties:
        InternetGatewayId: !Ref InternetGateway
        VpcId: !Ref VPC

    AZ1PublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref VPC
        AvailabilityZone: !Select [0, !GetAZs '']
        CidrBlock: ********/24
        MapPublicIpOnLaunch: true
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-public-subnet-az1

    AZ2PublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref VPC
        AvailabilityZone: !Select [1, !GetAZs '']
        CidrBlock: ********/24
        MapPublicIpOnLaunch: true
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-public-subnet-az2

    AZ1PrivateSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref VPC
        AvailabilityZone: !Select [0, !GetAZs '']
        CidrBlock: *********/24
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-private-subnet-az1

    AZ2PrivateSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref VPC
        AvailabilityZone: !Select [1, !GetAZs '']
        CidrBlock: *********/24
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-private-subnet-az2

    NatGateway1EIP:
      Type: AWS::EC2::EIP
      DependsOn: InternetGatewayAttachment
      Properties:
        Domain: vpc

    NatGateway2EIP:
      Type: AWS::EC2::EIP
      DependsOn: InternetGatewayAttachment
      Properties:
        Domain: vpc

    NatGateway1:
      Type: AWS::EC2::NatGateway
      Properties:
        AllocationId: !GetAtt NatGateway1EIP.AllocationId
        SubnetId: !Ref AZ1PublicSubnet1

    NatGateway2:
      Type: AWS::EC2::NatGateway
      Properties:
        AllocationId: !GetAtt NatGateway2EIP.AllocationId
        SubnetId: !Ref AZ2PublicSubnet1

    PublicRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref VPC
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-public-routes

    DefaultPublicRoute:
      Type: AWS::EC2::Route
      DependsOn: InternetGatewayAttachment
      Properties:
        RouteTableId: !Ref PublicRouteTable
        DestinationCidrBlock: 0.0.0.0/0
        GatewayId: !Ref InternetGateway

    AZ1PublicSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref PublicRouteTable
        SubnetId: !Ref AZ1PublicSubnet1

    AZ2PublicSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref PublicRouteTable
        SubnetId: !Ref AZ2PublicSubnet1

    PrivateRouteTable1:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref VPC
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-private-routes-az1

    DefaultPrivateRoute1:
      Type: AWS::EC2::Route
      Properties:
        RouteTableId: !Ref PrivateRouteTable1
        DestinationCidrBlock: 0.0.0.0/0
        NatGatewayId: !Ref NatGateway1

    AZ1PrivateSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref PrivateRouteTable1
        SubnetId: !Ref AZ1PrivateSubnet1

    PrivateRouteTable2:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref VPC
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-private-routes-az2

    DefaultPrivateRoute2:
      Type: AWS::EC2::Route
      Properties:
        RouteTableId: !Ref PrivateRouteTable2
        DestinationCidrBlock: 0.0.0.0/0
        NatGatewayId: !Ref NatGateway2

    AZ2PrivateSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref PrivateRouteTable2
        SubnetId: !Ref AZ2PrivateSubnet1

    InstanceSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupName: ${self:service}-${self:provider.stage}-sg
        GroupDescription: Security group for ${self:service}-${self:provider.stage}
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 8000
            ToPort: 8000
            SourceSecurityGroupId: !Ref LoadBalancerSecurityGroup
          - IpProtocol: tcp
            FromPort: 80
            ToPort: 80
            SourceSecurityGroupId: !Ref LoadBalancerSecurityGroup
          - IpProtocol: tcp
            FromPort: 443
            ToPort: 443
            SourceSecurityGroupId: !Ref LoadBalancerSecurityGroup
        SecurityGroupEgress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-sg

    LoadBalancerSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupName: ${self:service}-${self:provider.stage}-alb-sg
        GroupDescription: Security group for ${self:service}-${self:provider.stage} ALB
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 80
            ToPort: 80
            CidrIp: 0.0.0.0/0
          - IpProtocol: tcp
            FromPort: 443
            ToPort: 443
            CidrIp: 0.0.0.0/0
        SecurityGroupEgress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-alb-sg

    CloudWatchLogsGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: ${self:service}-${self:provider.stage}
        RetentionInDays: 14

    ApplicationLoadBalancer:
      Type: AWS::ElasticLoadBalancingV2::LoadBalancer
      Properties:
        Name: ${self:service}-${self:provider.stage}-alb
        Scheme: internet-facing
        Type: application
        Subnets:
          - !Ref AZ1PublicSubnet1
          - !Ref AZ2PublicSubnet1
        SecurityGroups:
          - !Ref LoadBalancerSecurityGroup
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-alb

    # =============================================================================
    # TARGET GROUPS
    # =============================================================================

    BackendTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      Properties:
        Name: ${self:service}-backend-${self:provider.stage}-tg
        Port: 8000
        Protocol: HTTP
        VpcId: !Ref VPC
        TargetType: ip
        HealthCheckIntervalSeconds: 30
        HealthCheckPath: /health
        HealthCheckProtocol: HTTP
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 2
        Tags:
          - Key: Name
            Value: ${self:service}-backend-${self:provider.stage}-tg

    DashboardTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      Properties:
        Name: ${self:service}-dashboard-${self:provider.stage}-tg
        Port: 2000
        Protocol: HTTP
        VpcId: !Ref VPC
        TargetType: ip
        HealthCheckIntervalSeconds: 30
        HealthCheckPath: /health
        HealthCheckProtocol: HTTP
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 2
        Tags:
          - Key: Name
            Value: ${self:service}-dashboard-${self:provider.stage}-tg

    # =============================================================================
    # LOAD BALANCER LISTENERS WITH PATH-BASED ROUTING
    # =============================================================================

    LoadBalancerListener:
      Type: AWS::ElasticLoadBalancingV2::Listener
      Properties:
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref BackendTargetGroup
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Port: 80
        Protocol: HTTP

    LoadBalancerListenerHTTPS:
      Type: AWS::ElasticLoadBalancingV2::Listener
      Properties:
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref BackendTargetGroup
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Port: 443
        Protocol: HTTPS
        Certificates:
          - CertificateArn: ${self:custom.acmCertificateArn}

    # Dashboard Listener Rule (for /dashboard/* paths)
    DashboardListenerRule:
      Type: AWS::ElasticLoadBalancingV2::ListenerRule
      Properties:
        Actions:
          - Type: forward
            TargetGroupArn: !Ref DashboardTargetGroup
        Conditions:
          - Field: path-pattern
            Values:
              - "/dashboard*"
        ListenerArn: !Ref LoadBalancerListenerHTTPS
        Priority: 100

    DashboardListenerRuleHTTP:
      Type: AWS::ElasticLoadBalancingV2::ListenerRule
      Properties:
        Actions:
          - Type: forward
            TargetGroupArn: !Ref DashboardTargetGroup
        Conditions:
          - Field: path-pattern
            Values:
              - "/dashboard*"
        ListenerArn: !Ref LoadBalancerListener
        Priority: 100

    ECRECSExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service:
                  - ecs-tasks.amazonaws.com
              Action:
                - sts:AssumeRole
        ManagedPolicyArns:
          - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
          - arn:aws:iam::aws:policy/CloudWatchLogsFullAccess
        Policies:
          - PolicyName: ECRAccess
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - ecr:GetAuthorizationToken
                    - ecr:BatchCheckLayerAvailability
                    - ecr:GetDownloadUrlForLayer
                    - ecr:BatchGetImage
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - secretsmanager:GetSecretValue
                  Resource: "*"

    # =============================================================================
    # BACKEND API TASK DEFINITION
    # =============================================================================

    BackendTaskDefinition:
      Type: AWS::ECS::TaskDefinition
      DependsOn:
        - CloudWatchLogsGroup
      Properties:
        Family: ${self:service}-backend-${self:provider.stage}
        NetworkMode: awsvpc
        RuntimePlatform:
          CpuArchitecture: X86_64
          OperatingSystemFamily: LINUX
        RequiresCompatibilities:
          - FARGATE
        ExecutionRoleArn: !GetAtt ECRECSExecutionRole.Arn
        TaskRoleArn: !GetAtt ECRECSExecutionRole.Arn
        Cpu: 1024
        Memory: 2048
        ContainerDefinitions:
          - Name: ${self:service}-backend-${self:provider.stage}
            Image: ${self:custom.backendEcrRepoUri}
            Memory: 2048
            Cpu: 1024
            Essential: true
            PortMappings:
              - ContainerPort: 8000
                Protocol: "tcp"
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "backend"
            Environment:
              - Name: PORT
                Value: "8000"
              - Name: OPENAI_API_KEY
                Value: ${env:OPENAI_API_KEY}
              - Name: SUPABASE_URL
                Value: ${env:SUPABASE_URL}
              - Name: SUPABASE_ANON_KEY
                Value: ${env:SUPABASE_ANON_KEY}
              - Name: BAML_ENVIRONMENT
                Value: ${env:BAML_ENVIRONMENT, "development"}
              - Name: FASTAPI_ENV
                Value: ${env:FASTAPI_ENV, "development"}
              - Name: LOG_LEVEL
                Value: ${env:LOG_LEVEL, "INFO"}
              - Name: SUPPORT_SUBDOMAIN
                Value: ${env:SUPPORT_SUBDOMAIN, "d3v-rozieai5417"}
              - Name: SUPPORT_EMAIL
                Value: ${env:SUPPORT_EMAIL, "<EMAIL>"}
              - Name: SUPPORT_TOKEN
                Value: ${env:SUPPORT_TOKEN}

    # =============================================================================
    # DASHBOARD TASK DEFINITION
    # =============================================================================

    DashboardTaskDefinition:
      Type: AWS::ECS::TaskDefinition
      DependsOn:
        - CloudWatchLogsGroup
      Properties:
        Family: ${self:service}-dashboard-${self:provider.stage}
        NetworkMode: awsvpc
        RuntimePlatform:
          CpuArchitecture: X86_64
          OperatingSystemFamily: LINUX
        RequiresCompatibilities:
          - FARGATE
        ExecutionRoleArn: !GetAtt ECRECSExecutionRole.Arn
        TaskRoleArn: !GetAtt ECRECSExecutionRole.Arn
        Cpu: 512
        Memory: 1024
        ContainerDefinitions:
          - Name: ${self:service}-dashboard-${self:provider.stage}
            Image: ${self:custom.dashboardEcrRepoUri}
            Memory: 1024
            Cpu: 512
            Essential: true
            PortMappings:
              - ContainerPort: 2000
                Protocol: "tcp"
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "dashboard"
            Environment:
              - Name: PORT
                Value: "2000"
              - Name: NODE_ENV
                Value: ${env:NODE_ENV, "production"}
              - Name: SUPABASE_URL
                Value: ${env:SUPABASE_URL}
              - Name: SUPABASE_ANON_KEY
                Value: ${env:SUPABASE_ANON_KEY}
              - Name: SUPPORT_SUBDOMAIN
                Value: ${env:SUPPORT_SUBDOMAIN, "d3v-rozieai5417"}
              - Name: SUPPORT_EMAIL
                Value: ${env:SUPPORT_EMAIL, "<EMAIL>"}
              - Name: SUPPORT_TOKEN
                Value: ${env:SUPPORT_TOKEN}

    # =============================================================================
    # ECS SERVICES
    # =============================================================================

    BackendECSService:
      Type: AWS::ECS::Service
      DependsOn:
        - BackendTaskDefinition
        - BackendTargetGroup
        - ApplicationLoadBalancer
        - LoadBalancerListener
      Properties:
        Cluster: !Ref ECSCluster
        DesiredCount: 1
        TaskDefinition: !Ref BackendTaskDefinition
        ServiceName: ${self:service}-backend-${self:provider.stage}-service
        PlatformVersion: LATEST
        EnableECSManagedTags: true
        DeploymentConfiguration:
          MaximumPercent: 200
          MinimumHealthyPercent: 70
          DeploymentCircuitBreaker:
            Enable: false
            Rollback: false
        NetworkConfiguration:
          AwsvpcConfiguration:
            SecurityGroups:
              - !Ref InstanceSecurityGroup
            Subnets:
              - !Ref AZ1PrivateSubnet1
              - !Ref AZ2PrivateSubnet1
        CapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1
        LaunchType: FARGATE
        LoadBalancers:
          - ContainerName: ${self:service}-backend-${self:provider.stage}
            ContainerPort: 8000
            TargetGroupArn: !Ref BackendTargetGroup

    DashboardECSService:
      Type: AWS::ECS::Service
      DependsOn:
        - DashboardTaskDefinition
        - DashboardTargetGroup
        - ApplicationLoadBalancer
        - LoadBalancerListener
      Properties:
        Cluster: !Ref ECSCluster
        DesiredCount: 1
        TaskDefinition: !Ref DashboardTaskDefinition
        ServiceName: ${self:service}-dashboard-${self:provider.stage}-service
        PlatformVersion: LATEST
        EnableECSManagedTags: true
        DeploymentConfiguration:
          MaximumPercent: 200
          MinimumHealthyPercent: 70
          DeploymentCircuitBreaker:
            Enable: false
            Rollback: false
        NetworkConfiguration:
          AwsvpcConfiguration:
            SecurityGroups:
              - !Ref InstanceSecurityGroup
            Subnets:
              - !Ref AZ1PrivateSubnet1
              - !Ref AZ2PrivateSubnet1
        CapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1
        LaunchType: FARGATE
        LoadBalancers:
          - ContainerName: ${self:service}-dashboard-${self:provider.stage}
            ContainerPort: 2000
            TargetGroupArn: !Ref DashboardTargetGroup

    # =============================================================================
    # FRONTEND S3 BUCKET AND CLOUDFRONT
    # =============================================================================

    FrontendS3Bucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:custom.frontendS3Bucket}
        WebsiteConfiguration:
          IndexDocument: index.html
          ErrorDocument: error.html
        PublicAccessBlockConfiguration:
          BlockPublicAcls: false
          BlockPublicPolicy: false
          IgnorePublicAcls: false
          RestrictPublicBuckets: false

    FrontendS3BucketPolicy:
      Type: AWS::S3::BucketPolicy
      Properties:
        Bucket: !Ref FrontendS3Bucket
        PolicyDocument:
          Statement:
            - Effect: Allow
              Principal: "*"
              Action: s3:GetObject
              Resource: !Sub "${FrontendS3Bucket}/*"

    # =============================================================================
    # DNS RECORDS
    # =============================================================================

    BackendDNSRecord:
      Type: AWS::Route53::RecordSet
      Properties:
        HostedZoneId: ${self:custom.publicHostedZoneID}
        Name: ${self:custom.backendDomainName}
        Type: A
        AliasTarget:
          DNSName: !GetAtt ApplicationLoadBalancer.DNSName
          HostedZoneId: !GetAtt ApplicationLoadBalancer.CanonicalHostedZoneID

    DashboardDNSRecord:
      Type: AWS::Route53::RecordSet
      Properties:
        HostedZoneId: ${self:custom.publicHostedZoneID}
        Name: ${self:custom.dashboardDomainName}
        Type: A
        AliasTarget:
          DNSName: !GetAtt ApplicationLoadBalancer.DNSName
          HostedZoneId: !GetAtt ApplicationLoadBalancer.CanonicalHostedZoneID

    FrontendDNSRecord:
      Type: AWS::Route53::RecordSet
      Properties:
        HostedZoneId: ${self:custom.publicHostedZoneID}
        Name: ${self:custom.frontendDomainName}
        Type: CNAME
        TTL: 300
        ResourceRecords:
          - !GetAtt FrontendS3Bucket.DomainName

  Outputs:
    LoadBalancerDNS:
      Description: DNS name of the load balancer
      Value: !GetAtt ApplicationLoadBalancer.DNSName
      Export:
        Name: ${self:service}-${self:provider.stage}-LoadBalancerDNS

    BackendServiceURL:
      Description: URL of the backend API service
      Value: !Sub "https://${self:custom.backendDomainName}"
      Export:
        Name: ${self:service}-${self:provider.stage}-BackendServiceURL

    DashboardServiceURL:
      Description: URL of the dashboard service
      Value: !Sub "https://${self:custom.dashboardDomainName}"
      Export:
        Name: ${self:service}-${self:provider.stage}-DashboardServiceURL

    FrontendServiceURL:
      Description: URL of the frontend service
      Value: !Sub "https://${self:custom.frontendDomainName}"
      Export:
        Name: ${self:service}-${self:provider.stage}-FrontendServiceURL

    FrontendS3Bucket:
      Description: S3 bucket for frontend static files
      Value: !Ref FrontendS3Bucket
      Export:
        Name: ${self:service}-${self:provider.stage}-FrontendS3Bucket

    ECSClusterName:
      Description: Name of the ECS cluster
      Value: !Ref ECSCluster
      Export:
        Name: ${self:service}-${self:provider.stage}-ECSCluster
