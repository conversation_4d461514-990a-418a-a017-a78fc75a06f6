"""
LLM-Powered Intelligent Document Routing System
Best-in-class document analysis and processor selection using GPT-4
"""

import json
import asyncio
import time
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from openai import AsyncOpenAI
import magic
import logging

from .config import settings

logger = logging.getLogger(__name__)

@dataclass
class DocumentAnalysis:
    """Comprehensive document analysis result"""
    document_type: str
    content_complexity: Dict[str, Any]
    processing_challenges: List[str]
    recommended_processor: str
    confidence: float
    preprocessing_strategy: List[str]
    fallback_processors: List[str]
    estimated_processing_time: float
    cost_estimate: float

@dataclass
class ProcessingStrategy:
    """Complete processing strategy for a document"""
    primary_processor: str
    enhancement_processors: List[str]
    preprocessing_steps: List[str]
    confidence_threshold: float
    fallback_strategy: List[str]
    parallel_processing: bool
    batch_processing: bool

class LLMIntelligentRouter:
    """
    Intelligent document routing using LLM analysis
    
    This class provides best-in-class document analysis and routing decisions
    using GPT-4 to understand document content, structure, and optimal processing strategy.
    """
    
    def __init__(self):
        self.llm_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY) if settings.OPENAI_API_KEY else None

        # Anti-hallucination safeguards
        self.allowed_processors = {
            "OCR_PROCESSOR",
            "FORM_PARSER_PROCESSOR",
            "INVOICE_PROCESSOR",
            "LAYOUT_PARSER_PROCESSOR"
        }
        self.llm_temperature = 0.1  # Low temperature for consistency
        self.min_confidence_threshold = 0.7
        self.cache = {}  # Simple in-memory cache for routing decisions
        self.processor_capabilities = settings.PROCESSOR_CAPABILITIES
        
        # Document type classification patterns
        self.document_patterns = {
            "insurance": {
                "keywords": ["policy", "claim", "coverage", "premium", "deductible", "liability", "underwriting"],
                "patterns": ["policy number", "claim number", "effective date", "coverage limit"]
            },
            "financial": {
                "keywords": ["invoice", "receipt", "statement", "balance", "payment", "transaction"],
                "patterns": ["total amount", "due date", "account number", "transaction id"]
            },
            "legal": {
                "keywords": ["contract", "agreement", "terms", "conditions", "party", "whereas"],
                "patterns": ["effective date", "termination", "governing law", "signature"]
            },
            "identity": {
                "keywords": ["license", "passport", "identification", "birth date", "expiration"],
                "patterns": ["document number", "date of birth", "expiration date", "issued by"]
            },
            "form": {
                "keywords": ["application", "form", "please fill", "required", "optional"],
                "patterns": ["name:", "address:", "phone:", "email:", "date:"]
            },
            "report": {
                "keywords": ["report", "analysis", "summary", "findings", "conclusion"],
                "patterns": ["executive summary", "table of contents", "appendix", "references"]
            }
        }
    
    async def analyze_document(self, document_data: bytes, filename: str, metadata: Dict[str, Any] = None) -> DocumentAnalysis:
        """
        Perform comprehensive document analysis using LLM intelligence
        
        Args:
            document_data: Raw document bytes
            filename: Original filename
            metadata: Additional document metadata
            
        Returns:
            DocumentAnalysis: Comprehensive analysis with routing recommendations
        """
        start_time = time.time()
        
        try:
            # Generate cache key
            cache_key = self._generate_cache_key(document_data, filename)
            
            # Check cache first
            if cache_key in self.cache:
                logger.info(f"Cache hit for document analysis: {filename}")
                return self.cache[cache_key]
            
            # Extract document metadata
            file_info = self._extract_file_info(document_data, filename)
            
            # Get sample text for LLM analysis
            sample_text = await self._extract_sample_text(document_data, file_info['mime_type'])
            
            # Perform LLM analysis if available
            if self.llm_client and settings.LLM_ROUTING_ENABLED:
                llm_analysis = await self._perform_llm_analysis(sample_text, file_info, metadata)
            else:
                # Fallback to rule-based analysis
                llm_analysis = self._perform_rule_based_analysis(sample_text, file_info)
            
            # Create comprehensive analysis
            analysis = self._create_document_analysis(llm_analysis, file_info, time.time() - start_time)
            
            # Cache the result
            self.cache[cache_key] = analysis
            
            logger.info(f"Document analysis completed for {filename}: {analysis.document_type} -> {analysis.recommended_processor}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in document analysis for {filename}: {str(e)}")
            # Return safe fallback analysis
            return self._create_fallback_analysis(filename)
    
    async def create_processing_strategy(self, analysis: DocumentAnalysis) -> ProcessingStrategy:
        """
        Create optimal processing strategy based on document analysis
        
        Args:
            analysis: Document analysis result
            
        Returns:
            ProcessingStrategy: Complete processing strategy
        """
        
        # Select primary processor
        primary_processor = analysis.recommended_processor
        
        # Determine enhancement processors
        enhancement_processors = []
        
        # Add table extraction if needed
        if "tables" in analysis.content_complexity.get("elements", []):
            if primary_processor != "FORM_PARSER_PROCESSOR":
                enhancement_processors.append("FORM_PARSER_PROCESSOR")
        
        # Add handwriting support if needed
        if analysis.content_complexity.get("handwriting_level", "none") != "none":
            if primary_processor != "OCR_PROCESSOR":
                enhancement_processors.append("OCR_PROCESSOR")
        
        # Add form parsing for complex structured documents
        if analysis.document_type == "insurance" or "complex_layout" in analysis.processing_challenges:
            if primary_processor != "FORM_PARSER_PROCESSOR":
                enhancement_processors.append("FORM_PARSER_PROCESSOR")
        
        # Determine preprocessing steps
        preprocessing_steps = analysis.preprocessing_strategy
        
        # Set confidence threshold based on document complexity
        confidence_threshold = self._calculate_confidence_threshold(analysis)
        
        # Create fallback strategy
        fallback_strategy = analysis.fallback_processors
        
        # Determine parallel processing
        parallel_processing = (
            len(enhancement_processors) > 0 and 
            settings.PARALLEL_PROCESSING and
            analysis.content_complexity.get("complexity_score", 0) > 0.7
        )
        
        # Determine batch processing
        batch_processing = analysis.estimated_processing_time > 10.0
        
        return ProcessingStrategy(
            primary_processor=primary_processor,
            enhancement_processors=enhancement_processors,
            preprocessing_steps=preprocessing_steps,
            confidence_threshold=confidence_threshold,
            fallback_strategy=fallback_strategy,
            parallel_processing=parallel_processing,
            batch_processing=batch_processing
        )
    
    def _generate_cache_key(self, document_data: bytes, filename: str) -> str:
        """Generate cache key for document"""
        content_hash = hashlib.md5(document_data[:1024]).hexdigest()  # Hash first 1KB
        return f"{filename}_{len(document_data)}_{content_hash}"
    
    def _extract_file_info(self, document_data: bytes, filename: str) -> Dict[str, Any]:
        """Extract file information and metadata"""
        try:
            mime_type = magic.from_buffer(document_data, mime=True)
        except:
            mime_type = "application/octet-stream"
        
        file_extension = filename.split('.')[-1].lower() if '.' in filename else ""
        
        return {
            "filename": filename,
            "file_extension": file_extension,
            "mime_type": mime_type,
            "file_size": len(document_data),
            "is_image": mime_type.startswith("image/"),
            "is_pdf": mime_type == "application/pdf",
            "is_office": mime_type.startswith("application/vnd.openxmlformats") or mime_type.startswith("application/msword"),
            "is_text": mime_type.startswith("text/")
        }
    
    async def _extract_sample_text(self, document_data: bytes, mime_type: str) -> str:
        """Extract sample text for LLM analysis"""
        try:
            if mime_type.startswith("text/"):
                # Plain text file
                return document_data.decode('utf-8', errors='ignore')[:2000]
            elif mime_type == "application/pdf":
                # PDF file - extract first page text
                return await self._extract_pdf_sample(document_data)
            elif mime_type.startswith("image/"):
                # Image file - use basic OCR for sample
                return await self._extract_image_sample(document_data)
            else:
                # Other formats - return filename and basic info
                return f"Binary file: {mime_type}"
        except Exception as e:
            logger.warning(f"Failed to extract sample text: {str(e)}")
            return "Unable to extract sample text"
    
    async def _extract_pdf_sample(self, document_data: bytes) -> str:
        """Extract sample text from PDF"""
        try:
            import pdfplumber
            import io
            
            with pdfplumber.open(io.BytesIO(document_data)) as pdf:
                if pdf.pages:
                    text = pdf.pages[0].extract_text() or ""
                    return text[:2000]
            return "PDF with no extractable text"
        except Exception:
            return "PDF file (text extraction failed)"
    
    async def _extract_image_sample(self, document_data: bytes) -> str:
        """Extract sample text from image using basic OCR"""
        try:
            import pytesseract
            from PIL import Image
            import io
            
            image = Image.open(io.BytesIO(document_data))
            text = pytesseract.image_to_string(image)[:1000]
            return text if text.strip() else "Image with no detectable text"
        except Exception:
            return "Image file (OCR extraction failed)"

    async def _perform_llm_analysis(self, sample_text: str, file_info: Dict[str, Any], metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform comprehensive LLM analysis of the document

        Args:
            sample_text: Sample text from document
            file_info: File information and metadata
            metadata: Additional metadata

        Returns:
            Dict containing LLM analysis results
        """

        # Prepare comprehensive analysis prompt
        analysis_prompt = f"""
        You are an expert document analyst for an OCR system. Analyze this document for optimal processing strategy.

        DOCUMENT METADATA:
        - Filename: {file_info['filename']}
        - File size: {file_info['file_size']} bytes
        - File type: {file_info['mime_type']}
        - Extension: {file_info['file_extension']}

        CONTENT SAMPLE (first 2000 characters):
        {sample_text}

        ANALYSIS REQUIREMENTS:

        1. DOCUMENT TYPE CLASSIFICATION (choose the most specific):
           - insurance_claim, insurance_policy, insurance_application, insurance_certificate
           - financial_invoice, financial_receipt, financial_statement, financial_report
           - legal_contract, legal_agreement, legal_notice, legal_form
           - identity_license, identity_passport, identity_card
           - business_form, application_form, survey_form
           - technical_report, research_paper, article
           - general_document, handwritten_note

        2. CONTENT COMPLEXITY ANALYSIS:
           - text_density: low/medium/high (amount of text per page)
           - table_presence: none/simple/complex (structured data in tables)
           - handwriting_level: none/minimal/significant (handwritten content)
           - language_detection: primary language and any secondary languages
           - layout_complexity: simple/medium/complex (document structure)
           - image_elements: none/minimal/significant (charts, diagrams, photos)
           - form_fields: none/few/many (fillable fields, checkboxes)
           - complexity_score: 0.0-1.0 (overall complexity rating)

        3. PROCESSING CHALLENGES IDENTIFICATION:
           - poor_scan_quality: true/false
           - skewed_rotation: true/false
           - mixed_content_types: true/false (text + images + tables)
           - multi_column_layout: true/false
           - small_font_size: true/false
           - faded_text: true/false
           - background_noise: true/false
           - complex_formatting: true/false

        4. OPTIMAL PROCESSOR RECOMMENDATION:
           Based on Google Document AI processors (ONLY choose from these 4):
           - OCR_PROCESSOR: Best for general text, handwriting, multilingual docs, identity documents
           - FORM_PARSER_PROCESSOR: Best for structured forms, key-value pairs, insurance applications
           - INVOICE_PROCESSOR: Best for financial documents, invoices, receipts, expense reports
           - LAYOUT_PARSER_PROCESSOR: Best for structured reports, articles, multi-column layouts

        5. PREPROCESSING STRATEGY:
           Recommend preprocessing steps from: [deskew, denoise, enhance, binarize, sharpen, contrast_adjust]

        6. CONFIDENCE AND FALLBACK:
           - processing_confidence: 0.0-1.0 (expected success rate)
           - fallback_processors: list of alternative processors
           - estimated_time: processing time in seconds
           - cost_estimate: relative cost (low/medium/high)

        Respond ONLY with valid JSON in this exact format:
        {{
            "document_type": "specific_type",
            "content_complexity": {{
                "text_density": "medium",
                "table_presence": "simple",
                "handwriting_level": "none",
                "language_detection": ["en"],
                "layout_complexity": "medium",
                "image_elements": "minimal",
                "form_fields": "few",
                "complexity_score": 0.6
            }},
            "processing_challenges": ["challenge1", "challenge2"],
            "recommended_processor": "PROCESSOR_NAME",
            "preprocessing_strategy": ["step1", "step2"],
            "processing_confidence": 0.85,
            "fallback_processors": ["FALLBACK1", "FALLBACK2"],
            "estimated_time": 5.0,
            "cost_estimate": "medium"
        }}
        """

        try:
            response = await self.llm_client.chat.completions.create(
                model=settings.LLM_MODEL,
                messages=[{"role": "user", "content": analysis_prompt}],
                max_tokens=settings.LLM_MAX_TOKENS,
                temperature=settings.LLM_TEMPERATURE,
                response_format={"type": "json_object"}
            )

            analysis_result = json.loads(response.choices[0].message.content)

            # Validate and enhance the result
            return self._validate_llm_analysis(analysis_result, file_info)

        except Exception as e:
            logger.error(f"LLM analysis failed: {str(e)}")
            # Fallback to rule-based analysis
            return self._perform_rule_based_analysis(sample_text, file_info)

    def _perform_rule_based_analysis(self, sample_text: str, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fallback rule-based document analysis when LLM is not available

        Args:
            sample_text: Sample text from document
            file_info: File information

        Returns:
            Dict containing analysis results
        """

        text_lower = sample_text.lower()

        # Document type classification
        document_type = "general_document"
        confidence = 0.7

        for doc_type, patterns in self.document_patterns.items():
            keyword_matches = sum(1 for keyword in patterns["keywords"] if keyword in text_lower)
            pattern_matches = sum(1 for pattern in patterns["patterns"] if pattern in text_lower)

            if keyword_matches >= 2 or pattern_matches >= 1:
                document_type = doc_type
                confidence = min(0.9, 0.6 + (keyword_matches + pattern_matches) * 0.1)
                break

        # Content complexity analysis
        complexity_score = 0.5

        # Increase complexity for certain indicators
        if "table" in text_lower or "|" in sample_text:
            complexity_score += 0.2
        if len(sample_text.split('\n')) > 20:
            complexity_score += 0.1
        if any(char.isdigit() for char in sample_text):
            complexity_score += 0.1

        complexity_score = min(1.0, complexity_score)

        # Simplified 4-processor mapping (anti-hallucination)
        processor_mapping = {
            "insurance": "FORM_PARSER_PROCESSOR",
            "financial": "INVOICE_PROCESSOR",
            "financial_receipt": "INVOICE_PROCESSOR",
            "financial_invoice": "INVOICE_PROCESSOR",
            "financial_bank": "LAYOUT_PARSER_PROCESSOR",
            "legal": "FORM_PARSER_PROCESSOR",
            "identity": "OCR_PROCESSOR",
            "form": "FORM_PARSER_PROCESSOR",
            "report": "LAYOUT_PARSER_PROCESSOR"
        }

        recommended_processor = processor_mapping.get(document_type, "OCR_PROCESSOR")

        return {
            "document_type": document_type,
            "content_complexity": {
                "text_density": "medium",
                "table_presence": "simple" if "|" in sample_text else "none",
                "handwriting_level": "none",
                "language_detection": ["en"],
                "layout_complexity": "medium",
                "image_elements": "minimal",
                "form_fields": "few" if ":" in sample_text else "none",
                "complexity_score": complexity_score
            },
            "processing_challenges": self._identify_challenges(sample_text, file_info),
            "recommended_processor": recommended_processor,
            "preprocessing_strategy": ["enhance", "deskew"],
            "processing_confidence": confidence,
            "fallback_processors": ["OCR_PROCESSOR", "FORM_PARSER_PROCESSOR"],
            "estimated_time": 3.0 + complexity_score * 2.0,
            "cost_estimate": "medium"
        }

    def _identify_challenges(self, sample_text: str, file_info: Dict[str, Any]) -> List[str]:
        """Identify potential processing challenges"""
        challenges = []

        if file_info['is_image']:
            challenges.append("image_processing_required")

        if len(sample_text) < 100:
            challenges.append("limited_text_content")

        if file_info['file_size'] > 10 * 1024 * 1024:  # 10MB
            challenges.append("large_file_size")

        if not sample_text.strip():
            challenges.append("no_extractable_text")

        return challenges

    def _validate_llm_analysis(self, analysis: Dict[str, Any], file_info: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and enhance LLM analysis result"""

        # Ensure required fields exist
        required_fields = [
            "document_type", "content_complexity", "processing_challenges",
            "recommended_processor", "preprocessing_strategy", "processing_confidence",
            "fallback_processors", "estimated_time", "cost_estimate"
        ]

        for field in required_fields:
            if field not in analysis:
                logger.warning(f"Missing field in LLM analysis: {field}")
                # Add default value
                analysis[field] = self._get_default_value(field)

        # Simplified 4-processor name mapping (anti-hallucination)
        processor_name_mapping = {
            "FORM_PARSER_PROCESSOR": "FORM_PARSER_PROCESSOR",
            "LAYOUT_PARSER_PROCESSOR": "LAYOUT_PARSER_PROCESSOR",
            "INVOICE_PROCESSOR": "INVOICE_PROCESSOR",
            "OCR_PROCESSOR": "OCR_PROCESSOR",
            # Legacy mappings for backward compatibility
            "EXPENSE_PROCESSOR": "INVOICE_PROCESSOR",
            "BANK_PROCESSOR": "LAYOUT_PARSER_PROCESSOR",
            "CUSTOM_EXTRACTION_PROCESSOR": "FORM_PARSER_PROCESSOR"
        }

        # Validate and convert processor name (anti-hallucination)
        recommended_processor = analysis["recommended_processor"]

        # Check if processor is in allowed list
        if recommended_processor not in self.allowed_processors:
            logger.warning(f"LLM recommended invalid processor: {recommended_processor}. Using fallback.")
            # Map to valid processor or use OCR as fallback
            if recommended_processor in processor_name_mapping:
                analysis["recommended_processor"] = processor_name_mapping[recommended_processor]
            else:
                analysis["recommended_processor"] = "OCR_PROCESSOR"  # Safe fallback

        # Ensure final processor is valid
        final_processor = analysis["recommended_processor"]
        if final_processor not in self.allowed_processors:
            logger.error(f"Invalid processor after mapping: {final_processor}. Using OCR_PROCESSOR.")
            analysis["recommended_processor"] = "OCR_PROCESSOR"

        # Validate processor exists in capabilities
        processor_capabilities = list(settings.PROCESSOR_CAPABILITIES.keys())
        if analysis["recommended_processor"] not in processor_capabilities:
            logger.warning(f"Invalid processor recommended: {analysis['recommended_processor']}")
            analysis["recommended_processor"] = "OCR_PROCESSOR"

        # Ensure confidence is in valid range
        analysis["processing_confidence"] = max(0.0, min(1.0, analysis["processing_confidence"]))

        return analysis

    def _get_default_value(self, field: str) -> Any:
        """Get default value for missing analysis field"""
        defaults = {
            "document_type": "general_document",
            "content_complexity": {"complexity_score": 0.5},
            "processing_challenges": [],
            "recommended_processor": "OCR_PROCESSOR",
            "preprocessing_strategy": ["enhance"],
            "processing_confidence": 0.7,
            "fallback_processors": ["OCR_PROCESSOR"],
            "estimated_time": 3.0,
            "cost_estimate": "medium"
        }
        return defaults.get(field, None)

    def _create_document_analysis(self, llm_analysis: Dict[str, Any], file_info: Dict[str, Any], processing_time: float) -> DocumentAnalysis:
        """Create DocumentAnalysis object from LLM analysis"""

        return DocumentAnalysis(
            document_type=llm_analysis["document_type"],
            content_complexity=llm_analysis["content_complexity"],
            processing_challenges=llm_analysis["processing_challenges"],
            recommended_processor=llm_analysis["recommended_processor"],
            confidence=llm_analysis["processing_confidence"],
            preprocessing_strategy=llm_analysis["preprocessing_strategy"],
            fallback_processors=llm_analysis["fallback_processors"],
            estimated_processing_time=llm_analysis["estimated_time"],
            cost_estimate=self._convert_cost_estimate(llm_analysis["cost_estimate"])
        )

    def _create_fallback_analysis(self, filename: str) -> DocumentAnalysis:
        """Create safe fallback analysis when all else fails"""

        return DocumentAnalysis(
            document_type="general_document",
            content_complexity={"complexity_score": 0.5},
            processing_challenges=["analysis_failed"],
            recommended_processor="OCR_PROCESSOR",
            confidence=0.6,
            preprocessing_strategy=["enhance"],
            fallback_processors=["FORM_PARSER_PROCESSOR"],
            estimated_processing_time=5.0,
            cost_estimate=0.002
        )

    def _convert_cost_estimate(self, cost_str: str) -> float:
        """Convert cost estimate string to numeric value"""
        cost_mapping = {
            "low": 0.001,
            "medium": 0.003,
            "high": 0.008
        }
        return cost_mapping.get(cost_str, 0.003)

    def _calculate_confidence_threshold(self, analysis: DocumentAnalysis) -> float:
        """Calculate appropriate confidence threshold based on document complexity"""

        base_threshold = settings.DEFAULT_CONFIDENCE_THRESHOLD
        complexity_score = analysis.content_complexity.get("complexity_score", 0.5)

        # Lower threshold for complex documents
        if complexity_score > 0.8:
            return base_threshold - 0.1
        elif complexity_score > 0.6:
            return base_threshold - 0.05
        else:
            return base_threshold

    async def get_processor_recommendations(self, document_type: str, complexity: Dict[str, Any]) -> List[str]:
        """
        Get ranked list of processor recommendations for a document type

        Args:
            document_type: Type of document
            complexity: Content complexity analysis

        Returns:
            List of processors ranked by suitability
        """

        recommendations = []

        # Simplified 4-processor recommendations (anti-hallucination)
        type_mapping = {
            "insurance": ["FORM_PARSER_PROCESSOR", "OCR_PROCESSOR"],
            "financial": ["INVOICE_PROCESSOR", "FORM_PARSER_PROCESSOR", "LAYOUT_PARSER_PROCESSOR"],
            "legal": ["FORM_PARSER_PROCESSOR", "LAYOUT_PARSER_PROCESSOR"],
            "identity": ["OCR_PROCESSOR", "FORM_PARSER_PROCESSOR"],
            "form": ["FORM_PARSER_PROCESSOR", "OCR_PROCESSOR"],
            "report": ["LAYOUT_PARSER_PROCESSOR", "OCR_PROCESSOR"]
        }

        # Get base recommendations
        base_type = document_type.split('_')[0] if '_' in document_type else document_type
        recommendations.extend(type_mapping.get(base_type, ["OCR_PROCESSOR"]))

        # Add complexity-based recommendations
        if complexity.get("table_presence") == "complex":
            if "FORM_PARSER_PROCESSOR" not in recommendations:
                recommendations.insert(1, "FORM_PARSER_PROCESSOR")

        if complexity.get("handwriting_level") != "none":
            if "OCR_PROCESSOR" not in recommendations:
                recommendations.append("OCR_PROCESSOR")

        # Always include OCR as final fallback
        if "OCR_PROCESSOR" not in recommendations:
            recommendations.append("OCR_PROCESSOR")

        return recommendations[:3]  # Return top 3 recommendations

    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get routing statistics and performance metrics"""

        total_analyses = len(self.cache)

        if total_analyses == 0:
            return {"total_analyses": 0, "cache_hit_rate": 0.0}

        # Analyze cached results
        document_types = {}
        processors_used = {}

        for analysis in self.cache.values():
            doc_type = analysis.document_type
            processor = analysis.recommended_processor

            document_types[doc_type] = document_types.get(doc_type, 0) + 1
            processors_used[processor] = processors_used.get(processor, 0) + 1

        return {
            "total_analyses": total_analyses,
            "document_types": document_types,
            "processors_used": processors_used,
            "cache_size": len(self.cache),
            "most_common_type": max(document_types.items(), key=lambda x: x[1])[0] if document_types else None,
            "most_used_processor": max(processors_used.items(), key=lambda x: x[1])[0] if processors_used else None
        }

    def clear_cache(self):
        """Clear the routing cache"""
        self.cache.clear()
        logger.info("Routing cache cleared")

# Global router instance
intelligent_router = LLMIntelligentRouter()
