<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claims Liability Reviewer - Zurich Insurance</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mark.js/8.11.1/mark.min.js"></script>
</head>
<body>
    <!-- Header Section -->
    <header class="claim-header">
        <div class="container">
            <div class="header-content">
                <div class="claim-info">
                    <h1 class="claim-title">Claims Liability Reviewer</h1>
                    <div class="claim-reference">
                        <span class="reference-label">Claim Reference:</span>
                        <span id="claimReference">Loading...</span>
                    </div>
                </div>
                <div class="claim-status-section">
                    <div class="status-indicator">
                        <span class="status-label">Status:</span>
                        <span id="claimStatus" class="status status--info">Loading...</span>
                    </div>
                    <div class="confidence-display">
                        <span class="confidence-label">Overall Confidence:</span>
                        <div class="confidence-meter">
                            <div class="confidence-bar">
                                <div id="confidenceProgress" class="confidence-progress" style="width: 0%"></div>
                            </div>
                            <span id="confidenceScore" class="confidence-value">0%</span>
                        </div>
                    </div>
                </div>
                <div class="quick-actions">
                    <button class="btn btn--primary btn--sm" id="approveBtn">Approve</button>
                    <button class="btn btn--secondary btn--sm" id="escalateBtn">Escalate</button>
                    <button class="btn btn--outline btn--sm" id="requestInfoBtn">Request Info</button>
                    <button class="btn btn--zendesk btn--sm" id="zendeskBtn" title="Open Support Dashboard">
                        <span class="btn-icon">🎫</span> Support
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard Layout -->
    <main class="dashboard-container">
        <!-- Left Panel: Analysis Decision Cards -->
        <section class="analysis-panel">
            
        </aside>

            <div class="panel-header">
                <h3>Analysis Dashboard</h3>
                <div class="analysis-controls">
                    <button class="btn btn--sm btn--outline" id="refreshBtn">Refresh</button>
                </div>
            </div>
            <div class="decision-cards">
                <!-- Coverage Decision Card -->
                <div class="decision-card" id="coverageCard">
                    <div class="card-header">
                        <h4>Coverage Decision</h4>
                        <div class="decision-indicator">
                            <span id="coverageDecision" class="decision-status">Analyzing...</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="confidence-row">
                            <span>Confidence:</span>
                            <div class="mini-confidence-meter">
                                <div class="mini-confidence-bar">
                                    <div id="coverageConfidence" class="mini-confidence-progress" style="width: 0%"></div>
                                </div>
                                <span id="coverageConfidenceText">0%</span>
                            </div>
                        </div>
                        <div class="decision-details">
                            <p id="coverageJustification">Analyzing coverage terms...</p>
                        </div>
                        <button class="btn btn--sm btn--outline expand-btn" data-target="coverage">View Details</button>
                    </div>
                </div>

                <!-- Fault Analysis Card -->
                <div class="decision-card" id="faultCard">
                    <div class="card-header">
                        <h4>Fault Analysis</h4>
                        <div class="fault-summary">
                            <span id="faultType">Analyzing...</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="fault-breakdown" id="faultBreakdown">
                            <div class="loading-placeholder">Analyzing fault allocation...</div>
                        </div>
                        <div class="confidence-row">
                            <span>Confidence:</span>
                            <div class="mini-confidence-meter">
                                <div class="mini-confidence-bar">
                                    <div id="faultConfidence" class="mini-confidence-progress" style="width: 0%"></div>
                                </div>
                                <span id="faultConfidenceText">0%</span>
                            </div>
                        </div>
                        <button class="btn btn--sm btn--outline expand-btn" data-target="fault">View Details</button>
                    </div>
                </div>

                <!-- Quantum Assessment Card -->
                <div class="decision-card" id="quantumCard">
                    <div class="card-header">
                        <h4>Quantum Assessment</h4>
                        <div class="quantum-amount">
                            <span id="settlementAmount">$0</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="quantum-breakdown" id="quantumBreakdown">
                            <div class="loading-placeholder">Calculating settlement...</div>
                        </div>
                        <div class="confidence-row">
                            <span>Confidence:</span>
                            <div class="mini-confidence-meter">
                                <div class="mini-confidence-bar">
                                    <div id="quantumConfidence" class="mini-confidence-progress" style="width: 0%"></div>
                                </div>
                                <span id="quantumConfidenceText">0%</span>
                            </div>
                        </div>
                        <button class="btn btn--sm btn--outline expand-btn" data-target="quantum">View Details</button>
                    </div>
                </div>

                <!-- Risk Assessment Card -->
                <div class="decision-card" id="riskCard">
                    <div class="card-header">
                        <h4>Risk Assessment</h4>
                        <div class="risk-indicator">
                            <span id="riskLevel" class="status status--warning">Medium</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="risk-factors" id="riskFactors">
                            <div class="loading-placeholder">Analyzing risk factors...</div>
                        </div>
                        <div class="review-timeline">
                            <span>Review Timeline: <strong id="reviewTimeline">14 days</strong></span>
                        </div>
                        <button class="btn btn--sm btn--outline expand-btn" data-target="risk">View Details</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Center Panel: Documents & Evidence -->
        <aside class="document-panel">
            <div class="panel-header">
                <h3>Documents & Evidence</h3>
               
            </div>
            <div class="document-content">

                <div class="document-viewer">
                    <div class="email-content active" id="emailContent">
                        <div class="loading-placeholder">Loading email content...</div>
                    </div>
                    <div class="documents-content" id="documentsContent">
                        <div class="document-list">
                            <div class="loading-placeholder">Loading documents...</div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Right Panel: Analysis Details -->
        <aside class="details-panel">
            <div class="panel-header">
                <h3>Analysis Details</h3>
                <div class="level-selector">
                    <select id="levelSelect" class="form-control">
                        <option value="all">All Levels</option>
                        <option value="level01">Level 01 - Initial</option>
                        <option value="level02">Level 02 - Coverage</option>
                        <option value="level03">Level 03 - Fault</option>
                        <option value="level04">Level 04 - Quantum</option>
                    </select>
                </div>
            </div>
            <div class="details-content">
                <div class="analysis-sections">
                    <!-- Level 01 Section -->
                    <div class="analysis-section" id="level01Section">
                        <div class="section-header">
                            <h4>Level 01 - Initial Analysis</h4>
                            <button class="collapse-btn" data-target="level01Details">
                                <span class="collapse-icon">▼</span>
                            </button>
                        </div>
                        <div class="section-content" id="level01Details">
                            <div class="detail-group">
                                <h5>Claim Details</h5>
                                <div class="detail-item">
                                    <span class="label">Incident Date:</span>
                                    <span id="incidentDate" class="value">Loading...</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">Location:</span>
                                    <span id="incidentLocation" class="value">Loading...</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">Claimant:</span>
                                    <span id="claimantName" class="value">Loading...</span>
                                </div>
                            </div>
                            <div class="detail-group">
                                <h5>Policy Information</h5>
                                <div class="detail-item">
                                    <span class="label">Policy Number:</span>
                                    <span id="policyNumber" class="value">Loading...</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">Policy Holder:</span>
                                    <span id="policyHolder" class="value">Loading...</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">Coverage Limits:</span>
                                    <span id="policyLimits" class="value">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Level 02 Section -->
                    <div class="analysis-section" id="level02Section">
                        <div class="section-header">
                            <h4>Level 02 - Coverage Analysis</h4>
                            <button class="collapse-btn" data-target="level02Details">
                                <span class="collapse-icon">▼</span>
                            </button>
                        </div>
                        <div class="section-content" id="level02Details">
                            <div class="detail-group">
                                <h5>Coverage Assessment</h5>
                                <div class="detail-item">
                                    <span class="label">Decision:</span>
                                    <span id="coverageDecisionDetail" class="value">Loading...</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">Justification:</span>
                                    <span id="coverageJustificationDetail" class="value">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Level 03 Section -->
                    <div class="analysis-section" id="level03Section">
                        <div class="section-header">
                            <h4>Level 03 - Fault Analysis</h4>
                            <button class="collapse-btn" data-target="level03Details">
                                <span class="collapse-icon">▼</span>
                            </button>
                        </div>
                        <div class="section-content" id="level03Details">
                            <div class="detail-group">
                                <h5>Fault Assessment</h5>
                                <div class="detail-item">
                                    <span class="label">Fault Type:</span>
                                    <span id="faultTypeDetail" class="value">Loading...</span>
                                </div>
                                <div class="fault-parties" id="faultPartiesDetail">
                                    <div class="loading-placeholder">Loading fault breakdown...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Level 04 Section -->
                    <div class="analysis-section" id="level04Section">
                        <div class="section-header">
                            <h4>Level 04 - Quantum Analysis</h4>
                            <button class="collapse-btn" data-target="level04Details">
                                <span class="collapse-icon">▼</span>
                            </button>
                        </div>
                        <div class="section-content" id="level04Details">
                            <div class="detail-group">
                                <h5>Settlement Breakdown</h5>
                                <div class="detail-item">
                                    <span class="label">Settlement Recommendation:</span>
                                    <span id="settlementRecommendation" class="value">Loading...</span>
                                </div>
                                <div class="quantum-details" id="quantumDetails">
                                    <div class="loading-placeholder">Loading quantum breakdown...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading claim data...</p>
        </div>
    </div>

    <!-- Tooltip for hover interactions -->
    <div class="tooltip" id="tooltip" style="display: none;">
        <div class="tooltip-content">
            <div class="tooltip-header">
                <span class="tooltip-title"></span>
                <span class="tooltip-confidence"></span>
            </div>
            <div class="tooltip-body"></div>
        </div>
    </div>

    <!-- Supabase JavaScript Client -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // Check if Supabase CDN loaded successfully (like working dashboard)
        if (typeof supabase !== 'undefined' && supabase.createClient) {
            console.log('✅ Supabase CDN loaded successfully');
        } else {
            console.error('❌ Supabase CDN failed to load');
        }
    </script>

    <!-- Application Script -->
    <script src="app.js"></script>
</body>
</html>