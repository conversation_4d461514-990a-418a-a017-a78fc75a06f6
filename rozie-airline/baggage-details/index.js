/* eslint-disable no-case-declarations */
const { ApiResp } = require("./helpers/api-helper.js")
const { createBaggage } = require("./baggage-helper/create-baggage.js")
const { getBaggage } = require("./baggage-helper/get-baggage.js")


/**
 * AWS Lambda handler for managing PNR operations. 
 * This function processes incoming API requests to perform CRUD operations on PNR records.
 *
 * Supported endpoints:
 *  - /createBaggage: Create a new PNR record.
 *  - /getCustomerPNR: Get all PNR records for a specific customer by customer_id.
 *  - /getPNR: Get a single PNR by pnr_id.
 *  - /updatePNR: Update an existing PNR.
 *  - /deletePNR: Delete a PNR by pnr_id.
 * 
 * @param {object} event - The event object representing the API request, including body and resource path.
 * @returns {object} - An API response object containing the result of the operation or an error message.
 */

const lambda_handler = async (event) => {
    console.log(JSON.stringify(event))
    let body = JSON.parse(event.body)
    const path = event.resource

    try {
        switch (path) {
            case "/createBaggage":
                const createBaggageResponse = await createBaggage(body)
                return createBaggageResponse
            case "/getBaggage":
                const baggage = await getBaggage(body)
                return baggage
            default:
                return ApiResp({
                    status: "Error",
                    message: "Invalid resource path"
                }, 400)
        }
    } catch (error) {
        console.log(error)
        return ApiResp({
            status: "Error",
            message: "Internal Server Error"
        }, 500)
    }
}

module.exports = {
    lambda_handler,
}
