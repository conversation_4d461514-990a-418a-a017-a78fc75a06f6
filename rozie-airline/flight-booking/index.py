"""
Module for handling flight details CRUD operations
"""

import json
import re
import os
import string
import random
from helpers.logging_helper import get_log_level, set_logging_level, print_info_log
from helpers.decorator_helper import exception_handler
from helpers.request_helper import post_request
from helpers.response_helper import format_response


@exception_handler
def lambda_handler(event, context):
    """
    Lambda handler for flight details crud operation APIs
    """
    set_logging_level(get_log_level())
    print_info_log("", "Flight Details api invoked", event)
    http_method = event.get("httpMethod", "")
    path = event.get("path", "")

    if http_method == "POST" and path == "/book-flights":
        body = json.loads(event.get("body", "{}"))
        return book_flight(body)
    if http_method == "POST" and path == "/update-booking":
        body = json.loads(event.get("body", "{}"))
        return update_booking(body)
    if http_method == "POST" and path == "/delete-booking":
        body = json.loads(event.get("body", "{}"))
        return delete_booking(body)

    # Invalid request
    print_info_log("", "Invalid request", event)
    print_info_log("", "Flight Details api completed", event)
    return format_response(400, None, "Invalid request")


def book_flight(body):
    """
    Book flight
    """
    customer_id = body.get("customer_id", "")
    schedule_id = body.get("schedule_id", "")
    payment_amount = body.get("payment_amount", "")
    travelers_details = body.get("travelers_details", {})

    url = os.environ.get("CREATE_PNR_URL")
    headers = {"Content-Type": "application/json", "x-api-key": os.environ.get("CREATE_PNR_KEY")}
    data = {
        "customer_id": customer_id,
        "payment_amount": payment_amount,
        "schedule_id": schedule_id,
        "travelers_details": travelers_details,
    }

    status, result = post_request(url, headers, data)
    if status == "Success":
        return format_response(200, result)
    return format_response(400, result)

def update_booking(body):
    """
    Update flight booking
    """
    url = os.environ.get("UPDATE_PNR_URL")
    headers = {"Content-Type": "application/json", "x-api-key": os.environ.get("CREATE_PNR_KEY")}
    status, result = post_request(url, headers, body)
    if status == "Success":
        return format_response(200, result)
    return format_response(400, result)

def delete_booking(body):
    """
    Delete flight booking
    """
    url = os.environ.get("DELETE_PNR_URL")
    headers = {"Content-Type": "application/json", "x-api-key": os.environ.get("CREATE_PNR_KEY")}
    status, result = post_request(url, headers, body)
    if status == "Success":
        return format_response(200, result)
    return format_response(400, result)