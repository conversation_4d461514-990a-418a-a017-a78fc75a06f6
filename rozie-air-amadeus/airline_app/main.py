import logging
from fastapi import FastAPI
from fastapi.responses import JSONResponse
from api.routes import flight_status, flight_offers, flight_orders, locations
from core.config import settings

# Configure logging
logging.basicConfig(
    level=settings.LOG_LEVEL,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Airline API",
    description="API for airline operations including flight status, offers, and orders",
    version="1.0.0"
)

# Include routers
app.include_router(flight_status.router, prefix="/api/v1", tags=["Flight Status"])
app.include_router(flight_offers.router, prefix="/api/v1", tags=["Flight Offers"])
app.include_router(flight_orders.router, prefix="/api/v1", tags=["Flight Orders"])
app.include_router(locations.router, prefix="/api/v1/locations", tags=["Locations"])

@app.get("/readiness", status_code=200)
async def health_check():
    """
    Health check endpoint for AWS ALB/API Gateway.
    """
    return JSONResponse(content={"status": "ok"})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "airline_app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=True
    ) 