import traceback
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from airline_app.adapters.auth_adapter import get_api_key

from airline_app.model.models import ActivePNRDetails, PNR

from airline_app.helpers.logging_helper import print_info_log


from airline_app.services.pnr import (
    get_active_pnr,
    get_pnr_details,
    update_pnr_details,
    get_pnr_details_by_pnr_id,
    get_flight_id_for_pnr,
    update_passenger_name
)

pnr_router = APIRouter(prefix="/pnr")


@pnr_router.get(
    "/active/{customer_id}",
    dependencies=[Depends(get_api_key)]
)
async def send_message_v3(customer_id: str) -> dict:
    try:
        pnr_data = get_active_pnr(customer_id)
        if not pnr_data:
            raise HTTPException(status_code=404, detail="Customer ID not found")
        return pnr_data
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )


@pnr_router.get(
    "/get_pnr",
    dependencies=[Depends(get_api_key)],
    response_model=Optional[list[PNR] | dict],
)
async def get_pnr(pnr_id: str, customer_last_name: str) -> dict:
    try:
        result = None
        if pnr_id and customer_last_name:
            result = get_pnr_details(pnr_id, customer_last_name)
        if not result:
            raise HTTPException(status_code=404, detail="PNR ID not found")
        return result
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )


@pnr_router.post("/update_pnr", dependencies=[Depends(get_api_key)])
async def update_pnr(pnr_details: PNR) -> dict:
    try:
        pnr_data = update_pnr_details(pnr_details.pnr, pnr_details.model_dump())
        if not pnr_data:
            raise HTTPException(status_code=404, detail="PNR ID not found")
        return {"status": "Success"}
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )


@pnr_router.get(
    "/get_pnr_details_by_pnr",
    dependencies=[Depends(get_api_key)],
    response_model=Optional[list[PNR] | dict],
)
async def get_pnr_details_by_pnr_id_api(pnr_id: str) -> dict:
    try:
        result = None
        if pnr_id:
            result = get_pnr_details_by_pnr_id(pnr_id)
        if not result:
            raise HTTPException(status_code=404, detail="PNR ID not found")
        return result
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )


@pnr_router.get(
    "/get_flight_id_by_pnr",
    dependencies=[Depends(get_api_key)],
    response_model=Optional[dict],
)
async def get_flight_id_by_pnr(pnr_id: str, customer_last_name: str) -> dict:
    try:
        result = None
        if pnr_id and customer_last_name:
            result = get_flight_id_for_pnr(pnr_id, customer_last_name)
        else:
            raise HTTPException(status_code=500, detail="Must provide both PNR ID and Customer Last Name.")
        if not result:
            raise HTTPException(status_code=404, detail="PNR ID not found")
        return result
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )

@pnr_router.get(
    "/get_pnr_for_update",
    dependencies=[Depends(get_api_key)],
    response_model=Optional[list[PNR] | dict],
)
async def get_pnr_for_update(pnr_id: str) -> dict:
    try:
        result = None
        if pnr_id:
            result = get_pnr_details_by_pnr_id(pnr_id)
        if not result:
            raise HTTPException(status_code=404, detail="PNR ID not found")
        return result[0]
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )

@pnr_router.post(
    "/name_correction",
    dependencies=[Depends(get_api_key)]
)
async def name_correction(body: dict) -> dict:
    try:
        pnr_id = body.get("pnr_id")
        first_name = body.get("first_name")
        if pnr_id:
            result = update_passenger_name(pnr_id, first_name)
            if not result:
                raise HTTPException(status_code=404, detail="PNR ID not found")
        return result
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )


# {
#     "booking_date": Decimal("1734506400"),
#     "customer_id": "CUST001",
#     "payment_status": "Paid",
#     "flight_id": "FL123",
#     "passengers": [
#         {
#             "name": "John Doe",
#             "seat": "12A",
#             "passenger_class": "Economy",
#             "checked_in": True,
#             "additional_services": ["Extra baggage", "Meal"],
#         },
#         {
#             "name": "Jane Smith",
#             "seat": "12B",
#             "passenger_class": "Economy",
#             "checked_in": False,
#             "additional_services": [],
#         },
#     ],
#     "pnr_id": "ABC123",
#     "flight_details": {
#         "flight_number": "FL123",
#         "airline": "Rozie Airline",
#         "departure_airport": "PNQ",
#         "arrival_airport": "GOI",
#     },
# }
