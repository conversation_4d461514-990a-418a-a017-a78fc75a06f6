service: ${self:custom.serviceName}

provider:
  name: aws
  runtime: python3.10  # Use Python 3.10 for FastAPI
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  iamRoleStatements:
    - Effect: "Allow"
      Action:
        # ECS permissions
        - ecs:RunTask
        - ecs:DescribeTasks
        - ecs:StopTask
        - ecs:DescribeServices
        - ecs:UpdateService
        
        # CloudWatch Logs permissions
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents

        # ECR permissions
        - ecr:GetAuthorizationToken
        - ecr:BatchGetImage
        - ecr:GetDownloadUrlForLayer

      Resource: "*"

custom:
  serviceName: rozie-brook
  ecrRepoUri: ${env:ECR_REPO_URI} # Replace with your ECR repository URI
  awsRegion: ${env:AWS_REGION}   # The region you are deploying to

resources:
  Resources:
    # ECS Cluster setup
    ECSCluster:
      Type: AWS::ECS::Cluster
      Properties:
        ClusterName: ${self:service}-${self:provider.stage}-cluster
        CapacityProviders:
          - FARGATE
        DefaultCapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1

    # IAM Role for ECS Task Execution
    ECRECSExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:service}-${self:provider.stage}-ecs-execution
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Principal:
                Service: ecs-tasks.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: ${self:service}-${self:provider.stage}-policy
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: Allow
                  Action:
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:GetAuthorizationToken
                    - ecr:BatchGetImage
                    - ecr:GetDownloadUrlForLayer
                  Resource: "*"

    # CloudWatch Log Group
    CloudWatchLogsGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: "${self:service}-${self:provider.stage}"
        RetentionInDays: 30

    # VPC setup (Only public subnets)
    VPC:
      Type: AWS::EC2::VPC
      DependsOn: InternetGateway
      Properties:
        CidrBlock: 10.0.0.0/16
        EnableDnsSupport: true
        EnableDnsHostnames: true

    # Public Subnets (AZ1 and AZ2)
    AZ1PublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 0
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: 10.0.0.0/18
        MapPublicIpOnLaunch: true

    AZ2PublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 1
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: *********/18
        MapPublicIpOnLaunch: true

    # Internet Gateway
    InternetGateway:
      Type: AWS::EC2::InternetGateway

    GatewayAttachment:
      Type: AWS::EC2::VPCGatewayAttachment
      Properties:
        VpcId: !Ref "VPC"
        InternetGatewayId: !Ref "InternetGateway"

    # Security Group for instances
    InstanceSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Allow HTTP and HTTPS traffic
        GroupName: ${self:service}-${self:provider.stage}-sg
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0  # Allow all inbound traffic (you may want to restrict this)
        SecurityGroupEgress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0  # Allow all outbound traffic

    # Application Load Balancer (ALB)
    ApplicationLoadBalancer:
      Type: AWS::ElasticLoadBalancingV2::LoadBalancer
      Properties:
        Name: "${self:service}-${self:provider.stage}-alb"
        Type: "application"
        Scheme: "internet-facing"
        Subnets:
          - !Ref AZ1PublicSubnet1
          - !Ref AZ2PublicSubnet1
        SecurityGroups:
          - !Ref InstanceSecurityGroup

    # Listener for HTTP traffic (using HTTP only instead of HTTPS)
    LoadBalancerListener:
      Type: AWS::ElasticLoadBalancingV2::Listener
      DependsOn:
        - ApplicationLoadBalancer
      Properties:
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Protocol: HTTP  # Use HTTP (no HTTPS)
        Port: 80
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref ElsaTargetGroup

    # Target Group for ALB routing
    ElsaTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      DependsOn: ApplicationLoadBalancer
      Properties:
        Name: "${self:service}-${self:provider.stage}-tg"
        Port: 80
        Protocol: HTTP
        VpcId: !Ref VPC
        HealthCheckEnabled: true
        HealthCheckIntervalSeconds: 60
        HealthCheckPath: "/readiness"
        HealthCheckPort: "traffic-port"
        HealthCheckProtocol: HTTP
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 2
        IpAddressType: "ipv4"
        ProtocolVersion: "HTTP1"
        Matcher:
          HttpCode: "200"
        TargetType: "ip"

    # ECS Task Definition (for FastAPI app container)
    TaskDefinition:
      Type: AWS::ECS::TaskDefinition
      DependsOn:
        - CloudWatchLogsGroup
      Properties:
        Family: ${self:service}-${self:provider.stage}
        NetworkMode: awsvpc
        RequiresCompatibilities:
          - FARGATE
        ExecutionRoleArn: !GetAtt ECRECSExecutionRole.Arn
        TaskRoleArn: !GetAtt ECRECSExecutionRole.Arn
        Cpu: 256
        Memory: 512
        ContainerDefinitions:
          - Name: ${self:service}-${self:provider.stage}
            Image: ${self:custom.ecrRepoUri}
            Memory: 512
            Cpu: 256
            Essential: true
            PortMappings:
              - ContainerPort: 8000  # FastAPI default port
                Protocol: "tcp"
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "${self:service}-${self:provider.stage}"

    # ECS Service to manage running containers
    ECRECSService:
      Type: AWS::ECS::Service
      DependsOn:
        - TaskDefinition
        - ElsaTargetGroup
        - ApplicationLoadBalancer
        - LoadBalancerListener
      Properties:
        Cluster: !Ref ECSCluster
        DesiredCount: 1
        TaskDefinition: !Ref TaskDefinition
        ServiceName: ${self:service}-${self:provider.stage}-service
        PlatformVersion: LATEST
        EnableECSManagedTags: true
        NetworkConfiguration:
          AwsvpcConfiguration:
            SecurityGroups:
              - !Ref InstanceSecurityGroup
            Subnets:
              - !Ref AZ1PublicSubnet1
              - !Ref AZ2PublicSubnet1
        LoadBalancers:
          - ContainerName: ${self:service}-${self:provider.stage}
            ContainerPort: 8000
            TargetGroupArn: !Ref ElsaTargetGroup
