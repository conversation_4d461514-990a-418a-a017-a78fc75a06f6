from fastapi import APIRouter, Depends
from middleware_app.adapters.auth_adapter import get_api_key
from middleware_app.services.webchat import initiate_chat_webchat, send_message_webchat
from middleware_app.services.rozie_studio import initiate_rozie_studio_skill, message_rozie_studio_skill
from middleware_app.services.rozie_studio_navigator import rozie_studio_navigator_call
from middleware_app.helpers.concept_helper import update_previous_interaction_concept, update_middleware_response


router = APIRouter()

@router.post("/initiate", dependencies=[Depends(get_api_key)])
def initiate_chat_request_router(body: dict):

    chat_id = body.get("chat_id")
    customer_details = body.get("customer_details")
    channel_id = body.get("channel_id")
    channel_name = body.get("channel_name")
    framework = body.get("framework_to_used")

    if framework == "multi_agent_framework":
        rosters_id = body.get("rosters_id")
        result = initiate_chat_webchat(
            chat_id, customer_details, rosters_id, channel_id, channel_name
        )
    if framework == "rozie_studio":
        skill_id = body.get("skill_id")
        concept_name = body.get("concept_name")
        customer_message = body.get("customer_message")
        result = initiate_rozie_studio_skill(
            chat_id,
            channel_id,
            channel_name,
            customer_details,
            customer_message,
            skill_id,
            concept_name,
        )
        for response in result["response_map"]["responses"]["default"]:
            if response["response_template"].get("metadata", {}).get("context.concept_id"):
                print(response["response_template"]["metadata"]["context.concept_id"])
                update_previous_interaction_concept(
                    chat_id,
                    response["response_template"]["metadata"]["context.concept_id"]
                )
    if framework == "rozie_studio_navigator":
        customer_message = body.get("customer_message")
        result = rozie_studio_navigator_call(
            chat_id, channel_id, channel_name, customer_details, customer_message
        )

    update_middleware_response(result, chat_id)
    return result


@router.post("/send_message", dependencies=[Depends(get_api_key)])
def send_message_request_router(body: dict):
    chat_id = body.get("chat_id")
    customer_details = body.get("customer_details")
    channel_id = body.get("channel_id")
    channel_name = body.get("channel_name")
    framework = body.get("framework_to_used")

    if framework == "multi_agent_framework":
        customer_message = body.get("customer_message")
        result = send_message_webchat(
            chat_id, customer_details, channel_id, channel_name, customer_message
        )
    if framework == "rozie_studio":
        customer_message = body.get("customer_message")
        result = message_rozie_studio_skill(
            chat_id,
            channel_id,
            channel_name,
            customer_details,
            customer_message
        )
        for response in result["response_map"]["responses"]["default"]:
            if response["response_template"].get("metadata", {}).get("context.concept_id"):
                print(response["response_template"]["metadata"]["context.concept_id"])
                update_previous_interaction_concept(
                    chat_id,
                    response["response_template"]["metadata"]["context.concept_id"]
                )
    if framework == "rozie_studio_navigator":
        customer_message = body.get("customer_message")
        result = rozie_studio_navigator_call(
            chat_id, channel_id, channel_name, customer_details, customer_message
        )

    update_middleware_response(result, chat_id)
    return result
