###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import typing
from baml_py.baml_py import FieldType, EnumV<PERSON>ue<PERSON>uilder, Enum<PERSON>uilder, ClassBuilder
from baml_py.type_builder import <PERSON><PERSON><PERSON><PERSON> as _<PERSON><PERSON><PERSON><PERSON>, Class<PERSON>ropertyBuilder, ClassPropertyViewer, <PERSON>um<PERSON><PERSON>ue<PERSON>iewer
from .globals import DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME


class TypeBuilder(_TypeBuilder):
    def __init__(self):
        super().__init__(classes=set(
          ["AccidentClassification","AgentObtainableInformation","AlternativeScenario","AnalysisColorScheme","AnalysisDetails","AnalysisFlow","AnalysisFlowStep","AnalysisQuality","AttachmentAnalysis","AttachmentInfo","CanLIILegalPrecedent","CanadianLegalAnalysis","CareAssistanceAssessment","CauseOfLoss","CauseOfLossMapping","ChartAnnotation","ChartDataPoint","CircumstanceDetails","ClaimDetails","ClaimDocumentInput","ClaimIndicators","ClaimantInformationNeeded","ColorLegend","ColorSchemeMapping","CommunicationAction","ConfidenceBreakdown","ConfidenceColorScheme","ConfidenceContributor","ConfidenceDistribution","ContactInfo","ContextualFactors","ContradictoryEvidence","ContributoryAnalysis","CoverageAnalysisDetails","CoverageDecisionSupport","CoverageJustification","CoverageMapping","DataCompletenessAssessment","DataFlowStep","DataItem","DecisionPoint","DiagnosticResult","DocumentHighlight","DocumentationAction","DocumentationQuality","EmailAttachment","EmailForClassification","EntityMapping","EntityTypeColor","EnvironmentalFactors","EvidenceQuality","EvidenceWeighting","ExclusionAnalysis","ExitAnalysis","ExpertOpinion","ExplainabilityInsight","ExplanationFactor","FaultGuidance","FaultImpactOnQuantum","FeatureImportance","FieldConfidence","FieldConfidenceBreakdown","FutureCareAssessment","GeneralDamagesAssessment","HighlightRegion","IncomeLossAssessment","InformationRequest","InjuryDetail","InjuryImpactAnalysis","InteractiveElement","InvestigationAction","LegalAction","Level01Analysis","Level01Summary","Level02AnalysisInput","Level02CoverageAnalysis","Level02ExitAnalysis","Level02NextSteps","Level02RiskAssessment","Level03AnalysisInput","Level04AnalysisInput","LiabilityFactorExtraction","MedicalDamageAssessment","MedicalImpactAssessment","MedicalSummary","MedicalTimeline","NegligenceAnalysis","NextSteps","OntarioIncomeLossStandards","OntarioMedicalGuidelines","OntarioPainSufferingGuide","OntarioQuantumBenchmarks","OntarioStatutoryThresholds","PolicyCoverageAnalysis","PolicyDetails","PolicyExclusion","PriorityRiskAssessment","QuantumCalculationGuidance","QuantumDamageExtraction","QuantumValidationChecks","Resume","RiskAssessment","RiskFactor","SparkNlpDateEntity","SparkNlpEnhancedData","SparkNlpEntity","SparkNlpFinancialEntity","SparkNlpLocationEntity","SpecialDamagesBreakdown","StructuredCircumstances","SupportingEvidence","ThirdPartyInformationNeeded","TreatmentRecord","UIMetadata","UncertaintyArea","VisualizationData","ZurichEmailClassificationResult",]
        ), enums=set(
          ["AttachmentMismatchType","CanadianProvince","ClaimType","ClaimTypeDetailed","ContactRole","CoverageDecision","CoverageReason","ExitPath","IncidentType","InformationSource","InjurySeverity","MedicalComplexity","PriorityLevel","RiskCategory","RiskLevel","TreatmentType","UrgencyLevel",]
        ), runtime=DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME)


    @property
    def AccidentClassification(self) -> "AccidentClassificationAst":
        return AccidentClassificationAst(self)

    @property
    def AgentObtainableInformation(self) -> "AgentObtainableInformationAst":
        return AgentObtainableInformationAst(self)

    @property
    def AlternativeScenario(self) -> "AlternativeScenarioAst":
        return AlternativeScenarioAst(self)

    @property
    def AnalysisColorScheme(self) -> "AnalysisColorSchemeAst":
        return AnalysisColorSchemeAst(self)

    @property
    def AnalysisDetails(self) -> "AnalysisDetailsAst":
        return AnalysisDetailsAst(self)

    @property
    def AnalysisFlow(self) -> "AnalysisFlowAst":
        return AnalysisFlowAst(self)

    @property
    def AnalysisFlowStep(self) -> "AnalysisFlowStepAst":
        return AnalysisFlowStepAst(self)

    @property
    def AnalysisQuality(self) -> "AnalysisQualityAst":
        return AnalysisQualityAst(self)

    @property
    def AttachmentAnalysis(self) -> "AttachmentAnalysisAst":
        return AttachmentAnalysisAst(self)

    @property
    def AttachmentInfo(self) -> "AttachmentInfoAst":
        return AttachmentInfoAst(self)

    @property
    def CanLIILegalPrecedent(self) -> "CanLIILegalPrecedentAst":
        return CanLIILegalPrecedentAst(self)

    @property
    def CanadianLegalAnalysis(self) -> "CanadianLegalAnalysisAst":
        return CanadianLegalAnalysisAst(self)

    @property
    def CareAssistanceAssessment(self) -> "CareAssistanceAssessmentAst":
        return CareAssistanceAssessmentAst(self)

    @property
    def CauseOfLoss(self) -> "CauseOfLossAst":
        return CauseOfLossAst(self)

    @property
    def CauseOfLossMapping(self) -> "CauseOfLossMappingAst":
        return CauseOfLossMappingAst(self)

    @property
    def ChartAnnotation(self) -> "ChartAnnotationAst":
        return ChartAnnotationAst(self)

    @property
    def ChartDataPoint(self) -> "ChartDataPointAst":
        return ChartDataPointAst(self)

    @property
    def CircumstanceDetails(self) -> "CircumstanceDetailsAst":
        return CircumstanceDetailsAst(self)

    @property
    def ClaimDetails(self) -> "ClaimDetailsAst":
        return ClaimDetailsAst(self)

    @property
    def ClaimDocumentInput(self) -> "ClaimDocumentInputAst":
        return ClaimDocumentInputAst(self)

    @property
    def ClaimIndicators(self) -> "ClaimIndicatorsAst":
        return ClaimIndicatorsAst(self)

    @property
    def ClaimantInformationNeeded(self) -> "ClaimantInformationNeededAst":
        return ClaimantInformationNeededAst(self)

    @property
    def ColorLegend(self) -> "ColorLegendAst":
        return ColorLegendAst(self)

    @property
    def ColorSchemeMapping(self) -> "ColorSchemeMappingAst":
        return ColorSchemeMappingAst(self)

    @property
    def CommunicationAction(self) -> "CommunicationActionAst":
        return CommunicationActionAst(self)

    @property
    def ConfidenceBreakdown(self) -> "ConfidenceBreakdownAst":
        return ConfidenceBreakdownAst(self)

    @property
    def ConfidenceColorScheme(self) -> "ConfidenceColorSchemeAst":
        return ConfidenceColorSchemeAst(self)

    @property
    def ConfidenceContributor(self) -> "ConfidenceContributorAst":
        return ConfidenceContributorAst(self)

    @property
    def ConfidenceDistribution(self) -> "ConfidenceDistributionAst":
        return ConfidenceDistributionAst(self)

    @property
    def ContactInfo(self) -> "ContactInfoAst":
        return ContactInfoAst(self)

    @property
    def ContextualFactors(self) -> "ContextualFactorsAst":
        return ContextualFactorsAst(self)

    @property
    def ContradictoryEvidence(self) -> "ContradictoryEvidenceAst":
        return ContradictoryEvidenceAst(self)

    @property
    def ContributoryAnalysis(self) -> "ContributoryAnalysisAst":
        return ContributoryAnalysisAst(self)

    @property
    def CoverageAnalysisDetails(self) -> "CoverageAnalysisDetailsAst":
        return CoverageAnalysisDetailsAst(self)

    @property
    def CoverageDecisionSupport(self) -> "CoverageDecisionSupportAst":
        return CoverageDecisionSupportAst(self)

    @property
    def CoverageJustification(self) -> "CoverageJustificationAst":
        return CoverageJustificationAst(self)

    @property
    def CoverageMapping(self) -> "CoverageMappingAst":
        return CoverageMappingAst(self)

    @property
    def DataCompletenessAssessment(self) -> "DataCompletenessAssessmentAst":
        return DataCompletenessAssessmentAst(self)

    @property
    def DataFlowStep(self) -> "DataFlowStepAst":
        return DataFlowStepAst(self)

    @property
    def DataItem(self) -> "DataItemAst":
        return DataItemAst(self)

    @property
    def DecisionPoint(self) -> "DecisionPointAst":
        return DecisionPointAst(self)

    @property
    def DiagnosticResult(self) -> "DiagnosticResultAst":
        return DiagnosticResultAst(self)

    @property
    def DocumentHighlight(self) -> "DocumentHighlightAst":
        return DocumentHighlightAst(self)

    @property
    def DocumentationAction(self) -> "DocumentationActionAst":
        return DocumentationActionAst(self)

    @property
    def DocumentationQuality(self) -> "DocumentationQualityAst":
        return DocumentationQualityAst(self)

    @property
    def EmailAttachment(self) -> "EmailAttachmentAst":
        return EmailAttachmentAst(self)

    @property
    def EmailForClassification(self) -> "EmailForClassificationAst":
        return EmailForClassificationAst(self)

    @property
    def EntityMapping(self) -> "EntityMappingAst":
        return EntityMappingAst(self)

    @property
    def EntityTypeColor(self) -> "EntityTypeColorAst":
        return EntityTypeColorAst(self)

    @property
    def EnvironmentalFactors(self) -> "EnvironmentalFactorsAst":
        return EnvironmentalFactorsAst(self)

    @property
    def EvidenceQuality(self) -> "EvidenceQualityAst":
        return EvidenceQualityAst(self)

    @property
    def EvidenceWeighting(self) -> "EvidenceWeightingAst":
        return EvidenceWeightingAst(self)

    @property
    def ExclusionAnalysis(self) -> "ExclusionAnalysisAst":
        return ExclusionAnalysisAst(self)

    @property
    def ExitAnalysis(self) -> "ExitAnalysisAst":
        return ExitAnalysisAst(self)

    @property
    def ExpertOpinion(self) -> "ExpertOpinionAst":
        return ExpertOpinionAst(self)

    @property
    def ExplainabilityInsight(self) -> "ExplainabilityInsightAst":
        return ExplainabilityInsightAst(self)

    @property
    def ExplanationFactor(self) -> "ExplanationFactorAst":
        return ExplanationFactorAst(self)

    @property
    def FaultGuidance(self) -> "FaultGuidanceAst":
        return FaultGuidanceAst(self)

    @property
    def FaultImpactOnQuantum(self) -> "FaultImpactOnQuantumAst":
        return FaultImpactOnQuantumAst(self)

    @property
    def FeatureImportance(self) -> "FeatureImportanceAst":
        return FeatureImportanceAst(self)

    @property
    def FieldConfidence(self) -> "FieldConfidenceAst":
        return FieldConfidenceAst(self)

    @property
    def FieldConfidenceBreakdown(self) -> "FieldConfidenceBreakdownAst":
        return FieldConfidenceBreakdownAst(self)

    @property
    def FutureCareAssessment(self) -> "FutureCareAssessmentAst":
        return FutureCareAssessmentAst(self)

    @property
    def GeneralDamagesAssessment(self) -> "GeneralDamagesAssessmentAst":
        return GeneralDamagesAssessmentAst(self)

    @property
    def HighlightRegion(self) -> "HighlightRegionAst":
        return HighlightRegionAst(self)

    @property
    def IncomeLossAssessment(self) -> "IncomeLossAssessmentAst":
        return IncomeLossAssessmentAst(self)

    @property
    def InformationRequest(self) -> "InformationRequestAst":
        return InformationRequestAst(self)

    @property
    def InjuryDetail(self) -> "InjuryDetailAst":
        return InjuryDetailAst(self)

    @property
    def InjuryImpactAnalysis(self) -> "InjuryImpactAnalysisAst":
        return InjuryImpactAnalysisAst(self)

    @property
    def InteractiveElement(self) -> "InteractiveElementAst":
        return InteractiveElementAst(self)

    @property
    def InvestigationAction(self) -> "InvestigationActionAst":
        return InvestigationActionAst(self)

    @property
    def LegalAction(self) -> "LegalActionAst":
        return LegalActionAst(self)

    @property
    def Level01Analysis(self) -> "Level01AnalysisAst":
        return Level01AnalysisAst(self)

    @property
    def Level01Summary(self) -> "Level01SummaryAst":
        return Level01SummaryAst(self)

    @property
    def Level02AnalysisInput(self) -> "Level02AnalysisInputAst":
        return Level02AnalysisInputAst(self)

    @property
    def Level02CoverageAnalysis(self) -> "Level02CoverageAnalysisAst":
        return Level02CoverageAnalysisAst(self)

    @property
    def Level02ExitAnalysis(self) -> "Level02ExitAnalysisAst":
        return Level02ExitAnalysisAst(self)

    @property
    def Level02NextSteps(self) -> "Level02NextStepsAst":
        return Level02NextStepsAst(self)

    @property
    def Level02RiskAssessment(self) -> "Level02RiskAssessmentAst":
        return Level02RiskAssessmentAst(self)

    @property
    def Level03AnalysisInput(self) -> "Level03AnalysisInputAst":
        return Level03AnalysisInputAst(self)

    @property
    def Level04AnalysisInput(self) -> "Level04AnalysisInputAst":
        return Level04AnalysisInputAst(self)

    @property
    def LiabilityFactorExtraction(self) -> "LiabilityFactorExtractionAst":
        return LiabilityFactorExtractionAst(self)

    @property
    def MedicalDamageAssessment(self) -> "MedicalDamageAssessmentAst":
        return MedicalDamageAssessmentAst(self)

    @property
    def MedicalImpactAssessment(self) -> "MedicalImpactAssessmentAst":
        return MedicalImpactAssessmentAst(self)

    @property
    def MedicalSummary(self) -> "MedicalSummaryAst":
        return MedicalSummaryAst(self)

    @property
    def MedicalTimeline(self) -> "MedicalTimelineAst":
        return MedicalTimelineAst(self)

    @property
    def NegligenceAnalysis(self) -> "NegligenceAnalysisAst":
        return NegligenceAnalysisAst(self)

    @property
    def NextSteps(self) -> "NextStepsAst":
        return NextStepsAst(self)

    @property
    def OntarioIncomeLossStandards(self) -> "OntarioIncomeLossStandardsAst":
        return OntarioIncomeLossStandardsAst(self)

    @property
    def OntarioMedicalGuidelines(self) -> "OntarioMedicalGuidelinesAst":
        return OntarioMedicalGuidelinesAst(self)

    @property
    def OntarioPainSufferingGuide(self) -> "OntarioPainSufferingGuideAst":
        return OntarioPainSufferingGuideAst(self)

    @property
    def OntarioQuantumBenchmarks(self) -> "OntarioQuantumBenchmarksAst":
        return OntarioQuantumBenchmarksAst(self)

    @property
    def OntarioStatutoryThresholds(self) -> "OntarioStatutoryThresholdsAst":
        return OntarioStatutoryThresholdsAst(self)

    @property
    def PolicyCoverageAnalysis(self) -> "PolicyCoverageAnalysisAst":
        return PolicyCoverageAnalysisAst(self)

    @property
    def PolicyDetails(self) -> "PolicyDetailsAst":
        return PolicyDetailsAst(self)

    @property
    def PolicyExclusion(self) -> "PolicyExclusionAst":
        return PolicyExclusionAst(self)

    @property
    def PriorityRiskAssessment(self) -> "PriorityRiskAssessmentAst":
        return PriorityRiskAssessmentAst(self)

    @property
    def QuantumCalculationGuidance(self) -> "QuantumCalculationGuidanceAst":
        return QuantumCalculationGuidanceAst(self)

    @property
    def QuantumDamageExtraction(self) -> "QuantumDamageExtractionAst":
        return QuantumDamageExtractionAst(self)

    @property
    def QuantumValidationChecks(self) -> "QuantumValidationChecksAst":
        return QuantumValidationChecksAst(self)

    @property
    def Resume(self) -> "ResumeAst":
        return ResumeAst(self)

    @property
    def RiskAssessment(self) -> "RiskAssessmentAst":
        return RiskAssessmentAst(self)

    @property
    def RiskFactor(self) -> "RiskFactorAst":
        return RiskFactorAst(self)

    @property
    def SparkNlpDateEntity(self) -> "SparkNlpDateEntityAst":
        return SparkNlpDateEntityAst(self)

    @property
    def SparkNlpEnhancedData(self) -> "SparkNlpEnhancedDataAst":
        return SparkNlpEnhancedDataAst(self)

    @property
    def SparkNlpEntity(self) -> "SparkNlpEntityAst":
        return SparkNlpEntityAst(self)

    @property
    def SparkNlpFinancialEntity(self) -> "SparkNlpFinancialEntityAst":
        return SparkNlpFinancialEntityAst(self)

    @property
    def SparkNlpLocationEntity(self) -> "SparkNlpLocationEntityAst":
        return SparkNlpLocationEntityAst(self)

    @property
    def SpecialDamagesBreakdown(self) -> "SpecialDamagesBreakdownAst":
        return SpecialDamagesBreakdownAst(self)

    @property
    def StructuredCircumstances(self) -> "StructuredCircumstancesAst":
        return StructuredCircumstancesAst(self)

    @property
    def SupportingEvidence(self) -> "SupportingEvidenceAst":
        return SupportingEvidenceAst(self)

    @property
    def ThirdPartyInformationNeeded(self) -> "ThirdPartyInformationNeededAst":
        return ThirdPartyInformationNeededAst(self)

    @property
    def TreatmentRecord(self) -> "TreatmentRecordAst":
        return TreatmentRecordAst(self)

    @property
    def UIMetadata(self) -> "UIMetadataAst":
        return UIMetadataAst(self)

    @property
    def UncertaintyArea(self) -> "UncertaintyAreaAst":
        return UncertaintyAreaAst(self)

    @property
    def VisualizationData(self) -> "VisualizationDataAst":
        return VisualizationDataAst(self)

    @property
    def ZurichEmailClassificationResult(self) -> "ZurichEmailClassificationResultAst":
        return ZurichEmailClassificationResultAst(self)





class AccidentClassificationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AccidentClassification")
        self._properties: typing.Set[str] = set([ "primaryType",  "specificSubtype",  "incidentComplexity",  "applicableLaw",  "certaintyLevel", ])
        self._props = AccidentClassificationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AccidentClassificationProperties":
        return self._props


class AccidentClassificationViewer(AccidentClassificationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AccidentClassificationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def primaryType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("primaryType"))

    @property
    def specificSubtype(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("specificSubtype"))

    @property
    def incidentComplexity(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incidentComplexity"))

    @property
    def applicableLaw(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("applicableLaw"))

    @property
    def certaintyLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("certaintyLevel"))

    

class AgentObtainableInformationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AgentObtainableInformation")
        self._properties: typing.Set[str] = set([ "informationType",  "sourceOfInformation",  "procedureRequired",  "timelineToObtain",  "costToCompany",  "reliabilityLevel", ])
        self._props = AgentObtainableInformationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AgentObtainableInformationProperties":
        return self._props


class AgentObtainableInformationViewer(AgentObtainableInformationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AgentObtainableInformationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def informationType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("informationType"))

    @property
    def sourceOfInformation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sourceOfInformation"))

    @property
    def procedureRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("procedureRequired"))

    @property
    def timelineToObtain(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timelineToObtain"))

    @property
    def costToCompany(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("costToCompany"))

    @property
    def reliabilityLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reliabilityLevel"))

    

class AlternativeScenarioAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AlternativeScenario")
        self._properties: typing.Set[str] = set([ "scenario",  "probabilityChange",  "requiredChanges",  "likelihood",  "implications", ])
        self._props = AlternativeScenarioProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AlternativeScenarioProperties":
        return self._props


class AlternativeScenarioViewer(AlternativeScenarioAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AlternativeScenarioProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def scenario(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("scenario"))

    @property
    def probabilityChange(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("probabilityChange"))

    @property
    def requiredChanges(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requiredChanges"))

    @property
    def likelihood(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("likelihood"))

    @property
    def implications(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("implications"))

    

class AnalysisColorSchemeAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AnalysisColorScheme")
        self._properties: typing.Set[str] = set([ "criticalColor",  "highPriorityColor",  "mediumPriorityColor",  "lowPriorityColor", ])
        self._props = AnalysisColorSchemeProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AnalysisColorSchemeProperties":
        return self._props


class AnalysisColorSchemeViewer(AnalysisColorSchemeAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AnalysisColorSchemeProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def criticalColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("criticalColor"))

    @property
    def highPriorityColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("highPriorityColor"))

    @property
    def mediumPriorityColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("mediumPriorityColor"))

    @property
    def lowPriorityColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("lowPriorityColor"))

    

class AnalysisDetailsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AnalysisDetails")
        self._properties: typing.Set[str] = set([ "extractedInformation",  "confidenceBySection",  "identifiedGaps",  "assumptionsMade",  "qualityAssessment",  "riskIndicators", ])
        self._props = AnalysisDetailsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AnalysisDetailsProperties":
        return self._props


class AnalysisDetailsViewer(AnalysisDetailsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AnalysisDetailsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def extractedInformation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("extractedInformation"))

    @property
    def confidenceBySection(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceBySection"))

    @property
    def identifiedGaps(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("identifiedGaps"))

    @property
    def assumptionsMade(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("assumptionsMade"))

    @property
    def qualityAssessment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("qualityAssessment"))

    @property
    def riskIndicators(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("riskIndicators"))

    

class AnalysisFlowAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AnalysisFlow")
        self._properties: typing.Set[str] = set([ "flowSteps",  "decisionPoints",  "dataFlow",  "alternativeScenarios", ])
        self._props = AnalysisFlowProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AnalysisFlowProperties":
        return self._props


class AnalysisFlowViewer(AnalysisFlowAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AnalysisFlowProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def flowSteps(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("flowSteps"))

    @property
    def decisionPoints(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("decisionPoints"))

    @property
    def dataFlow(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("dataFlow"))

    @property
    def alternativeScenarios(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("alternativeScenarios"))

    

class AnalysisFlowStepAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AnalysisFlowStep")
        self._properties: typing.Set[str] = set([ "stepNumber",  "stepName",  "stepDescription",  "inputData",  "outcome",  "nextSteps",  "confidence", ])
        self._props = AnalysisFlowStepProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AnalysisFlowStepProperties":
        return self._props


class AnalysisFlowStepViewer(AnalysisFlowStepAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AnalysisFlowStepProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def stepNumber(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("stepNumber"))

    @property
    def stepName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("stepName"))

    @property
    def stepDescription(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("stepDescription"))

    @property
    def inputData(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("inputData"))

    @property
    def outcome(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("outcome"))

    @property
    def nextSteps(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("nextSteps"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    

class AnalysisQualityAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AnalysisQuality")
        self._properties: typing.Set[str] = set([ "dataCompleteness",  "dataReliability",  "analysisDepth",  "analysisConsistency",  "expertValidation",  "overallQuality",  "qualityNotes", ])
        self._props = AnalysisQualityProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AnalysisQualityProperties":
        return self._props


class AnalysisQualityViewer(AnalysisQualityAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AnalysisQualityProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def dataCompleteness(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("dataCompleteness"))

    @property
    def dataReliability(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("dataReliability"))

    @property
    def analysisDepth(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisDepth"))

    @property
    def analysisConsistency(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisConsistency"))

    @property
    def expertValidation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("expertValidation"))

    @property
    def overallQuality(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overallQuality"))

    @property
    def qualityNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("qualityNotes"))

    

class AttachmentAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AttachmentAnalysis")
        self._properties: typing.Set[str] = set([ "hasAttachments",  "attachmentCount",  "attachments",  "mentionsAttachments",  "attachmentMismatch",  "suspiciousFilenames", ])
        self._props = AttachmentAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AttachmentAnalysisProperties":
        return self._props


class AttachmentAnalysisViewer(AttachmentAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AttachmentAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def hasAttachments(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("hasAttachments"))

    @property
    def attachmentCount(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachmentCount"))

    @property
    def attachments(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachments"))

    @property
    def mentionsAttachments(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("mentionsAttachments"))

    @property
    def attachmentMismatch(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachmentMismatch"))

    @property
    def suspiciousFilenames(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("suspiciousFilenames"))

    

class AttachmentInfoAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AttachmentInfo")
        self._properties: typing.Set[str] = set([ "filename",  "fileType",  "isClaimRelated",  "documentType", ])
        self._props = AttachmentInfoProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AttachmentInfoProperties":
        return self._props


class AttachmentInfoViewer(AttachmentInfoAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AttachmentInfoProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def filename(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("filename"))

    @property
    def fileType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("fileType"))

    @property
    def isClaimRelated(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("isClaimRelated"))

    @property
    def documentType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentType"))

    

class CanLIILegalPrecedentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CanLIILegalPrecedent")
        self._properties: typing.Set[str] = set([ "caseId",  "caseName",  "court",  "jurisdiction",  "decisionDate",  "relevanceToCase",  "keyPrinciple",  "supportsCoverage",  "confidenceLevel",  "canliiUrl", ])
        self._props = CanLIILegalPrecedentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CanLIILegalPrecedentProperties":
        return self._props


class CanLIILegalPrecedentViewer(CanLIILegalPrecedentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CanLIILegalPrecedentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def caseId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("caseId"))

    @property
    def caseName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("caseName"))

    @property
    def court(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("court"))

    @property
    def jurisdiction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("jurisdiction"))

    @property
    def decisionDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("decisionDate"))

    @property
    def relevanceToCase(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("relevanceToCase"))

    @property
    def keyPrinciple(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("keyPrinciple"))

    @property
    def supportsCoverage(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("supportsCoverage"))

    @property
    def confidenceLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceLevel"))

    @property
    def canliiUrl(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("canliiUrl"))

    

class CanadianLegalAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CanadianLegalAnalysis")
        self._properties: typing.Set[str] = set([ "applicableLaw",  "provincialRegulations",  "federalStatutes",  "legalPrecedents",  "regulatoryGuidance",  "interpretationNotes",  "jurisdictionalCompliance",  "legalRiskAssessment", ])
        self._props = CanadianLegalAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CanadianLegalAnalysisProperties":
        return self._props


class CanadianLegalAnalysisViewer(CanadianLegalAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CanadianLegalAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def applicableLaw(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("applicableLaw"))

    @property
    def provincialRegulations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("provincialRegulations"))

    @property
    def federalStatutes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("federalStatutes"))

    @property
    def legalPrecedents(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalPrecedents"))

    @property
    def regulatoryGuidance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("regulatoryGuidance"))

    @property
    def interpretationNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("interpretationNotes"))

    @property
    def jurisdictionalCompliance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("jurisdictionalCompliance"))

    @property
    def legalRiskAssessment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalRiskAssessment"))

    

class CareAssistanceAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CareAssistanceAssessment")
        self._properties: typing.Set[str] = set([ "familyCareProvided",  "familyCareHours",  "familyCareValue",  "professionalCareRequired",  "professionalCareHours",  "professionalCareCosts",  "housekeepingAssistance",  "housekeepingCosts",  "personalCareNeeds",  "careAssistanceDuration",  "totalCareAssistanceCosts", ])
        self._props = CareAssistanceAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CareAssistanceAssessmentProperties":
        return self._props


class CareAssistanceAssessmentViewer(CareAssistanceAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CareAssistanceAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def familyCareProvided(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("familyCareProvided"))

    @property
    def familyCareHours(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("familyCareHours"))

    @property
    def familyCareValue(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("familyCareValue"))

    @property
    def professionalCareRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("professionalCareRequired"))

    @property
    def professionalCareHours(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("professionalCareHours"))

    @property
    def professionalCareCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("professionalCareCosts"))

    @property
    def housekeepingAssistance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("housekeepingAssistance"))

    @property
    def housekeepingCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("housekeepingCosts"))

    @property
    def personalCareNeeds(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("personalCareNeeds"))

    @property
    def careAssistanceDuration(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("careAssistanceDuration"))

    @property
    def totalCareAssistanceCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("totalCareAssistanceCosts"))

    

class CauseOfLossAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CauseOfLoss")
        self._properties: typing.Set[str] = set([ "primaryCause",  "contributingFactors",  "incidentType",  "atFaultParties",  "circumstances",  "weatherConditions",  "roadConditions",  "trafficViolations",  "negligenceFactors",  "causationChain", ])
        self._props = CauseOfLossProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CauseOfLossProperties":
        return self._props


class CauseOfLossViewer(CauseOfLossAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CauseOfLossProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def primaryCause(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("primaryCause"))

    @property
    def contributingFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contributingFactors"))

    @property
    def incidentType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incidentType"))

    @property
    def atFaultParties(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("atFaultParties"))

    @property
    def circumstances(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("circumstances"))

    @property
    def weatherConditions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("weatherConditions"))

    @property
    def roadConditions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("roadConditions"))

    @property
    def trafficViolations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("trafficViolations"))

    @property
    def negligenceFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("negligenceFactors"))

    @property
    def causationChain(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("causationChain"))

    

class CauseOfLossMappingAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CauseOfLossMapping")
        self._properties: typing.Set[str] = set([ "primaryCause",  "proximateCause",  "coverageApplicability",  "causationChain",  "concurrentCauses",  "causationAnalysis",  "canadianCausationLaw", ])
        self._props = CauseOfLossMappingProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CauseOfLossMappingProperties":
        return self._props


class CauseOfLossMappingViewer(CauseOfLossMappingAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CauseOfLossMappingProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def primaryCause(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("primaryCause"))

    @property
    def proximateCause(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("proximateCause"))

    @property
    def coverageApplicability(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageApplicability"))

    @property
    def causationChain(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("causationChain"))

    @property
    def concurrentCauses(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("concurrentCauses"))

    @property
    def causationAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("causationAnalysis"))

    @property
    def canadianCausationLaw(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("canadianCausationLaw"))

    

class ChartAnnotationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ChartAnnotation")
        self._properties: typing.Set[str] = set([ "text",  "position",  "style",  "importance",  "analysisRelevance", ])
        self._props = ChartAnnotationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ChartAnnotationProperties":
        return self._props


class ChartAnnotationViewer(ChartAnnotationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ChartAnnotationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def text(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("text"))

    @property
    def position(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("position"))

    @property
    def style(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("style"))

    @property
    def importance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("importance"))

    @property
    def analysisRelevance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisRelevance"))

    

class ChartDataPointAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ChartDataPoint")
        self._properties: typing.Set[str] = set([ "label",  "value",  "color",  "metadata",  "tooltip",  "analysisImpact", ])
        self._props = ChartDataPointProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ChartDataPointProperties":
        return self._props


class ChartDataPointViewer(ChartDataPointAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ChartDataPointProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def label(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("label"))

    @property
    def value(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("value"))

    @property
    def color(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("color"))

    @property
    def metadata(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("metadata"))

    @property
    def tooltip(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("tooltip"))

    @property
    def analysisImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisImpact"))

    

class CircumstanceDetailsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CircumstanceDetails")
        self._properties: typing.Set[str] = set([ "environmentalFactors",  "locationDetails",  "timingFactors",  "humanBehaviorFactors",  "equipmentConditions", ])
        self._props = CircumstanceDetailsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CircumstanceDetailsProperties":
        return self._props


class CircumstanceDetailsViewer(CircumstanceDetailsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CircumstanceDetailsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def environmentalFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("environmentalFactors"))

    @property
    def locationDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("locationDetails"))

    @property
    def timingFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timingFactors"))

    @property
    def humanBehaviorFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("humanBehaviorFactors"))

    @property
    def equipmentConditions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("equipmentConditions"))

    

class ClaimDetailsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ClaimDetails")
        self._properties: typing.Set[str] = set([ "incidentDate",  "incidentTime",  "incidentLocation",  "claimantName",  "insuredParty",  "claimType",  "damageDescription",  "estimatedAmount",  "policeReportNumber",  "emergencyServicesInvolved",  "injuriesReported",  "propertyDamage",  "witnessesPresent",  "vehicleInvolved",  "thirdPartyInvolved",  "medicalInformation", ])
        self._props = ClaimDetailsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ClaimDetailsProperties":
        return self._props


class ClaimDetailsViewer(ClaimDetailsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ClaimDetailsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def incidentDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incidentDate"))

    @property
    def incidentTime(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incidentTime"))

    @property
    def incidentLocation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incidentLocation"))

    @property
    def claimantName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimantName"))

    @property
    def insuredParty(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("insuredParty"))

    @property
    def claimType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimType"))

    @property
    def damageDescription(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("damageDescription"))

    @property
    def estimatedAmount(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("estimatedAmount"))

    @property
    def policeReportNumber(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policeReportNumber"))

    @property
    def emergencyServicesInvolved(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("emergencyServicesInvolved"))

    @property
    def injuriesReported(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("injuriesReported"))

    @property
    def propertyDamage(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("propertyDamage"))

    @property
    def witnessesPresent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("witnessesPresent"))

    @property
    def vehicleInvolved(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("vehicleInvolved"))

    @property
    def thirdPartyInvolved(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("thirdPartyInvolved"))

    @property
    def medicalInformation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalInformation"))

    

class ClaimDocumentInputAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ClaimDocumentInput")
        self._properties: typing.Set[str] = set([ "claimId",  "emailContent",  "emailSubject",  "attachmentNames",  "attachmentsText",  "preprocessingNotes", ])
        self._props = ClaimDocumentInputProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ClaimDocumentInputProperties":
        return self._props


class ClaimDocumentInputViewer(ClaimDocumentInputAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ClaimDocumentInputProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def emailContent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("emailContent"))

    @property
    def emailSubject(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("emailSubject"))

    @property
    def attachmentNames(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachmentNames"))

    @property
    def attachmentsText(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachmentsText"))

    @property
    def preprocessingNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("preprocessingNotes"))

    

class ClaimIndicatorsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ClaimIndicators")
        self._properties: typing.Set[str] = set([ "incidentKeywords",  "damageKeywords",  "legalKeywords",  "medicalKeywords",  "timeIndicators",  "locationIndicators",  "partyIndicators", ])
        self._props = ClaimIndicatorsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ClaimIndicatorsProperties":
        return self._props


class ClaimIndicatorsViewer(ClaimIndicatorsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ClaimIndicatorsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def incidentKeywords(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incidentKeywords"))

    @property
    def damageKeywords(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("damageKeywords"))

    @property
    def legalKeywords(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalKeywords"))

    @property
    def medicalKeywords(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalKeywords"))

    @property
    def timeIndicators(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timeIndicators"))

    @property
    def locationIndicators(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("locationIndicators"))

    @property
    def partyIndicators(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("partyIndicators"))

    

class ClaimantInformationNeededAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ClaimantInformationNeeded")
        self._properties: typing.Set[str] = set([ "documentsRequired",  "clarificationsNeeded",  "evidenceRequired",  "timelineForResponse",  "consequencesOfNonCompliance",  "assistanceAvailable", ])
        self._props = ClaimantInformationNeededProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ClaimantInformationNeededProperties":
        return self._props


class ClaimantInformationNeededViewer(ClaimantInformationNeededAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ClaimantInformationNeededProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def documentsRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentsRequired"))

    @property
    def clarificationsNeeded(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("clarificationsNeeded"))

    @property
    def evidenceRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceRequired"))

    @property
    def timelineForResponse(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timelineForResponse"))

    @property
    def consequencesOfNonCompliance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("consequencesOfNonCompliance"))

    @property
    def assistanceAvailable(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("assistanceAvailable"))

    

class ColorLegendAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ColorLegend")
        self._properties: typing.Set[str] = set([ "color",  "meaning",  "examples", ])
        self._props = ColorLegendProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ColorLegendProperties":
        return self._props


class ColorLegendViewer(ColorLegendAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ColorLegendProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def color(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("color"))

    @property
    def meaning(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("meaning"))

    @property
    def examples(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("examples"))

    

class ColorSchemeMappingAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ColorSchemeMapping")
        self._properties: typing.Set[str] = set([ "entityTypeColors",  "confidenceColorScheme",  "analysisColorScheme", ])
        self._props = ColorSchemeMappingProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ColorSchemeMappingProperties":
        return self._props


class ColorSchemeMappingViewer(ColorSchemeMappingAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ColorSchemeMappingProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def entityTypeColors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("entityTypeColors"))

    @property
    def confidenceColorScheme(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceColorScheme"))

    @property
    def analysisColorScheme(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisColorScheme"))

    

class CommunicationActionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CommunicationAction")
        self._properties: typing.Set[str] = set([ "communicationType",  "communicationRecipient",  "communicationPurpose",  "communicationTimeline",  "communicationMethod",  "followUpRequired", ])
        self._props = CommunicationActionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CommunicationActionProperties":
        return self._props


class CommunicationActionViewer(CommunicationActionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CommunicationActionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def communicationType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("communicationType"))

    @property
    def communicationRecipient(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("communicationRecipient"))

    @property
    def communicationPurpose(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("communicationPurpose"))

    @property
    def communicationTimeline(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("communicationTimeline"))

    @property
    def communicationMethod(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("communicationMethod"))

    @property
    def followUpRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("followUpRequired"))

    

class ConfidenceBreakdownAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ConfidenceBreakdown")
        self._properties: typing.Set[str] = set([ "claimDetailsConfidence",  "policyDetailsConfidence",  "causeOfLossConfidence",  "contactDetailsConfidence",  "overallConfidence", ])
        self._props = ConfidenceBreakdownProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ConfidenceBreakdownProperties":
        return self._props


class ConfidenceBreakdownViewer(ConfidenceBreakdownAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ConfidenceBreakdownProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claimDetailsConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimDetailsConfidence"))

    @property
    def policyDetailsConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyDetailsConfidence"))

    @property
    def causeOfLossConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("causeOfLossConfidence"))

    @property
    def contactDetailsConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contactDetailsConfidence"))

    @property
    def overallConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overallConfidence"))

    

class ConfidenceColorSchemeAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ConfidenceColorScheme")
        self._properties: typing.Set[str] = set([ "highConfidenceColor",  "mediumConfidenceColor",  "lowConfidenceColor", ])
        self._props = ConfidenceColorSchemeProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ConfidenceColorSchemeProperties":
        return self._props


class ConfidenceColorSchemeViewer(ConfidenceColorSchemeAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ConfidenceColorSchemeProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def highConfidenceColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("highConfidenceColor"))

    @property
    def mediumConfidenceColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("mediumConfidenceColor"))

    @property
    def lowConfidenceColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("lowConfidenceColor"))

    

class ConfidenceContributorAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ConfidenceContributor")
        self._properties: typing.Set[str] = set([ "contributor",  "contribution",  "reason",  "sourceType", ])
        self._props = ConfidenceContributorProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ConfidenceContributorProperties":
        return self._props


class ConfidenceContributorViewer(ConfidenceContributorAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ConfidenceContributorProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def contributor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contributor"))

    @property
    def contribution(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contribution"))

    @property
    def reason(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reason"))

    @property
    def sourceType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sourceType"))

    

class ConfidenceDistributionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ConfidenceDistribution")
        self._properties: typing.Set[str] = set([ "highConfidenceFields",  "mediumConfidenceFields",  "lowConfidenceFields",  "averageConfidence", ])
        self._props = ConfidenceDistributionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ConfidenceDistributionProperties":
        return self._props


class ConfidenceDistributionViewer(ConfidenceDistributionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ConfidenceDistributionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def highConfidenceFields(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("highConfidenceFields"))

    @property
    def mediumConfidenceFields(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("mediumConfidenceFields"))

    @property
    def lowConfidenceFields(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("lowConfidenceFields"))

    @property
    def averageConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("averageConfidence"))

    

class ContactInfoAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ContactInfo")
        self._properties: typing.Set[str] = set([ "name",  "role",  "phoneNumber",  "email",  "address",  "driverLicense",  "relationship",  "insuranceInfo",  "employer",  "notes", ])
        self._props = ContactInfoProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ContactInfoProperties":
        return self._props


class ContactInfoViewer(ContactInfoAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ContactInfoProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def name(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("name"))

    @property
    def role(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("role"))

    @property
    def phoneNumber(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("phoneNumber"))

    @property
    def email(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("email"))

    @property
    def address(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("address"))

    @property
    def driverLicense(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("driverLicense"))

    @property
    def relationship(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("relationship"))

    @property
    def insuranceInfo(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("insuranceInfo"))

    @property
    def employer(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("employer"))

    @property
    def notes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("notes"))

    

class ContextualFactorsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ContextualFactors")
        self._properties: typing.Set[str] = set([ "analysisLevel",  "analysisContext",  "domainSpecificFactors",  "regulatoryFactors",  "businessRules",  "qualityFactors", ])
        self._props = ContextualFactorsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ContextualFactorsProperties":
        return self._props


class ContextualFactorsViewer(ContextualFactorsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ContextualFactorsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def analysisLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisLevel"))

    @property
    def analysisContext(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisContext"))

    @property
    def domainSpecificFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("domainSpecificFactors"))

    @property
    def regulatoryFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("regulatoryFactors"))

    @property
    def businessRules(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("businessRules"))

    @property
    def qualityFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("qualityFactors"))

    

class ContradictoryEvidenceAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ContradictoryEvidence")
        self._properties: typing.Set[str] = set([ "evidenceType",  "evidenceDescription",  "contradictionReason",  "responseToContradiction",  "contradictionWeight",  "mitigationStrategy", ])
        self._props = ContradictoryEvidenceProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ContradictoryEvidenceProperties":
        return self._props


class ContradictoryEvidenceViewer(ContradictoryEvidenceAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ContradictoryEvidenceProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def evidenceType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceType"))

    @property
    def evidenceDescription(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceDescription"))

    @property
    def contradictionReason(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contradictionReason"))

    @property
    def responseToContradiction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("responseToContradiction"))

    @property
    def contradictionWeight(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contradictionWeight"))

    @property
    def mitigationStrategy(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("mitigationStrategy"))

    

class ContributoryAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ContributoryAnalysis")
        self._properties: typing.Set[str] = set([ "claimantFactors",  "awarenessLevel",  "avoidabilityFactor",  "behaviorFactors",  "mitigatingCircumstances",  "aggravatingCircumstances",  "contributionPercentage", ])
        self._props = ContributoryAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ContributoryAnalysisProperties":
        return self._props


class ContributoryAnalysisViewer(ContributoryAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ContributoryAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claimantFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimantFactors"))

    @property
    def awarenessLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("awarenessLevel"))

    @property
    def avoidabilityFactor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("avoidabilityFactor"))

    @property
    def behaviorFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("behaviorFactors"))

    @property
    def mitigatingCircumstances(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("mitigatingCircumstances"))

    @property
    def aggravatingCircumstances(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("aggravatingCircumstances"))

    @property
    def contributionPercentage(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contributionPercentage"))

    

class CoverageAnalysisDetailsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CoverageAnalysisDetails")
        self._properties: typing.Set[str] = set([ "analysisCompleteness",  "analysisConfidence",  "keyFindings",  "analysisLimitations",  "assumptionsMade",  "uncertaintyAreas",  "additionalAnalysisNeeded", ])
        self._props = CoverageAnalysisDetailsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CoverageAnalysisDetailsProperties":
        return self._props


class CoverageAnalysisDetailsViewer(CoverageAnalysisDetailsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CoverageAnalysisDetailsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def analysisCompleteness(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisCompleteness"))

    @property
    def analysisConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisConfidence"))

    @property
    def keyFindings(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("keyFindings"))

    @property
    def analysisLimitations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisLimitations"))

    @property
    def assumptionsMade(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("assumptionsMade"))

    @property
    def uncertaintyAreas(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyAreas"))

    @property
    def additionalAnalysisNeeded(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("additionalAnalysisNeeded"))

    

class CoverageDecisionSupportAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CoverageDecisionSupport")
        self._properties: typing.Set[str] = set([ "supportingEvidence",  "contradictoryEvidence",  "evidenceWeighting",  "expertOpinions",  "documentationQuality",  "evidenceGaps", ])
        self._props = CoverageDecisionSupportProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CoverageDecisionSupportProperties":
        return self._props


class CoverageDecisionSupportViewer(CoverageDecisionSupportAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CoverageDecisionSupportProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def supportingEvidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("supportingEvidence"))

    @property
    def contradictoryEvidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contradictoryEvidence"))

    @property
    def evidenceWeighting(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceWeighting"))

    @property
    def expertOpinions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("expertOpinions"))

    @property
    def documentationQuality(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentationQuality"))

    @property
    def evidenceGaps(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceGaps"))

    

class CoverageJustificationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CoverageJustification")
        self._properties: typing.Set[str] = set([ "primaryReason",  "detailedReasoning",  "policyBasis",  "legalBasis",  "factualBasis",  "industryPractice",  "precedentSupport",  "riskFactors",  "alternativeInterpretations",  "decisionStrength", ])
        self._props = CoverageJustificationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CoverageJustificationProperties":
        return self._props


class CoverageJustificationViewer(CoverageJustificationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CoverageJustificationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def primaryReason(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("primaryReason"))

    @property
    def detailedReasoning(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("detailedReasoning"))

    @property
    def policyBasis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyBasis"))

    @property
    def legalBasis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalBasis"))

    @property
    def factualBasis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("factualBasis"))

    @property
    def industryPractice(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("industryPractice"))

    @property
    def precedentSupport(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("precedentSupport"))

    @property
    def riskFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("riskFactors"))

    @property
    def alternativeInterpretations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("alternativeInterpretations"))

    @property
    def decisionStrength(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("decisionStrength"))

    

class CoverageMappingAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CoverageMapping")
        self._properties: typing.Set[str] = set([ "causeOfLoss",  "policySection",  "coverageRationale",  "coverageStrength",  "counterArguments",  "supportingArguments",  "industryPractice", ])
        self._props = CoverageMappingProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CoverageMappingProperties":
        return self._props


class CoverageMappingViewer(CoverageMappingAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CoverageMappingProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def causeOfLoss(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("causeOfLoss"))

    @property
    def policySection(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policySection"))

    @property
    def coverageRationale(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageRationale"))

    @property
    def coverageStrength(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageStrength"))

    @property
    def counterArguments(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("counterArguments"))

    @property
    def supportingArguments(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("supportingArguments"))

    @property
    def industryPractice(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("industryPractice"))

    

class DataCompletenessAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DataCompletenessAssessment")
        self._properties: typing.Set[str] = set([ "overallCompletenessScore",  "level01AnalysisCompleteness",  "claimDetailsCompleteness",  "ocrDocumentsCompleteness",  "attachmentsCompleteness",  "level01AnalysisImportance",  "claimDetailsImportance",  "ocrDocumentsImportance",  "attachmentsImportance",  "criticalMissingItems",  "minorMissingItems",  "dataQualityScore",  "analysisReadiness",  "improvementRecommendations",  "alternativeDataSources",  "claimTypeSpecificNeeds",  "riskFactorsFromGaps",  "assessmentConfidence",  "uncertaintyAreas", ])
        self._props = DataCompletenessAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DataCompletenessAssessmentProperties":
        return self._props


class DataCompletenessAssessmentViewer(DataCompletenessAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DataCompletenessAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def overallCompletenessScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overallCompletenessScore"))

    @property
    def level01AnalysisCompleteness(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level01AnalysisCompleteness"))

    @property
    def claimDetailsCompleteness(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimDetailsCompleteness"))

    @property
    def ocrDocumentsCompleteness(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ocrDocumentsCompleteness"))

    @property
    def attachmentsCompleteness(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachmentsCompleteness"))

    @property
    def level01AnalysisImportance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level01AnalysisImportance"))

    @property
    def claimDetailsImportance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimDetailsImportance"))

    @property
    def ocrDocumentsImportance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ocrDocumentsImportance"))

    @property
    def attachmentsImportance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachmentsImportance"))

    @property
    def criticalMissingItems(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("criticalMissingItems"))

    @property
    def minorMissingItems(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("minorMissingItems"))

    @property
    def dataQualityScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("dataQualityScore"))

    @property
    def analysisReadiness(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisReadiness"))

    @property
    def improvementRecommendations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("improvementRecommendations"))

    @property
    def alternativeDataSources(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("alternativeDataSources"))

    @property
    def claimTypeSpecificNeeds(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimTypeSpecificNeeds"))

    @property
    def riskFactorsFromGaps(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("riskFactorsFromGaps"))

    @property
    def assessmentConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("assessmentConfidence"))

    @property
    def uncertaintyAreas(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyAreas"))

    

class DataFlowStepAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DataFlowStep")
        self._properties: typing.Set[str] = set([ "stepName",  "sourceData",  "processing",  "outputData",  "dataQuality", ])
        self._props = DataFlowStepProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DataFlowStepProperties":
        return self._props


class DataFlowStepViewer(DataFlowStepAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DataFlowStepProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def stepName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("stepName"))

    @property
    def sourceData(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sourceData"))

    @property
    def processing(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("processing"))

    @property
    def outputData(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("outputData"))

    @property
    def dataQuality(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("dataQuality"))

    

class DataItemAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DataItem")
        self._properties: typing.Set[str] = set([ "itemName",  "isPresent",  "qualityScore",  "importanceForClaimType",  "alternativeSources",  "impactOnAnalysis", ])
        self._props = DataItemProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DataItemProperties":
        return self._props


class DataItemViewer(DataItemAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DataItemProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def itemName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("itemName"))

    @property
    def isPresent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("isPresent"))

    @property
    def qualityScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("qualityScore"))

    @property
    def importanceForClaimType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("importanceForClaimType"))

    @property
    def alternativeSources(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("alternativeSources"))

    @property
    def impactOnAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("impactOnAnalysis"))

    

class DecisionPointAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DecisionPoint")
        self._properties: typing.Set[str] = set([ "decisionName",  "decisionDescription",  "inputFactors",  "outcome",  "confidence",  "alternativeOptions", ])
        self._props = DecisionPointProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DecisionPointProperties":
        return self._props


class DecisionPointViewer(DecisionPointAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DecisionPointProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def decisionName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("decisionName"))

    @property
    def decisionDescription(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("decisionDescription"))

    @property
    def inputFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("inputFactors"))

    @property
    def outcome(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("outcome"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def alternativeOptions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("alternativeOptions"))

    

class DiagnosticResultAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DiagnosticResult")
        self._properties: typing.Set[str] = set([ "diagnosticType",  "findings",  "datePerformed",  "facility",  "abnormalFindings",  "supportsCausation", ])
        self._props = DiagnosticResultProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DiagnosticResultProperties":
        return self._props


class DiagnosticResultViewer(DiagnosticResultAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DiagnosticResultProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def diagnosticType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("diagnosticType"))

    @property
    def findings(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("findings"))

    @property
    def datePerformed(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("datePerformed"))

    @property
    def facility(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("facility"))

    @property
    def abnormalFindings(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("abnormalFindings"))

    @property
    def supportsCausation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("supportsCausation"))

    

class DocumentHighlightAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DocumentHighlight")
        self._properties: typing.Set[str] = set([ "documentId",  "documentName",  "pageNumber",  "highlights", ])
        self._props = DocumentHighlightProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DocumentHighlightProperties":
        return self._props


class DocumentHighlightViewer(DocumentHighlightAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DocumentHighlightProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def documentId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentId"))

    @property
    def documentName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentName"))

    @property
    def pageNumber(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("pageNumber"))

    @property
    def highlights(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("highlights"))

    

class DocumentationActionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DocumentationAction")
        self._properties: typing.Set[str] = set([ "documentType",  "documentPurpose",  "documentRecipient",  "documentTimeline",  "documentImportance", ])
        self._props = DocumentationActionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DocumentationActionProperties":
        return self._props


class DocumentationActionViewer(DocumentationActionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DocumentationActionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def documentType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentType"))

    @property
    def documentPurpose(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentPurpose"))

    @property
    def documentRecipient(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentRecipient"))

    @property
    def documentTimeline(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentTimeline"))

    @property
    def documentImportance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentImportance"))

    

class DocumentationQualityAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DocumentationQuality")
        self._properties: typing.Set[str] = set([ "completeness",  "reliability",  "consistency",  "timeliness",  "authentication",  "qualityNotes", ])
        self._props = DocumentationQualityProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DocumentationQualityProperties":
        return self._props


class DocumentationQualityViewer(DocumentationQualityAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DocumentationQualityProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def completeness(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("completeness"))

    @property
    def reliability(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reliability"))

    @property
    def consistency(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("consistency"))

    @property
    def timeliness(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timeliness"))

    @property
    def authentication(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("authentication"))

    @property
    def qualityNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("qualityNotes"))

    

class EmailAttachmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("EmailAttachment")
        self._properties: typing.Set[str] = set([ "filename",  "contentType",  "size", ])
        self._props = EmailAttachmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "EmailAttachmentProperties":
        return self._props


class EmailAttachmentViewer(EmailAttachmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class EmailAttachmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def filename(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("filename"))

    @property
    def contentType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contentType"))

    @property
    def size(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("size"))

    

class EmailForClassificationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("EmailForClassification")
        self._properties: typing.Set[str] = set([ "subject",  "body",  "senderEmail",  "senderName",  "receivedDate",  "attachments", ])
        self._props = EmailForClassificationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "EmailForClassificationProperties":
        return self._props


class EmailForClassificationViewer(EmailForClassificationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class EmailForClassificationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def subject(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("subject"))

    @property
    def body(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("body"))

    @property
    def senderEmail(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("senderEmail"))

    @property
    def senderName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("senderName"))

    @property
    def receivedDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("receivedDate"))

    @property
    def attachments(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachments"))

    

class EntityMappingAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("EntityMapping")
        self._properties: typing.Set[str] = set([ "fieldName",  "extractedValue",  "originalText",  "sourceDocument",  "pageNumber",  "lineNumber",  "startPosition",  "endPosition",  "boundingBox",  "extractionMethod",  "confidence",  "highlightColor",  "entityType",  "relevanceScore", ])
        self._props = EntityMappingProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "EntityMappingProperties":
        return self._props


class EntityMappingViewer(EntityMappingAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class EntityMappingProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def fieldName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("fieldName"))

    @property
    def extractedValue(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("extractedValue"))

    @property
    def originalText(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("originalText"))

    @property
    def sourceDocument(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sourceDocument"))

    @property
    def pageNumber(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("pageNumber"))

    @property
    def lineNumber(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("lineNumber"))

    @property
    def startPosition(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("startPosition"))

    @property
    def endPosition(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("endPosition"))

    @property
    def boundingBox(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("boundingBox"))

    @property
    def extractionMethod(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("extractionMethod"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def highlightColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("highlightColor"))

    @property
    def entityType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("entityType"))

    @property
    def relevanceScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("relevanceScore"))

    

class EntityTypeColorAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("EntityTypeColor")
        self._properties: typing.Set[str] = set([ "entityType",  "primaryColor",  "secondaryColor",  "textColor",  "weight", ])
        self._props = EntityTypeColorProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "EntityTypeColorProperties":
        return self._props


class EntityTypeColorViewer(EntityTypeColorAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class EntityTypeColorProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def entityType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("entityType"))

    @property
    def primaryColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("primaryColor"))

    @property
    def secondaryColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("secondaryColor"))

    @property
    def textColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("textColor"))

    @property
    def weight(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("weight"))

    

class EnvironmentalFactorsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("EnvironmentalFactors")
        self._properties: typing.Set[str] = set([ "weatherConditions",  "lightingConditions",  "surfaceConditions",  "visibilityFactors",  "crowdingLevel", ])
        self._props = EnvironmentalFactorsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "EnvironmentalFactorsProperties":
        return self._props


class EnvironmentalFactorsViewer(EnvironmentalFactorsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class EnvironmentalFactorsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def weatherConditions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("weatherConditions"))

    @property
    def lightingConditions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("lightingConditions"))

    @property
    def surfaceConditions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("surfaceConditions"))

    @property
    def visibilityFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("visibilityFactors"))

    @property
    def crowdingLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("crowdingLevel"))

    

class EvidenceQualityAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("EvidenceQuality")
        self._properties: typing.Set[str] = set([ "witnessStatements",  "documentaryEvidence",  "physicalEvidence",  "expertOpinions",  "overallReliability",  "evidenceGaps", ])
        self._props = EvidenceQualityProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "EvidenceQualityProperties":
        return self._props


class EvidenceQualityViewer(EvidenceQualityAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class EvidenceQualityProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def witnessStatements(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("witnessStatements"))

    @property
    def documentaryEvidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentaryEvidence"))

    @property
    def physicalEvidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("physicalEvidence"))

    @property
    def expertOpinions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("expertOpinions"))

    @property
    def overallReliability(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overallReliability"))

    @property
    def evidenceGaps(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceGaps"))

    

class EvidenceWeightingAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("EvidenceWeighting")
        self._properties: typing.Set[str] = set([ "documentaryEvidence",  "witnessTestimony",  "expertOpinion",  "physicalEvidence",  "circumstantialEvidence",  "weightingRationale", ])
        self._props = EvidenceWeightingProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "EvidenceWeightingProperties":
        return self._props


class EvidenceWeightingViewer(EvidenceWeightingAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class EvidenceWeightingProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def documentaryEvidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentaryEvidence"))

    @property
    def witnessTestimony(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("witnessTestimony"))

    @property
    def expertOpinion(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("expertOpinion"))

    @property
    def physicalEvidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("physicalEvidence"))

    @property
    def circumstantialEvidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("circumstantialEvidence"))

    @property
    def weightingRationale(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("weightingRationale"))

    

class ExclusionAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ExclusionAnalysis")
        self._properties: typing.Set[str] = set([ "potentialExclusions",  "applicableExclusions",  "exclusionJustification",  "canadianExclusionPrecedents",  "exclusionInterpretation",  "exclusionRisk", ])
        self._props = ExclusionAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ExclusionAnalysisProperties":
        return self._props


class ExclusionAnalysisViewer(ExclusionAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ExclusionAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def potentialExclusions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("potentialExclusions"))

    @property
    def applicableExclusions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("applicableExclusions"))

    @property
    def exclusionJustification(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exclusionJustification"))

    @property
    def canadianExclusionPrecedents(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("canadianExclusionPrecedents"))

    @property
    def exclusionInterpretation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exclusionInterpretation"))

    @property
    def exclusionRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exclusionRisk"))

    

class ExitAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ExitAnalysis")
        self._properties: typing.Set[str] = set([ "exitReason",  "analysisProvided",  "nextStepsRequired",  "estimatedTimeToResolution",  "priorityLevel",  "automationOpportunities",  "humanReviewItems", ])
        self._props = ExitAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ExitAnalysisProperties":
        return self._props


class ExitAnalysisViewer(ExitAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ExitAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def exitReason(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exitReason"))

    @property
    def analysisProvided(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisProvided"))

    @property
    def nextStepsRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("nextStepsRequired"))

    @property
    def estimatedTimeToResolution(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("estimatedTimeToResolution"))

    @property
    def priorityLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("priorityLevel"))

    @property
    def automationOpportunities(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("automationOpportunities"))

    @property
    def humanReviewItems(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("humanReviewItems"))

    

class ExpertOpinionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ExpertOpinion")
        self._properties: typing.Set[str] = set([ "expertType",  "expertCredentials",  "opinionSummary",  "opinionBasis",  "opinionReliability",  "opinionImpact",  "conflictingOpinions", ])
        self._props = ExpertOpinionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ExpertOpinionProperties":
        return self._props


class ExpertOpinionViewer(ExpertOpinionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ExpertOpinionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def expertType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("expertType"))

    @property
    def expertCredentials(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("expertCredentials"))

    @property
    def opinionSummary(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("opinionSummary"))

    @property
    def opinionBasis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("opinionBasis"))

    @property
    def opinionReliability(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("opinionReliability"))

    @property
    def opinionImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("opinionImpact"))

    @property
    def conflictingOpinions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("conflictingOpinions"))

    

class ExplainabilityInsightAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ExplainabilityInsight")
        self._properties: typing.Set[str] = set([ "analysisType",  "decisionContext",  "featureImportances",  "topPositiveFactors",  "topNegativeFactors",  "confidenceContributors",  "alternativeScenarios",  "visualizationData",  "methodologyNotes",  "contextualFactors", ])
        self._props = ExplainabilityInsightProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ExplainabilityInsightProperties":
        return self._props


class ExplainabilityInsightViewer(ExplainabilityInsightAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ExplainabilityInsightProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def analysisType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisType"))

    @property
    def decisionContext(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("decisionContext"))

    @property
    def featureImportances(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("featureImportances"))

    @property
    def topPositiveFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("topPositiveFactors"))

    @property
    def topNegativeFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("topNegativeFactors"))

    @property
    def confidenceContributors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceContributors"))

    @property
    def alternativeScenarios(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("alternativeScenarios"))

    @property
    def visualizationData(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("visualizationData"))

    @property
    def methodologyNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("methodologyNotes"))

    @property
    def contextualFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contextualFactors"))

    

class ExplanationFactorAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ExplanationFactor")
        self._properties: typing.Set[str] = set([ "factor",  "impact",  "explanation",  "sourceLocation",  "evidenceStrength",  "supportingEvidence", ])
        self._props = ExplanationFactorProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ExplanationFactorProperties":
        return self._props


class ExplanationFactorViewer(ExplanationFactorAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ExplanationFactorProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def factor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("factor"))

    @property
    def impact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("impact"))

    @property
    def explanation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("explanation"))

    @property
    def sourceLocation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sourceLocation"))

    @property
    def evidenceStrength(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceStrength"))

    @property
    def supportingEvidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("supportingEvidence"))

    

class FaultGuidanceAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FaultGuidance")
        self._properties: typing.Set[str] = set([ "suggestedPrimaryFault",  "suggestedSecondaryFault",  "faultRationale",  "uncertaintyAreas",  "comparisonCases",  "recommendedRuleSet",  "confidenceInGuidance", ])
        self._props = FaultGuidanceProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FaultGuidanceProperties":
        return self._props


class FaultGuidanceViewer(FaultGuidanceAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FaultGuidanceProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def suggestedPrimaryFault(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("suggestedPrimaryFault"))

    @property
    def suggestedSecondaryFault(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("suggestedSecondaryFault"))

    @property
    def faultRationale(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("faultRationale"))

    @property
    def uncertaintyAreas(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyAreas"))

    @property
    def comparisonCases(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("comparisonCases"))

    @property
    def recommendedRuleSet(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendedRuleSet"))

    @property
    def confidenceInGuidance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceInGuidance"))

    

class FaultImpactOnQuantumAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FaultImpactOnQuantum")
        self._properties: typing.Set[str] = set([ "claimantFaultPercentage",  "faultReductionApplicable",  "specialDamagesReduction",  "generalDamagesReduction",  "futureCareDamagesReduction",  "thresholdConsiderations",  "faultImpactOnSettlement",  "netRecoverableAmount", ])
        self._props = FaultImpactOnQuantumProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FaultImpactOnQuantumProperties":
        return self._props


class FaultImpactOnQuantumViewer(FaultImpactOnQuantumAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FaultImpactOnQuantumProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claimantFaultPercentage(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimantFaultPercentage"))

    @property
    def faultReductionApplicable(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("faultReductionApplicable"))

    @property
    def specialDamagesReduction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("specialDamagesReduction"))

    @property
    def generalDamagesReduction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("generalDamagesReduction"))

    @property
    def futureCareDamagesReduction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("futureCareDamagesReduction"))

    @property
    def thresholdConsiderations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("thresholdConsiderations"))

    @property
    def faultImpactOnSettlement(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("faultImpactOnSettlement"))

    @property
    def netRecoverableAmount(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("netRecoverableAmount"))

    

class FeatureImportanceAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FeatureImportance")
        self._properties: typing.Set[str] = set([ "featureName",  "importance",  "direction",  "explanation",  "sourceText",  "confidence",  "relatedFields",  "analysisContext", ])
        self._props = FeatureImportanceProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FeatureImportanceProperties":
        return self._props


class FeatureImportanceViewer(FeatureImportanceAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FeatureImportanceProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def featureName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("featureName"))

    @property
    def importance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("importance"))

    @property
    def direction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("direction"))

    @property
    def explanation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("explanation"))

    @property
    def sourceText(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sourceText"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def relatedFields(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("relatedFields"))

    @property
    def analysisContext(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisContext"))

    

class FieldConfidenceAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FieldConfidence")
        self._properties: typing.Set[str] = set([ "fieldName",  "confidence",  "contributingFactors",  "uncertaintyReasons",  "sourceQuality",  "validationStrength", ])
        self._props = FieldConfidenceProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FieldConfidenceProperties":
        return self._props


class FieldConfidenceViewer(FieldConfidenceAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FieldConfidenceProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def fieldName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("fieldName"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def contributingFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contributingFactors"))

    @property
    def uncertaintyReasons(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyReasons"))

    @property
    def sourceQuality(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sourceQuality"))

    @property
    def validationStrength(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("validationStrength"))

    

class FieldConfidenceBreakdownAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FieldConfidenceBreakdown")
        self._properties: typing.Set[str] = set([ "overallConfidence",  "fieldConfidences",  "uncertaintyFactors",  "confidenceDistribution",  "analysisCertainty", ])
        self._props = FieldConfidenceBreakdownProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FieldConfidenceBreakdownProperties":
        return self._props


class FieldConfidenceBreakdownViewer(FieldConfidenceBreakdownAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FieldConfidenceBreakdownProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def overallConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overallConfidence"))

    @property
    def fieldConfidences(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("fieldConfidences"))

    @property
    def uncertaintyFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyFactors"))

    @property
    def confidenceDistribution(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceDistribution"))

    @property
    def analysisCertainty(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisCertainty"))

    

class FutureCareAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FutureCareAssessment")
        self._properties: typing.Set[str] = set([ "futureMedicalTreatment",  "futureTreatmentCosts",  "futureTherapyCosts",  "futureCarePeriod",  "assistiveDevices",  "assistiveDeviceCosts",  "homeModifications",  "homeModificationCosts",  "attendantCareRequired",  "attendantCareCosts",  "totalFutureCareCosts",  "futureCareUncertainty", ])
        self._props = FutureCareAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FutureCareAssessmentProperties":
        return self._props


class FutureCareAssessmentViewer(FutureCareAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FutureCareAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def futureMedicalTreatment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("futureMedicalTreatment"))

    @property
    def futureTreatmentCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("futureTreatmentCosts"))

    @property
    def futureTherapyCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("futureTherapyCosts"))

    @property
    def futureCarePeriod(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("futureCarePeriod"))

    @property
    def assistiveDevices(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("assistiveDevices"))

    @property
    def assistiveDeviceCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("assistiveDeviceCosts"))

    @property
    def homeModifications(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("homeModifications"))

    @property
    def homeModificationCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("homeModificationCosts"))

    @property
    def attendantCareRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attendantCareRequired"))

    @property
    def attendantCareCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attendantCareCosts"))

    @property
    def totalFutureCareCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("totalFutureCareCosts"))

    @property
    def futureCareUncertainty(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("futureCareUncertainty"))

    

class GeneralDamagesAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("GeneralDamagesAssessment")
        self._properties: typing.Set[str] = set([ "painLevel",  "sufferingLevel",  "functionalImpairment",  "lifestyleImpact",  "psychologicalImpact",  "relationshipImpact",  "ageAtTimeOfAccident",  "genderConsiderations",  "permanentDisabilityFactor",  "comparableAwards",  "generalDamagesRange",  "recommendedGeneralDamages",  "ontarioBenchmarkCategory",  "benchmarkJustification",  "conservativeEstimate", ])
        self._props = GeneralDamagesAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "GeneralDamagesAssessmentProperties":
        return self._props


class GeneralDamagesAssessmentViewer(GeneralDamagesAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class GeneralDamagesAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def painLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("painLevel"))

    @property
    def sufferingLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sufferingLevel"))

    @property
    def functionalImpairment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("functionalImpairment"))

    @property
    def lifestyleImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("lifestyleImpact"))

    @property
    def psychologicalImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("psychologicalImpact"))

    @property
    def relationshipImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("relationshipImpact"))

    @property
    def ageAtTimeOfAccident(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ageAtTimeOfAccident"))

    @property
    def genderConsiderations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("genderConsiderations"))

    @property
    def permanentDisabilityFactor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("permanentDisabilityFactor"))

    @property
    def comparableAwards(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("comparableAwards"))

    @property
    def generalDamagesRange(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("generalDamagesRange"))

    @property
    def recommendedGeneralDamages(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendedGeneralDamages"))

    @property
    def ontarioBenchmarkCategory(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ontarioBenchmarkCategory"))

    @property
    def benchmarkJustification(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("benchmarkJustification"))

    @property
    def conservativeEstimate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("conservativeEstimate"))

    

class HighlightRegionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("HighlightRegion")
        self._properties: typing.Set[str] = set([ "fieldName",  "text",  "startPos",  "endPos",  "boundingBox",  "highlightColor",  "confidence",  "entityType",  "analysisRelevance", ])
        self._props = HighlightRegionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "HighlightRegionProperties":
        return self._props


class HighlightRegionViewer(HighlightRegionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class HighlightRegionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def fieldName(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("fieldName"))

    @property
    def text(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("text"))

    @property
    def startPos(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("startPos"))

    @property
    def endPos(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("endPos"))

    @property
    def boundingBox(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("boundingBox"))

    @property
    def highlightColor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("highlightColor"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def entityType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("entityType"))

    @property
    def analysisRelevance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisRelevance"))

    

class IncomeLossAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("IncomeLossAssessment")
        self._properties: typing.Set[str] = set([ "preAccidentIncome",  "employmentStatus",  "jobTitle",  "timeOffWork",  "workDaysLost",  "wageReplacementAmount",  "returnToWorkStatus",  "futureEarningImpact",  "documentedIncomeLoss",  "projectedFutureIncomeLoss",  "incomeLossCertainty",  "returnToWorkCapacity",  "ageFactorAdjustment", ])
        self._props = IncomeLossAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "IncomeLossAssessmentProperties":
        return self._props


class IncomeLossAssessmentViewer(IncomeLossAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class IncomeLossAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def preAccidentIncome(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("preAccidentIncome"))

    @property
    def employmentStatus(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("employmentStatus"))

    @property
    def jobTitle(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("jobTitle"))

    @property
    def timeOffWork(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timeOffWork"))

    @property
    def workDaysLost(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("workDaysLost"))

    @property
    def wageReplacementAmount(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("wageReplacementAmount"))

    @property
    def returnToWorkStatus(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("returnToWorkStatus"))

    @property
    def futureEarningImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("futureEarningImpact"))

    @property
    def documentedIncomeLoss(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentedIncomeLoss"))

    @property
    def projectedFutureIncomeLoss(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("projectedFutureIncomeLoss"))

    @property
    def incomeLossCertainty(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incomeLossCertainty"))

    @property
    def returnToWorkCapacity(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("returnToWorkCapacity"))

    @property
    def ageFactorAdjustment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ageFactorAdjustment"))

    

class InformationRequestAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("InformationRequest")
        self._properties: typing.Set[str] = set([ "informationType",  "informationSource",  "informationDetails",  "justification",  "urgencyLevel",  "timelineRequired",  "alternativeSources",  "impactOnDecision", ])
        self._props = InformationRequestProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "InformationRequestProperties":
        return self._props


class InformationRequestViewer(InformationRequestAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class InformationRequestProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def informationType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("informationType"))

    @property
    def informationSource(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("informationSource"))

    @property
    def informationDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("informationDetails"))

    @property
    def justification(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("justification"))

    @property
    def urgencyLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("urgencyLevel"))

    @property
    def timelineRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timelineRequired"))

    @property
    def alternativeSources(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("alternativeSources"))

    @property
    def impactOnDecision(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("impactOnDecision"))

    

class InjuryDetailAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("InjuryDetail")
        self._properties: typing.Set[str] = set([ "bodyPart",  "injuryType",  "severity",  "description",  "isPermanent",  "causationClear",  "priorHistory", ])
        self._props = InjuryDetailProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "InjuryDetailProperties":
        return self._props


class InjuryDetailViewer(InjuryDetailAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class InjuryDetailProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def bodyPart(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("bodyPart"))

    @property
    def injuryType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("injuryType"))

    @property
    def severity(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("severity"))

    @property
    def description(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("description"))

    @property
    def isPermanent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("isPermanent"))

    @property
    def causationClear(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("causationClear"))

    @property
    def priorHistory(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("priorHistory"))

    

class InjuryImpactAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("InjuryImpactAnalysis")
        self._properties: typing.Set[str] = set([ "injurySeverityFactor",  "medicalCausationCertainty",  "treatmentComplexity",  "functionalImpact",  "workImpact",  "recoveryPrognosis",  "preExistingFactors",  "medicalDocumentationQuality",  "liabilityAggravationFactor", ])
        self._props = InjuryImpactAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "InjuryImpactAnalysisProperties":
        return self._props


class InjuryImpactAnalysisViewer(InjuryImpactAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class InjuryImpactAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def injurySeverityFactor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("injurySeverityFactor"))

    @property
    def medicalCausationCertainty(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalCausationCertainty"))

    @property
    def treatmentComplexity(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentComplexity"))

    @property
    def functionalImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("functionalImpact"))

    @property
    def workImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("workImpact"))

    @property
    def recoveryPrognosis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recoveryPrognosis"))

    @property
    def preExistingFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("preExistingFactors"))

    @property
    def medicalDocumentationQuality(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalDocumentationQuality"))

    @property
    def liabilityAggravationFactor(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("liabilityAggravationFactor"))

    

class InteractiveElementAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("InteractiveElement")
        self._properties: typing.Set[str] = set([ "elementType",  "targetField",  "action",  "description",  "context", ])
        self._props = InteractiveElementProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "InteractiveElementProperties":
        return self._props


class InteractiveElementViewer(InteractiveElementAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class InteractiveElementProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def elementType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("elementType"))

    @property
    def targetField(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("targetField"))

    @property
    def action(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("action"))

    @property
    def description(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("description"))

    @property
    def context(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("context"))

    

class InvestigationActionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("InvestigationAction")
        self._properties: typing.Set[str] = set([ "investigationType",  "investigationScope",  "investigationTimeline",  "investigationResources",  "investigationCost",  "investigationExpectedOutcome", ])
        self._props = InvestigationActionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "InvestigationActionProperties":
        return self._props


class InvestigationActionViewer(InvestigationActionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class InvestigationActionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def investigationType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("investigationType"))

    @property
    def investigationScope(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("investigationScope"))

    @property
    def investigationTimeline(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("investigationTimeline"))

    @property
    def investigationResources(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("investigationResources"))

    @property
    def investigationCost(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("investigationCost"))

    @property
    def investigationExpectedOutcome(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("investigationExpectedOutcome"))

    

class LegalActionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("LegalAction")
        self._properties: typing.Set[str] = set([ "legalActionType",  "legalBasis",  "legalTimeline",  "legalCost",  "legalRisk",  "alternativesToLegalAction", ])
        self._props = LegalActionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "LegalActionProperties":
        return self._props


class LegalActionViewer(LegalActionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class LegalActionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def legalActionType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalActionType"))

    @property
    def legalBasis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalBasis"))

    @property
    def legalTimeline(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalTimeline"))

    @property
    def legalCost(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalCost"))

    @property
    def legalRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalRisk"))

    @property
    def alternativesToLegalAction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("alternativesToLegalAction"))

    

class Level01AnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Level01Analysis")
        self._properties: typing.Set[str] = set([ "claimDetails",  "policyDetails",  "causeOfLoss",  "contactDetails",  "documentsSufficient",  "policyNumberAvailable",  "policyDetailsComplete",  "exitPath",  "exitAnalysis",  "sparkNlpInsights",  "confidenceScore",  "processingNotes",  "dataQualityScore",  "canadianJurisdiction",  "legalConsiderations",  "regulatoryNotes",  "uiMetadata",  "explainabilityInsights",  "analysisTimestamp",  "modelVersion",  "processingTimeMs", ])
        self._props = Level01AnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "Level01AnalysisProperties":
        return self._props


class Level01AnalysisViewer(Level01AnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class Level01AnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claimDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimDetails"))

    @property
    def policyDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyDetails"))

    @property
    def causeOfLoss(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("causeOfLoss"))

    @property
    def contactDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contactDetails"))

    @property
    def documentsSufficient(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentsSufficient"))

    @property
    def policyNumberAvailable(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyNumberAvailable"))

    @property
    def policyDetailsComplete(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyDetailsComplete"))

    @property
    def exitPath(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exitPath"))

    @property
    def exitAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exitAnalysis"))

    @property
    def sparkNlpInsights(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sparkNlpInsights"))

    @property
    def confidenceScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceScore"))

    @property
    def processingNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("processingNotes"))

    @property
    def dataQualityScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("dataQualityScore"))

    @property
    def canadianJurisdiction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("canadianJurisdiction"))

    @property
    def legalConsiderations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalConsiderations"))

    @property
    def regulatoryNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("regulatoryNotes"))

    @property
    def uiMetadata(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uiMetadata"))

    @property
    def explainabilityInsights(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("explainabilityInsights"))

    @property
    def analysisTimestamp(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisTimestamp"))

    @property
    def modelVersion(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("modelVersion"))

    @property
    def processingTimeMs(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("processingTimeMs"))

    

class Level01SummaryAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Level01Summary")
        self._properties: typing.Set[str] = set([ "claimId",  "claimType",  "policyNumber",  "incidentDate",  "primaryCause",  "level01Confidence",  "level01ExitPath",  "keyFindings", ])
        self._props = Level01SummaryProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "Level01SummaryProperties":
        return self._props


class Level01SummaryViewer(Level01SummaryAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class Level01SummaryProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def claimType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimType"))

    @property
    def policyNumber(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyNumber"))

    @property
    def incidentDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incidentDate"))

    @property
    def primaryCause(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("primaryCause"))

    @property
    def level01Confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level01Confidence"))

    @property
    def level01ExitPath(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level01ExitPath"))

    @property
    def keyFindings(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("keyFindings"))

    

class Level02AnalysisInputAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Level02AnalysisInput")
        self._properties: typing.Set[str] = set([ "claimId",  "level01Analysis",  "policyDocuments",  "additionalEvidence",  "humanInputs",  "processingNotes",  "urgencyLevel",  "specialInstructions", ])
        self._props = Level02AnalysisInputProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "Level02AnalysisInputProperties":
        return self._props


class Level02AnalysisInputViewer(Level02AnalysisInputAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class Level02AnalysisInputProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def level01Analysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level01Analysis"))

    @property
    def policyDocuments(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyDocuments"))

    @property
    def additionalEvidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("additionalEvidence"))

    @property
    def humanInputs(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("humanInputs"))

    @property
    def processingNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("processingNotes"))

    @property
    def urgencyLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("urgencyLevel"))

    @property
    def specialInstructions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("specialInstructions"))

    

class Level02CoverageAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Level02CoverageAnalysis")
        self._properties: typing.Set[str] = set([ "coverageDecision",  "confidenceScore",  "policyAnalysis",  "exclusionAnalysis",  "coverageMapping",  "causeMapping",  "coverageJustification",  "decisionSupport",  "canadianLegalAnalysis",  "medicalImpactAssessment",  "claimantInformation",  "thirdPartyInformation",  "agentInformation",  "allInformationRequests",  "exitAnalysis",  "analysisQuality",  "riskAssessment",  "uncertaintyAreas",  "uiMetadata",  "explainabilityInsights",  "level01Data",  "analysisTimestamp",  "processingTimeMs",  "modelVersion",  "analystId", ])
        self._props = Level02CoverageAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "Level02CoverageAnalysisProperties":
        return self._props


class Level02CoverageAnalysisViewer(Level02CoverageAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class Level02CoverageAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def coverageDecision(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageDecision"))

    @property
    def confidenceScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceScore"))

    @property
    def policyAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyAnalysis"))

    @property
    def exclusionAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exclusionAnalysis"))

    @property
    def coverageMapping(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageMapping"))

    @property
    def causeMapping(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("causeMapping"))

    @property
    def coverageJustification(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageJustification"))

    @property
    def decisionSupport(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("decisionSupport"))

    @property
    def canadianLegalAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("canadianLegalAnalysis"))

    @property
    def medicalImpactAssessment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalImpactAssessment"))

    @property
    def claimantInformation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimantInformation"))

    @property
    def thirdPartyInformation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("thirdPartyInformation"))

    @property
    def agentInformation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("agentInformation"))

    @property
    def allInformationRequests(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("allInformationRequests"))

    @property
    def exitAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exitAnalysis"))

    @property
    def analysisQuality(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisQuality"))

    @property
    def riskAssessment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("riskAssessment"))

    @property
    def uncertaintyAreas(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyAreas"))

    @property
    def uiMetadata(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uiMetadata"))

    @property
    def explainabilityInsights(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("explainabilityInsights"))

    @property
    def level01Data(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level01Data"))

    @property
    def analysisTimestamp(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisTimestamp"))

    @property
    def processingTimeMs(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("processingTimeMs"))

    @property
    def modelVersion(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("modelVersion"))

    @property
    def analystId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analystId"))

    

class Level02ExitAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Level02ExitAnalysis")
        self._properties: typing.Set[str] = set([ "exitPath",  "exitReason",  "coverageAnalysisProvided",  "nextStepsRequired",  "riskAssessment",  "humanReviewRequired",  "legalCounselRequired",  "timelineForResolution",  "priorityLevel",  "escalationTriggers", ])
        self._props = Level02ExitAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "Level02ExitAnalysisProperties":
        return self._props


class Level02ExitAnalysisViewer(Level02ExitAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class Level02ExitAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def exitPath(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exitPath"))

    @property
    def exitReason(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exitReason"))

    @property
    def coverageAnalysisProvided(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageAnalysisProvided"))

    @property
    def nextStepsRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("nextStepsRequired"))

    @property
    def riskAssessment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("riskAssessment"))

    @property
    def humanReviewRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("humanReviewRequired"))

    @property
    def legalCounselRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalCounselRequired"))

    @property
    def timelineForResolution(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timelineForResolution"))

    @property
    def priorityLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("priorityLevel"))

    @property
    def escalationTriggers(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("escalationTriggers"))

    

class Level02NextStepsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Level02NextSteps")
        self._properties: typing.Set[str] = set([ "immediateActions",  "shortTermActions",  "longTermActions",  "documentationRequired",  "communicationRequired",  "investigationRequired",  "legalActionRequired", ])
        self._props = Level02NextStepsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "Level02NextStepsProperties":
        return self._props


class Level02NextStepsViewer(Level02NextStepsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class Level02NextStepsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def immediateActions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("immediateActions"))

    @property
    def shortTermActions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("shortTermActions"))

    @property
    def longTermActions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("longTermActions"))

    @property
    def documentationRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentationRequired"))

    @property
    def communicationRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("communicationRequired"))

    @property
    def investigationRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("investigationRequired"))

    @property
    def legalActionRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalActionRequired"))

    

class Level02RiskAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Level02RiskAssessment")
        self._properties: typing.Set[str] = set([ "coverageRisk",  "legalRisk",  "financialRisk",  "reputationalRisk",  "regulatoryRisk",  "overallRisk",  "riskMitigationStrategies",  "riskMonitoringRequired", ])
        self._props = Level02RiskAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "Level02RiskAssessmentProperties":
        return self._props


class Level02RiskAssessmentViewer(Level02RiskAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class Level02RiskAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def coverageRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageRisk"))

    @property
    def legalRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalRisk"))

    @property
    def financialRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("financialRisk"))

    @property
    def reputationalRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reputationalRisk"))

    @property
    def regulatoryRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("regulatoryRisk"))

    @property
    def overallRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overallRisk"))

    @property
    def riskMitigationStrategies(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("riskMitigationStrategies"))

    @property
    def riskMonitoringRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("riskMonitoringRequired"))

    

class Level03AnalysisInputAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Level03AnalysisInput")
        self._properties: typing.Set[str] = set([ "claimReference",  "province",  "level01Analysis",  "level02Coverage",  "emailContent",  "sparkNlpInsights",  "ocrTexts",  "attachmentDetails", ])
        self._props = Level03AnalysisInputProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "Level03AnalysisInputProperties":
        return self._props


class Level03AnalysisInputViewer(Level03AnalysisInputAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class Level03AnalysisInputProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claimReference(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimReference"))

    @property
    def province(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("province"))

    @property
    def level01Analysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level01Analysis"))

    @property
    def level02Coverage(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level02Coverage"))

    @property
    def emailContent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("emailContent"))

    @property
    def sparkNlpInsights(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sparkNlpInsights"))

    @property
    def ocrTexts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ocrTexts"))

    @property
    def attachmentDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachmentDetails"))

    

class Level04AnalysisInputAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Level04AnalysisInput")
        self._properties: typing.Set[str] = set([ "claimReference",  "province",  "level01Analysis",  "level02Coverage",  "level03Fault",  "emailContent",  "sparkNlpInsights",  "ocrTexts",  "attachmentDetails", ])
        self._props = Level04AnalysisInputProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "Level04AnalysisInputProperties":
        return self._props


class Level04AnalysisInputViewer(Level04AnalysisInputAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class Level04AnalysisInputProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claimReference(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimReference"))

    @property
    def province(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("province"))

    @property
    def level01Analysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level01Analysis"))

    @property
    def level02Coverage(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level02Coverage"))

    @property
    def level03Fault(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("level03Fault"))

    @property
    def emailContent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("emailContent"))

    @property
    def sparkNlpInsights(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("sparkNlpInsights"))

    @property
    def ocrTexts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ocrTexts"))

    @property
    def attachmentDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachmentDetails"))

    

class LiabilityFactorExtractionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("LiabilityFactorExtraction")
        self._properties: typing.Set[str] = set([ "accidentClassification",  "negligenceFactors",  "contributoryFactors",  "evidenceQuality",  "circumstanceDetails",  "structuredForRules",  "injuryImpactAnalysis",  "faultGuidance",  "uiMetadata",  "explainabilityInsights", ])
        self._props = LiabilityFactorExtractionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "LiabilityFactorExtractionProperties":
        return self._props


class LiabilityFactorExtractionViewer(LiabilityFactorExtractionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class LiabilityFactorExtractionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def accidentClassification(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("accidentClassification"))

    @property
    def negligenceFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("negligenceFactors"))

    @property
    def contributoryFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contributoryFactors"))

    @property
    def evidenceQuality(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceQuality"))

    @property
    def circumstanceDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("circumstanceDetails"))

    @property
    def structuredForRules(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("structuredForRules"))

    @property
    def injuryImpactAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("injuryImpactAnalysis"))

    @property
    def faultGuidance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("faultGuidance"))

    @property
    def uiMetadata(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uiMetadata"))

    @property
    def explainabilityInsights(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("explainabilityInsights"))

    

class MedicalDamageAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("MedicalDamageAssessment")
        self._properties: typing.Set[str] = set([ "emergencyCareCosts",  "ongoingTreatmentCosts",  "diagnosticCosts",  "medicationCosts",  "medicalEquipmentCosts",  "treatmentSessions",  "treatmentDuration",  "medicalProviders",  "medicalComplexity",  "documentedMedicalCosts",  "estimatedFutureMedical",  "medicalCostCertainty",  "currentTreatmentStatus",  "recoveryPrognosis", ])
        self._props = MedicalDamageAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "MedicalDamageAssessmentProperties":
        return self._props


class MedicalDamageAssessmentViewer(MedicalDamageAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class MedicalDamageAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def emergencyCareCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("emergencyCareCosts"))

    @property
    def ongoingTreatmentCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ongoingTreatmentCosts"))

    @property
    def diagnosticCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("diagnosticCosts"))

    @property
    def medicationCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicationCosts"))

    @property
    def medicalEquipmentCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalEquipmentCosts"))

    @property
    def treatmentSessions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentSessions"))

    @property
    def treatmentDuration(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentDuration"))

    @property
    def medicalProviders(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalProviders"))

    @property
    def medicalComplexity(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalComplexity"))

    @property
    def documentedMedicalCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentedMedicalCosts"))

    @property
    def estimatedFutureMedical(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("estimatedFutureMedical"))

    @property
    def medicalCostCertainty(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalCostCertainty"))

    @property
    def currentTreatmentStatus(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("currentTreatmentStatus"))

    @property
    def recoveryPrognosis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recoveryPrognosis"))

    

class MedicalImpactAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("MedicalImpactAssessment")
        self._properties: typing.Set[str] = set([ "injurySeverityLevel",  "treatmentComplexity",  "functionalImpairment",  "returnToWorkLikelihood",  "futureCareCosts",  "permanentDisability",  "medicalCausationClear",  "preExistingConditions",  "medicalDocumentationQuality",  "expertMedicalOpinionNeeded", ])
        self._props = MedicalImpactAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "MedicalImpactAssessmentProperties":
        return self._props


class MedicalImpactAssessmentViewer(MedicalImpactAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class MedicalImpactAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def injurySeverityLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("injurySeverityLevel"))

    @property
    def treatmentComplexity(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentComplexity"))

    @property
    def functionalImpairment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("functionalImpairment"))

    @property
    def returnToWorkLikelihood(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("returnToWorkLikelihood"))

    @property
    def futureCareCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("futureCareCosts"))

    @property
    def permanentDisability(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("permanentDisability"))

    @property
    def medicalCausationClear(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalCausationClear"))

    @property
    def preExistingConditions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("preExistingConditions"))

    @property
    def medicalDocumentationQuality(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalDocumentationQuality"))

    @property
    def expertMedicalOpinionNeeded(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("expertMedicalOpinionNeeded"))

    

class MedicalSummaryAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("MedicalSummary")
        self._properties: typing.Set[str] = set([ "injuriesReported",  "injuryDetails",  "treatmentRecords",  "diagnosticResults",  "medicalTimeline",  "medicalProfessionals",  "medicalCosts",  "futureCareneeds",  "impactOnDailyLife",  "returnToWorkPrognosis",  "medicalDocumentsAvailable",  "medicalDocumentsNeeded", ])
        self._props = MedicalSummaryProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "MedicalSummaryProperties":
        return self._props


class MedicalSummaryViewer(MedicalSummaryAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class MedicalSummaryProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def injuriesReported(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("injuriesReported"))

    @property
    def injuryDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("injuryDetails"))

    @property
    def treatmentRecords(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentRecords"))

    @property
    def diagnosticResults(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("diagnosticResults"))

    @property
    def medicalTimeline(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalTimeline"))

    @property
    def medicalProfessionals(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalProfessionals"))

    @property
    def medicalCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalCosts"))

    @property
    def futureCareneeds(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("futureCareneeds"))

    @property
    def impactOnDailyLife(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("impactOnDailyLife"))

    @property
    def returnToWorkPrognosis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("returnToWorkPrognosis"))

    @property
    def medicalDocumentsAvailable(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalDocumentsAvailable"))

    @property
    def medicalDocumentsNeeded(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalDocumentsNeeded"))

    

class MedicalTimelineAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("MedicalTimeline")
        self._properties: typing.Set[str] = set([ "incidentDate",  "firstTreatmentDate",  "emergencyTreatment",  "timeOffWork",  "returnToWorkDate",  "treatmentEndDate",  "functionalLimitations", ])
        self._props = MedicalTimelineProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "MedicalTimelineProperties":
        return self._props


class MedicalTimelineViewer(MedicalTimelineAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class MedicalTimelineProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def incidentDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incidentDate"))

    @property
    def firstTreatmentDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("firstTreatmentDate"))

    @property
    def emergencyTreatment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("emergencyTreatment"))

    @property
    def timeOffWork(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timeOffWork"))

    @property
    def returnToWorkDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("returnToWorkDate"))

    @property
    def treatmentEndDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentEndDate"))

    @property
    def functionalLimitations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("functionalLimitations"))

    

class NegligenceAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("NegligenceAnalysis")
        self._properties: typing.Set[str] = set([ "propertyOwnerFactors",  "vehicleOperatorFactors",  "thirdPartyFactors",  "institutionalFactors",  "maintenanceFailures",  "warningDeficiencies",  "dutyOfCareBreaches",  "statutoryViolations", ])
        self._props = NegligenceAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "NegligenceAnalysisProperties":
        return self._props


class NegligenceAnalysisViewer(NegligenceAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class NegligenceAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def propertyOwnerFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("propertyOwnerFactors"))

    @property
    def vehicleOperatorFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("vehicleOperatorFactors"))

    @property
    def thirdPartyFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("thirdPartyFactors"))

    @property
    def institutionalFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("institutionalFactors"))

    @property
    def maintenanceFailures(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("maintenanceFailures"))

    @property
    def warningDeficiencies(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("warningDeficiencies"))

    @property
    def dutyOfCareBreaches(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("dutyOfCareBreaches"))

    @property
    def statutoryViolations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("statutoryViolations"))

    

class NextStepsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("NextSteps")
        self._properties: typing.Set[str] = set([ "documentsNeeded",  "informationNeeded",  "contactsToReach",  "timelineForResponse",  "escalationRequired",  "automatedActions",  "manualActions", ])
        self._props = NextStepsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "NextStepsProperties":
        return self._props


class NextStepsViewer(NextStepsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class NextStepsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def documentsNeeded(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentsNeeded"))

    @property
    def informationNeeded(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("informationNeeded"))

    @property
    def contactsToReach(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contactsToReach"))

    @property
    def timelineForResponse(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timelineForResponse"))

    @property
    def escalationRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("escalationRequired"))

    @property
    def automatedActions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("automatedActions"))

    @property
    def manualActions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("manualActions"))

    

class OntarioIncomeLossStandardsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("OntarioIncomeLossStandards")
        self._properties: typing.Set[str] = set([ "averageWeeklyEarnings",  "maximumBenefitPeriod",  "returnToWorkAssumption",  "ageFactors",  "documentationRequired",  "ageSpecificGuidelines",  "evidenceBasedCalculations",  "conservativeApproach", ])
        self._props = OntarioIncomeLossStandardsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "OntarioIncomeLossStandardsProperties":
        return self._props


class OntarioIncomeLossStandardsViewer(OntarioIncomeLossStandardsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class OntarioIncomeLossStandardsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def averageWeeklyEarnings(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("averageWeeklyEarnings"))

    @property
    def maximumBenefitPeriod(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("maximumBenefitPeriod"))

    @property
    def returnToWorkAssumption(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("returnToWorkAssumption"))

    @property
    def ageFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ageFactors"))

    @property
    def documentationRequired(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentationRequired"))

    @property
    def ageSpecificGuidelines(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ageSpecificGuidelines"))

    @property
    def evidenceBasedCalculations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceBasedCalculations"))

    @property
    def conservativeApproach(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("conservativeApproach"))

    

class OntarioMedicalGuidelinesAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("OntarioMedicalGuidelines")
        self._properties: typing.Set[str] = set([ "emergencyCareTypical",  "physiotherapySessionCost",  "chiropracticSessionCost",  "diagnosticImagingCosts",  "maximumTreatmentDuration",  "documentationRequirement",  "evidenceBasedLimits",  "moderateInjuryMedicalCap",  "ageAdjustedRecovery", ])
        self._props = OntarioMedicalGuidelinesProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "OntarioMedicalGuidelinesProperties":
        return self._props


class OntarioMedicalGuidelinesViewer(OntarioMedicalGuidelinesAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class OntarioMedicalGuidelinesProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def emergencyCareTypical(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("emergencyCareTypical"))

    @property
    def physiotherapySessionCost(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("physiotherapySessionCost"))

    @property
    def chiropracticSessionCost(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("chiropracticSessionCost"))

    @property
    def diagnosticImagingCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("diagnosticImagingCosts"))

    @property
    def maximumTreatmentDuration(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("maximumTreatmentDuration"))

    @property
    def documentationRequirement(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentationRequirement"))

    @property
    def evidenceBasedLimits(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceBasedLimits"))

    @property
    def moderateInjuryMedicalCap(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("moderateInjuryMedicalCap"))

    @property
    def ageAdjustedRecovery(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ageAdjustedRecovery"))

    

class OntarioPainSufferingGuideAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("OntarioPainSufferingGuide")
        self._properties: typing.Set[str] = set([ "minorInjuryRange",  "moderateInjuryRange",  "severeInjuryRange",  "catastrophicInjuryRange",  "slipFallBenchmarks",  "andrewsCap2025",  "deductible2025",  "conservativeApproach", ])
        self._props = OntarioPainSufferingGuideProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "OntarioPainSufferingGuideProperties":
        return self._props


class OntarioPainSufferingGuideViewer(OntarioPainSufferingGuideAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class OntarioPainSufferingGuideProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def minorInjuryRange(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("minorInjuryRange"))

    @property
    def moderateInjuryRange(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("moderateInjuryRange"))

    @property
    def severeInjuryRange(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("severeInjuryRange"))

    @property
    def catastrophicInjuryRange(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("catastrophicInjuryRange"))

    @property
    def slipFallBenchmarks(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("slipFallBenchmarks"))

    @property
    def andrewsCap2025(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("andrewsCap2025"))

    @property
    def deductible2025(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("deductible2025"))

    @property
    def conservativeApproach(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("conservativeApproach"))

    

class OntarioQuantumBenchmarksAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("OntarioQuantumBenchmarks")
        self._properties: typing.Set[str] = set([ "painAndSufferingBenchmarks",  "medicalCostGuidelines",  "incomeLossStandards",  "statutoryThresholds", ])
        self._props = OntarioQuantumBenchmarksProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "OntarioQuantumBenchmarksProperties":
        return self._props


class OntarioQuantumBenchmarksViewer(OntarioQuantumBenchmarksAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class OntarioQuantumBenchmarksProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def painAndSufferingBenchmarks(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("painAndSufferingBenchmarks"))

    @property
    def medicalCostGuidelines(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalCostGuidelines"))

    @property
    def incomeLossStandards(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incomeLossStandards"))

    @property
    def statutoryThresholds(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("statutoryThresholds"))

    

class OntarioStatutoryThresholdsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("OntarioStatutoryThresholds")
        self._properties: typing.Set[str] = set([ "motorVehicleThreshold",  "nonEconomicLossDeductible",  "andrewsCapAmount",  "catastrophicThreshold",  "faultReductionRules", ])
        self._props = OntarioStatutoryThresholdsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "OntarioStatutoryThresholdsProperties":
        return self._props


class OntarioStatutoryThresholdsViewer(OntarioStatutoryThresholdsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class OntarioStatutoryThresholdsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def motorVehicleThreshold(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("motorVehicleThreshold"))

    @property
    def nonEconomicLossDeductible(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("nonEconomicLossDeductible"))

    @property
    def andrewsCapAmount(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("andrewsCapAmount"))

    @property
    def catastrophicThreshold(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("catastrophicThreshold"))

    @property
    def faultReductionRules(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("faultReductionRules"))

    

class PolicyCoverageAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("PolicyCoverageAnalysis")
        self._properties: typing.Set[str] = set([ "applicableCoverageTypes",  "coverageLimits",  "deductibleAmount",  "policyConditions",  "conditionsMet",  "coverageInterpretation",  "ambiguousTerms",  "industryStandards", ])
        self._props = PolicyCoverageAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "PolicyCoverageAnalysisProperties":
        return self._props


class PolicyCoverageAnalysisViewer(PolicyCoverageAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class PolicyCoverageAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def applicableCoverageTypes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("applicableCoverageTypes"))

    @property
    def coverageLimits(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageLimits"))

    @property
    def deductibleAmount(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("deductibleAmount"))

    @property
    def policyConditions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyConditions"))

    @property
    def conditionsMet(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("conditionsMet"))

    @property
    def coverageInterpretation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageInterpretation"))

    @property
    def ambiguousTerms(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ambiguousTerms"))

    @property
    def industryStandards(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("industryStandards"))

    

class PolicyDetailsAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("PolicyDetails")
        self._properties: typing.Set[str] = set([ "policyNumber",  "policyHolder",  "insuredVehicle",  "effectiveDate",  "expiryDate",  "coverageTypes",  "deductibleAmount",  "policyLimits",  "insuranceCompany",  "agentDetails",  "hasCompleteInformation", ])
        self._props = PolicyDetailsProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "PolicyDetailsProperties":
        return self._props


class PolicyDetailsViewer(PolicyDetailsAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class PolicyDetailsProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def policyNumber(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyNumber"))

    @property
    def policyHolder(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyHolder"))

    @property
    def insuredVehicle(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("insuredVehicle"))

    @property
    def effectiveDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("effectiveDate"))

    @property
    def expiryDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("expiryDate"))

    @property
    def coverageTypes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageTypes"))

    @property
    def deductibleAmount(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("deductibleAmount"))

    @property
    def policyLimits(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyLimits"))

    @property
    def insuranceCompany(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("insuranceCompany"))

    @property
    def agentDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("agentDetails"))

    @property
    def hasCompleteInformation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("hasCompleteInformation"))

    

class PolicyExclusionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("PolicyExclusion")
        self._properties: typing.Set[str] = set([ "exclusionType",  "exclusionText",  "applicabilityReason",  "legalBasis",  "precedentSupport",  "interpretationChallenges", ])
        self._props = PolicyExclusionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "PolicyExclusionProperties":
        return self._props


class PolicyExclusionViewer(PolicyExclusionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class PolicyExclusionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def exclusionType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exclusionType"))

    @property
    def exclusionText(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exclusionText"))

    @property
    def applicabilityReason(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("applicabilityReason"))

    @property
    def legalBasis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalBasis"))

    @property
    def precedentSupport(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("precedentSupport"))

    @property
    def interpretationChallenges(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("interpretationChallenges"))

    

class PriorityRiskAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("PriorityRiskAssessment")
        self._properties: typing.Set[str] = set([ "overallPriorityLevel",  "overallRiskScore",  "priorityDrivers",  "timelineSensitivity",  "stakeholderImpact",  "identifiedRisks",  "highestRiskCategory",  "riskMitigationPriority",  "requiresHumanReview",  "requiresLegalCounsel",  "requiresSpecialistReview",  "reviewReason",  "recommendedProcessingTimeline",  "escalationTriggers",  "urgencyJustification",  "estimatedFinancialExposure",  "costOfDelay",  "assessmentConfidence",  "uncertaintyFactors",  "immediateActions",  "processOptimizations",  "preventiveRecommendations", ])
        self._props = PriorityRiskAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "PriorityRiskAssessmentProperties":
        return self._props


class PriorityRiskAssessmentViewer(PriorityRiskAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class PriorityRiskAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def overallPriorityLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overallPriorityLevel"))

    @property
    def overallRiskScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overallRiskScore"))

    @property
    def priorityDrivers(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("priorityDrivers"))

    @property
    def timelineSensitivity(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timelineSensitivity"))

    @property
    def stakeholderImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("stakeholderImpact"))

    @property
    def identifiedRisks(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("identifiedRisks"))

    @property
    def highestRiskCategory(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("highestRiskCategory"))

    @property
    def riskMitigationPriority(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("riskMitigationPriority"))

    @property
    def requiresHumanReview(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requiresHumanReview"))

    @property
    def requiresLegalCounsel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requiresLegalCounsel"))

    @property
    def requiresSpecialistReview(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requiresSpecialistReview"))

    @property
    def reviewReason(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reviewReason"))

    @property
    def recommendedProcessingTimeline(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendedProcessingTimeline"))

    @property
    def escalationTriggers(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("escalationTriggers"))

    @property
    def urgencyJustification(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("urgencyJustification"))

    @property
    def estimatedFinancialExposure(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("estimatedFinancialExposure"))

    @property
    def costOfDelay(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("costOfDelay"))

    @property
    def assessmentConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("assessmentConfidence"))

    @property
    def uncertaintyFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyFactors"))

    @property
    def immediateActions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("immediateActions"))

    @property
    def processOptimizations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("processOptimizations"))

    @property
    def preventiveRecommendations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("preventiveRecommendations"))

    

class QuantumCalculationGuidanceAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("QuantumCalculationGuidance")
        self._properties: typing.Set[str] = set([ "totalSpecialDamages",  "totalGeneralDamages",  "totalFutureCareDamages",  "grossDamagesTotal",  "netDamagesTotal",  "quantumConfidence",  "quantumRationale",  "provincialFactors",  "uncertaintyAreas",  "recommendedExpertReports",  "settlementRange",  "litigationRisk",  "settlementStrategy", ])
        self._props = QuantumCalculationGuidanceProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "QuantumCalculationGuidanceProperties":
        return self._props


class QuantumCalculationGuidanceViewer(QuantumCalculationGuidanceAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class QuantumCalculationGuidanceProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def totalSpecialDamages(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("totalSpecialDamages"))

    @property
    def totalGeneralDamages(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("totalGeneralDamages"))

    @property
    def totalFutureCareDamages(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("totalFutureCareDamages"))

    @property
    def grossDamagesTotal(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("grossDamagesTotal"))

    @property
    def netDamagesTotal(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("netDamagesTotal"))

    @property
    def quantumConfidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("quantumConfidence"))

    @property
    def quantumRationale(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("quantumRationale"))

    @property
    def provincialFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("provincialFactors"))

    @property
    def uncertaintyAreas(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyAreas"))

    @property
    def recommendedExpertReports(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendedExpertReports"))

    @property
    def settlementRange(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("settlementRange"))

    @property
    def litigationRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("litigationRisk"))

    @property
    def settlementStrategy(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("settlementStrategy"))

    

class QuantumDamageExtractionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("QuantumDamageExtraction")
        self._properties: typing.Set[str] = set([ "medicalDamages",  "incomeLossDamages",  "careAndAssistanceCosts",  "specialDamages",  "generalDamages",  "futureCareCosts",  "faultImpactAnalysis",  "quantumGuidance",  "ontarioBenchmarks",  "validationChecks",  "uiMetadata",  "explainabilityInsights", ])
        self._props = QuantumDamageExtractionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "QuantumDamageExtractionProperties":
        return self._props


class QuantumDamageExtractionViewer(QuantumDamageExtractionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class QuantumDamageExtractionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def medicalDamages(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalDamages"))

    @property
    def incomeLossDamages(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incomeLossDamages"))

    @property
    def careAndAssistanceCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("careAndAssistanceCosts"))

    @property
    def specialDamages(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("specialDamages"))

    @property
    def generalDamages(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("generalDamages"))

    @property
    def futureCareCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("futureCareCosts"))

    @property
    def faultImpactAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("faultImpactAnalysis"))

    @property
    def quantumGuidance(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("quantumGuidance"))

    @property
    def ontarioBenchmarks(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ontarioBenchmarks"))

    @property
    def validationChecks(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("validationChecks"))

    @property
    def uiMetadata(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uiMetadata"))

    @property
    def explainabilityInsights(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("explainabilityInsights"))

    

class QuantumValidationChecksAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("QuantumValidationChecks")
        self._properties: typing.Set[str] = set([ "painSufferingValidation",  "totalDamagesValidation",  "medicalCostValidation",  "settlementRangeValidation",  "overvaluationRisk",  "recommendedAdjustments",  "comparisonToActualClaims", ])
        self._props = QuantumValidationChecksProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "QuantumValidationChecksProperties":
        return self._props


class QuantumValidationChecksViewer(QuantumValidationChecksAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class QuantumValidationChecksProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def painSufferingValidation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("painSufferingValidation"))

    @property
    def totalDamagesValidation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("totalDamagesValidation"))

    @property
    def medicalCostValidation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalCostValidation"))

    @property
    def settlementRangeValidation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("settlementRangeValidation"))

    @property
    def overvaluationRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overvaluationRisk"))

    @property
    def recommendedAdjustments(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendedAdjustments"))

    @property
    def comparisonToActualClaims(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("comparisonToActualClaims"))

    

class ResumeAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Resume")
        self._properties: typing.Set[str] = set([ "name",  "email",  "experience",  "skills", ])
        self._props = ResumeProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ResumeProperties":
        return self._props


class ResumeViewer(ResumeAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ResumeProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def name(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("name"))

    @property
    def email(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("email"))

    @property
    def experience(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("experience"))

    @property
    def skills(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("skills"))

    

class RiskAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("RiskAssessment")
        self._properties: typing.Set[str] = set([ "liabilityRisk",  "financialRisk",  "reputationalRisk",  "regulatoryRisk",  "overallRiskScore", ])
        self._props = RiskAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "RiskAssessmentProperties":
        return self._props


class RiskAssessmentViewer(RiskAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class RiskAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def liabilityRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("liabilityRisk"))

    @property
    def financialRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("financialRisk"))

    @property
    def reputationalRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reputationalRisk"))

    @property
    def regulatoryRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("regulatoryRisk"))

    @property
    def overallRiskScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overallRiskScore"))

    

class RiskFactorAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("RiskFactor")
        self._properties: typing.Set[str] = set([ "category",  "severity",  "description",  "likelihood",  "potentialImpact",  "mitigationStrategy",  "timeframeToResolve",  "requiresSpecialistReview", ])
        self._props = RiskFactorProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "RiskFactorProperties":
        return self._props


class RiskFactorViewer(RiskFactorAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class RiskFactorProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def category(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("category"))

    @property
    def severity(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("severity"))

    @property
    def description(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("description"))

    @property
    def likelihood(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("likelihood"))

    @property
    def potentialImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("potentialImpact"))

    @property
    def mitigationStrategy(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("mitigationStrategy"))

    @property
    def timeframeToResolve(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("timeframeToResolve"))

    @property
    def requiresSpecialistReview(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requiresSpecialistReview"))

    

class SparkNlpDateEntityAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SparkNlpDateEntity")
        self._properties: typing.Set[str] = set([ "text",  "normalizedDate",  "dateType",  "confidence",  "context", ])
        self._props = SparkNlpDateEntityProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SparkNlpDateEntityProperties":
        return self._props


class SparkNlpDateEntityViewer(SparkNlpDateEntityAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SparkNlpDateEntityProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def text(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("text"))

    @property
    def normalizedDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("normalizedDate"))

    @property
    def dateType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("dateType"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def context(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("context"))

    

class SparkNlpEnhancedDataAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SparkNlpEnhancedData")
        self._properties: typing.Set[str] = set([ "enhancedEntities",  "financialEntities",  "extractedDates",  "locationEntities",  "confidenceBoost",  "processingNotes", ])
        self._props = SparkNlpEnhancedDataProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SparkNlpEnhancedDataProperties":
        return self._props


class SparkNlpEnhancedDataViewer(SparkNlpEnhancedDataAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SparkNlpEnhancedDataProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def enhancedEntities(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("enhancedEntities"))

    @property
    def financialEntities(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("financialEntities"))

    @property
    def extractedDates(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("extractedDates"))

    @property
    def locationEntities(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("locationEntities"))

    @property
    def confidenceBoost(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceBoost"))

    @property
    def processingNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("processingNotes"))

    

class SparkNlpEntityAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SparkNlpEntity")
        self._properties: typing.Set[str] = set([ "text",  "entityType",  "confidence",  "context",  "startPosition",  "endPosition", ])
        self._props = SparkNlpEntityProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SparkNlpEntityProperties":
        return self._props


class SparkNlpEntityViewer(SparkNlpEntityAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SparkNlpEntityProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def text(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("text"))

    @property
    def entityType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("entityType"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def context(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("context"))

    @property
    def startPosition(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("startPosition"))

    @property
    def endPosition(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("endPosition"))

    

class SparkNlpFinancialEntityAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SparkNlpFinancialEntity")
        self._properties: typing.Set[str] = set([ "text",  "entityType",  "normalizedValue",  "confidence",  "context", ])
        self._props = SparkNlpFinancialEntityProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SparkNlpFinancialEntityProperties":
        return self._props


class SparkNlpFinancialEntityViewer(SparkNlpFinancialEntityAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SparkNlpFinancialEntityProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def text(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("text"))

    @property
    def entityType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("entityType"))

    @property
    def normalizedValue(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("normalizedValue"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def context(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("context"))

    

class SparkNlpLocationEntityAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SparkNlpLocationEntity")
        self._properties: typing.Set[str] = set([ "text",  "addressType",  "normalizedAddress",  "confidence",  "context", ])
        self._props = SparkNlpLocationEntityProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SparkNlpLocationEntityProperties":
        return self._props


class SparkNlpLocationEntityViewer(SparkNlpLocationEntityAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SparkNlpLocationEntityProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def text(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("text"))

    @property
    def addressType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("addressType"))

    @property
    def normalizedAddress(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("normalizedAddress"))

    @property
    def confidence(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidence"))

    @property
    def context(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("context"))

    

class SpecialDamagesBreakdownAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SpecialDamagesBreakdown")
        self._properties: typing.Set[str] = set([ "transportationCosts",  "accommodationCosts",  "prescriptionCosts",  "medicalSuppliesCosts",  "parkingFees",  "lostBenefits",  "otherOutOfPocketCosts",  "outOfPocketReceipts",  "specialDamagesTotal",  "specialDamagesCertainty", ])
        self._props = SpecialDamagesBreakdownProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SpecialDamagesBreakdownProperties":
        return self._props


class SpecialDamagesBreakdownViewer(SpecialDamagesBreakdownAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SpecialDamagesBreakdownProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def transportationCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("transportationCosts"))

    @property
    def accommodationCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("accommodationCosts"))

    @property
    def prescriptionCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("prescriptionCosts"))

    @property
    def medicalSuppliesCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalSuppliesCosts"))

    @property
    def parkingFees(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("parkingFees"))

    @property
    def lostBenefits(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("lostBenefits"))

    @property
    def otherOutOfPocketCosts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("otherOutOfPocketCosts"))

    @property
    def outOfPocketReceipts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("outOfPocketReceipts"))

    @property
    def specialDamagesTotal(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("specialDamagesTotal"))

    @property
    def specialDamagesCertainty(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("specialDamagesCertainty"))

    

class StructuredCircumstancesAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("StructuredCircumstances")
        self._properties: typing.Set[str] = set([ "trafficViolations",  "rightOfWayFactors",  "vehicleConditions",  "roadConditions",  "visitorStatus",  "locationType",  "hazardType",  "warningsPosted",  "mitigationEfforts",  "dutyOfCareLevel",  "breachFactors",  "causationChain",  "contributingFactors",  "mitigatingFactors",  "atFaultParties", ])
        self._props = StructuredCircumstancesProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "StructuredCircumstancesProperties":
        return self._props


class StructuredCircumstancesViewer(StructuredCircumstancesAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class StructuredCircumstancesProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def trafficViolations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("trafficViolations"))

    @property
    def rightOfWayFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("rightOfWayFactors"))

    @property
    def vehicleConditions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("vehicleConditions"))

    @property
    def roadConditions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("roadConditions"))

    @property
    def visitorStatus(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("visitorStatus"))

    @property
    def locationType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("locationType"))

    @property
    def hazardType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("hazardType"))

    @property
    def warningsPosted(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("warningsPosted"))

    @property
    def mitigationEfforts(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("mitigationEfforts"))

    @property
    def dutyOfCareLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("dutyOfCareLevel"))

    @property
    def breachFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("breachFactors"))

    @property
    def causationChain(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("causationChain"))

    @property
    def contributingFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contributingFactors"))

    @property
    def mitigatingFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("mitigatingFactors"))

    @property
    def atFaultParties(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("atFaultParties"))

    

class SupportingEvidenceAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SupportingEvidence")
        self._properties: typing.Set[str] = set([ "evidenceType",  "evidenceDescription",  "evidenceSource",  "evidenceStrength",  "evidenceReliability",  "evidenceImpact", ])
        self._props = SupportingEvidenceProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SupportingEvidenceProperties":
        return self._props


class SupportingEvidenceViewer(SupportingEvidenceAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SupportingEvidenceProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def evidenceType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceType"))

    @property
    def evidenceDescription(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceDescription"))

    @property
    def evidenceSource(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceSource"))

    @property
    def evidenceStrength(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceStrength"))

    @property
    def evidenceReliability(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceReliability"))

    @property
    def evidenceImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceImpact"))

    

class ThirdPartyInformationNeededAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ThirdPartyInformationNeeded")
        self._properties: typing.Set[str] = set([ "thirdPartyType",  "thirdPartyContact",  "informationNeeded",  "legalBasisForRequest",  "voluntaryVsMandatory",  "costImplications",  "alternativeApproaches", ])
        self._props = ThirdPartyInformationNeededProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ThirdPartyInformationNeededProperties":
        return self._props


class ThirdPartyInformationNeededViewer(ThirdPartyInformationNeededAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ThirdPartyInformationNeededProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def thirdPartyType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("thirdPartyType"))

    @property
    def thirdPartyContact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("thirdPartyContact"))

    @property
    def informationNeeded(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("informationNeeded"))

    @property
    def legalBasisForRequest(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("legalBasisForRequest"))

    @property
    def voluntaryVsMandatory(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("voluntaryVsMandatory"))

    @property
    def costImplications(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("costImplications"))

    @property
    def alternativeApproaches(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("alternativeApproaches"))

    

class TreatmentRecordAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("TreatmentRecord")
        self._properties: typing.Set[str] = set([ "treatmentType",  "provider",  "treatmentDate",  "treatmentDetails",  "treatmentDuration",  "treatmentCost",  "ongoing", ])
        self._props = TreatmentRecordProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "TreatmentRecordProperties":
        return self._props


class TreatmentRecordViewer(TreatmentRecordAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class TreatmentRecordProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def treatmentType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentType"))

    @property
    def provider(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("provider"))

    @property
    def treatmentDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentDate"))

    @property
    def treatmentDetails(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentDetails"))

    @property
    def treatmentDuration(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentDuration"))

    @property
    def treatmentCost(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("treatmentCost"))

    @property
    def ongoing(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("ongoing"))

    

class UIMetadataAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("UIMetadata")
        self._properties: typing.Set[str] = set([ "entityMappings",  "explainabilityData",  "documentHighlights",  "confidenceBreakdown",  "colorScheme",  "analysisFlow", ])
        self._props = UIMetadataProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "UIMetadataProperties":
        return self._props


class UIMetadataViewer(UIMetadataAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class UIMetadataProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def entityMappings(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("entityMappings"))

    @property
    def explainabilityData(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("explainabilityData"))

    @property
    def documentHighlights(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentHighlights"))

    @property
    def confidenceBreakdown(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceBreakdown"))

    @property
    def colorScheme(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("colorScheme"))

    @property
    def analysisFlow(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("analysisFlow"))

    

class UncertaintyAreaAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("UncertaintyArea")
        self._properties: typing.Set[str] = set([ "uncertaintyType",  "uncertaintyDescription",  "uncertaintyImpact",  "resolutionApproach",  "resolutionTimeline",  "uncertaintyRisk", ])
        self._props = UncertaintyAreaProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "UncertaintyAreaProperties":
        return self._props


class UncertaintyAreaViewer(UncertaintyAreaAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class UncertaintyAreaProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def uncertaintyType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyType"))

    @property
    def uncertaintyDescription(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyDescription"))

    @property
    def uncertaintyImpact(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyImpact"))

    @property
    def resolutionApproach(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("resolutionApproach"))

    @property
    def resolutionTimeline(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("resolutionTimeline"))

    @property
    def uncertaintyRisk(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("uncertaintyRisk"))

    

class VisualizationDataAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("VisualizationData")
        self._properties: typing.Set[str] = set([ "chartType",  "chartData",  "colorScheme",  "annotations",  "interactiveElements", ])
        self._props = VisualizationDataProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "VisualizationDataProperties":
        return self._props


class VisualizationDataViewer(VisualizationDataAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class VisualizationDataProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def chartType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("chartType"))

    @property
    def chartData(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("chartData"))

    @property
    def colorScheme(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("colorScheme"))

    @property
    def annotations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("annotations"))

    @property
    def interactiveElements(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("interactiveElements"))

    

class ZurichEmailClassificationResultAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ZurichEmailClassificationResult")
        self._properties: typing.Set[str] = set([ "isClaimRelated",  "claimType",  "workflowAction",  "confidenceScore",  "urgencyLevel",  "canadianJurisdiction",  "attachmentAnalysis",  "claimIndicators",  "riskAssessment",  "classificationReasoning",  "recommendedNextSteps",  "flagsForHumanReview",  "processingNotes",  "canadianLegalConsiderations", ])
        self._props = ZurichEmailClassificationResultProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ZurichEmailClassificationResultProperties":
        return self._props


class ZurichEmailClassificationResultViewer(ZurichEmailClassificationResultAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ZurichEmailClassificationResultProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def isClaimRelated(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("isClaimRelated"))

    @property
    def claimType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimType"))

    @property
    def workflowAction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("workflowAction"))

    @property
    def confidenceScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceScore"))

    @property
    def urgencyLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("urgencyLevel"))

    @property
    def canadianJurisdiction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("canadianJurisdiction"))

    @property
    def attachmentAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachmentAnalysis"))

    @property
    def claimIndicators(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimIndicators"))

    @property
    def riskAssessment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("riskAssessment"))

    @property
    def classificationReasoning(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("classificationReasoning"))

    @property
    def recommendedNextSteps(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendedNextSteps"))

    @property
    def flagsForHumanReview(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("flagsForHumanReview"))

    @property
    def processingNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("processingNotes"))

    @property
    def canadianLegalConsiderations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("canadianLegalConsiderations"))

    



class AttachmentMismatchTypeAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("AttachmentMismatchType")
        self._values: typing.Set[str] = set([ "NO_MISMATCH",  "MENTIONS_BUT_MISSING",  "PROVIDED_BUT_NOT_MENTIONED", ])
        self._vals = AttachmentMismatchTypeValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "AttachmentMismatchTypeValues":
        return self._vals


class AttachmentMismatchTypeViewer(AttachmentMismatchTypeAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class AttachmentMismatchTypeValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def NO_MISMATCH(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NO_MISMATCH"))
    

    @property
    def MENTIONS_BUT_MISSING(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MENTIONS_BUT_MISSING"))
    

    @property
    def PROVIDED_BUT_NOT_MENTIONED(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PROVIDED_BUT_NOT_MENTIONED"))
    

    

class CanadianProvinceAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("CanadianProvince")
        self._values: typing.Set[str] = set([ "ALBERTA",  "BRITISH_COLUMBIA",  "MANITOBA",  "NEW_BRUNSWICK",  "NEWFOUNDLAND_LABRADOR",  "NORTHWEST_TERRITORIES",  "NOVA_SCOTIA",  "NUNAVUT",  "ONTARIO",  "PRINCE_EDWARD_ISLAND",  "QUEBEC",  "SASKATCHEWAN",  "YUKON",  "UNKNOWN", ])
        self._vals = CanadianProvinceValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "CanadianProvinceValues":
        return self._vals


class CanadianProvinceViewer(CanadianProvinceAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class CanadianProvinceValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def ALBERTA(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("ALBERTA"))
    

    @property
    def BRITISH_COLUMBIA(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("BRITISH_COLUMBIA"))
    

    @property
    def MANITOBA(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MANITOBA"))
    

    @property
    def NEW_BRUNSWICK(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NEW_BRUNSWICK"))
    

    @property
    def NEWFOUNDLAND_LABRADOR(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NEWFOUNDLAND_LABRADOR"))
    

    @property
    def NORTHWEST_TERRITORIES(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NORTHWEST_TERRITORIES"))
    

    @property
    def NOVA_SCOTIA(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NOVA_SCOTIA"))
    

    @property
    def NUNAVUT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NUNAVUT"))
    

    @property
    def ONTARIO(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("ONTARIO"))
    

    @property
    def PRINCE_EDWARD_ISLAND(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PRINCE_EDWARD_ISLAND"))
    

    @property
    def QUEBEC(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("QUEBEC"))
    

    @property
    def SASKATCHEWAN(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("SASKATCHEWAN"))
    

    @property
    def YUKON(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("YUKON"))
    

    @property
    def UNKNOWN(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("UNKNOWN"))
    

    

class ClaimTypeAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("ClaimType")
        self._values: typing.Set[str] = set([ "LIABILITY",  "PROPERTY",  "AUTO",  "MEDICAL_MALPRACTICE",  "PRODUCT_LIABILITY",  "ENVIRONMENTAL",  "WORKERS_COMPENSATION",  "PROFESSIONAL_LIABILITY",  "CYBER_LIABILITY",  "NOT_CLAIM", ])
        self._vals = ClaimTypeValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "ClaimTypeValues":
        return self._vals


class ClaimTypeViewer(ClaimTypeAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class ClaimTypeValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LIABILITY"))
    

    @property
    def PROPERTY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PROPERTY"))
    

    @property
    def AUTO(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("AUTO"))
    

    @property
    def MEDICAL_MALPRACTICE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDICAL_MALPRACTICE"))
    

    @property
    def PRODUCT_LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PRODUCT_LIABILITY"))
    

    @property
    def ENVIRONMENTAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("ENVIRONMENTAL"))
    

    @property
    def WORKERS_COMPENSATION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("WORKERS_COMPENSATION"))
    

    @property
    def PROFESSIONAL_LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PROFESSIONAL_LIABILITY"))
    

    @property
    def CYBER_LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CYBER_LIABILITY"))
    

    @property
    def NOT_CLAIM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NOT_CLAIM"))
    

    

class ClaimTypeDetailedAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("ClaimTypeDetailed")
        self._values: typing.Set[str] = set([ "AUTO",  "PROPERTY",  "LIABILITY",  "WORKERS_COMPENSATION",  "MEDICAL_MALPRACTICE",  "PROFESSIONAL_LIABILITY",  "PRODUCT_LIABILITY",  "CYBER_LIABILITY",  "ENVIRONMENTAL",  "GENERAL",  "UNKNOWN", ])
        self._vals = ClaimTypeDetailedValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "ClaimTypeDetailedValues":
        return self._vals


class ClaimTypeDetailedViewer(ClaimTypeDetailedAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class ClaimTypeDetailedValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def AUTO(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("AUTO"))
    

    @property
    def PROPERTY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PROPERTY"))
    

    @property
    def LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LIABILITY"))
    

    @property
    def WORKERS_COMPENSATION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("WORKERS_COMPENSATION"))
    

    @property
    def MEDICAL_MALPRACTICE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDICAL_MALPRACTICE"))
    

    @property
    def PROFESSIONAL_LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PROFESSIONAL_LIABILITY"))
    

    @property
    def PRODUCT_LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PRODUCT_LIABILITY"))
    

    @property
    def CYBER_LIABILITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CYBER_LIABILITY"))
    

    @property
    def ENVIRONMENTAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("ENVIRONMENTAL"))
    

    @property
    def GENERAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("GENERAL"))
    

    @property
    def UNKNOWN(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("UNKNOWN"))
    

    

class ContactRoleAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("ContactRole")
        self._values: typing.Set[str] = set([ "CLAIMANT",  "INSURED",  "OTHER_DRIVER",  "WITNESS",  "PASSENGER",  "ADJUSTER",  "LAWYER",  "POLICE_OFFICER",  "MEDICAL_PROFESSIONAL",  "PROPERTY_OWNER",  "CONTRACTOR",  "EXPERT_WITNESS", ])
        self._vals = ContactRoleValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "ContactRoleValues":
        return self._vals


class ContactRoleViewer(ContactRoleAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class ContactRoleValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def CLAIMANT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CLAIMANT"))
    

    @property
    def INSURED(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("INSURED"))
    

    @property
    def OTHER_DRIVER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("OTHER_DRIVER"))
    

    @property
    def WITNESS(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("WITNESS"))
    

    @property
    def PASSENGER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PASSENGER"))
    

    @property
    def ADJUSTER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("ADJUSTER"))
    

    @property
    def LAWYER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LAWYER"))
    

    @property
    def POLICE_OFFICER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("POLICE_OFFICER"))
    

    @property
    def MEDICAL_PROFESSIONAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDICAL_PROFESSIONAL"))
    

    @property
    def PROPERTY_OWNER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PROPERTY_OWNER"))
    

    @property
    def CONTRACTOR(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CONTRACTOR"))
    

    @property
    def EXPERT_WITNESS(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("EXPERT_WITNESS"))
    

    

class CoverageDecisionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("CoverageDecision")
        self._values: typing.Set[str] = set([ "NOT_COVERED",  "COVERED",  "INFORMATION_REQUIRED", ])
        self._vals = CoverageDecisionValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "CoverageDecisionValues":
        return self._vals


class CoverageDecisionViewer(CoverageDecisionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class CoverageDecisionValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def NOT_COVERED(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NOT_COVERED"))
    

    @property
    def COVERED(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("COVERED"))
    

    @property
    def INFORMATION_REQUIRED(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("INFORMATION_REQUIRED"))
    

    

class CoverageReasonAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("CoverageReason")
        self._values: typing.Set[str] = set([ "POLICY_TERMS_CLEAR",  "LEGAL_PRECEDENT",  "REGULATORY_REQUIREMENT",  "STANDARD_INTERPRETATION",  "EXCLUSION_APPLIES",  "OUTSIDE_POLICY_PERIOD",  "CONDITION_NOT_MET",  "INSUFFICIENT_EVIDENCE", ])
        self._vals = CoverageReasonValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "CoverageReasonValues":
        return self._vals


class CoverageReasonViewer(CoverageReasonAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class CoverageReasonValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def POLICY_TERMS_CLEAR(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("POLICY_TERMS_CLEAR"))
    

    @property
    def LEGAL_PRECEDENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LEGAL_PRECEDENT"))
    

    @property
    def REGULATORY_REQUIREMENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REGULATORY_REQUIREMENT"))
    

    @property
    def STANDARD_INTERPRETATION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("STANDARD_INTERPRETATION"))
    

    @property
    def EXCLUSION_APPLIES(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("EXCLUSION_APPLIES"))
    

    @property
    def OUTSIDE_POLICY_PERIOD(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("OUTSIDE_POLICY_PERIOD"))
    

    @property
    def CONDITION_NOT_MET(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CONDITION_NOT_MET"))
    

    @property
    def INSUFFICIENT_EVIDENCE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("INSUFFICIENT_EVIDENCE"))
    

    

class ExitPathAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("ExitPath")
        self._values: typing.Set[str] = set([ "DOCUMENTS_INSUFFICIENT",  "POLICY_LOOKUP_NEEDED",  "PROCEED_TO_LEVEL02", ])
        self._vals = ExitPathValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "ExitPathValues":
        return self._vals


class ExitPathViewer(ExitPathAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class ExitPathValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def DOCUMENTS_INSUFFICIENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("DOCUMENTS_INSUFFICIENT"))
    

    @property
    def POLICY_LOOKUP_NEEDED(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("POLICY_LOOKUP_NEEDED"))
    

    @property
    def PROCEED_TO_LEVEL02(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PROCEED_TO_LEVEL02"))
    

    

class IncidentTypeAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("IncidentType")
        self._values: typing.Set[str] = set([ "COLLISION",  "WEATHER",  "THEFT",  "FIRE",  "VANDALISM",  "MEDICAL_EVENT",  "SLIP_FALL",  "WORKPLACE_INJURY",  "WATER_DAMAGE",  "CYBER_ATTACK",  "OTHER", ])
        self._vals = IncidentTypeValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "IncidentTypeValues":
        return self._vals


class IncidentTypeViewer(IncidentTypeAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class IncidentTypeValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def COLLISION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("COLLISION"))
    

    @property
    def WEATHER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("WEATHER"))
    

    @property
    def THEFT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("THEFT"))
    

    @property
    def FIRE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("FIRE"))
    

    @property
    def VANDALISM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("VANDALISM"))
    

    @property
    def MEDICAL_EVENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDICAL_EVENT"))
    

    @property
    def SLIP_FALL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("SLIP_FALL"))
    

    @property
    def WORKPLACE_INJURY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("WORKPLACE_INJURY"))
    

    @property
    def WATER_DAMAGE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("WATER_DAMAGE"))
    

    @property
    def CYBER_ATTACK(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CYBER_ATTACK"))
    

    @property
    def OTHER(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("OTHER"))
    

    

class InformationSourceAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("InformationSource")
        self._values: typing.Set[str] = set([ "CLAIMANT",  "THIRD_PARTY",  "INSURANCE_AGENT",  "EXPERT_ASSESSMENT",  "LEGAL_COUNSEL",  "REGULATORY_AUTHORITY", ])
        self._vals = InformationSourceValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "InformationSourceValues":
        return self._vals


class InformationSourceViewer(InformationSourceAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class InformationSourceValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def CLAIMANT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CLAIMANT"))
    

    @property
    def THIRD_PARTY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("THIRD_PARTY"))
    

    @property
    def INSURANCE_AGENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("INSURANCE_AGENT"))
    

    @property
    def EXPERT_ASSESSMENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("EXPERT_ASSESSMENT"))
    

    @property
    def LEGAL_COUNSEL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LEGAL_COUNSEL"))
    

    @property
    def REGULATORY_AUTHORITY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REGULATORY_AUTHORITY"))
    

    

class InjurySeverityAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("InjurySeverity")
        self._values: typing.Set[str] = set([ "MINOR",  "MODERATE",  "SEVERE",  "CATASTROPHIC", ])
        self._vals = InjurySeverityValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "InjurySeverityValues":
        return self._vals


class InjurySeverityViewer(InjurySeverityAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class InjurySeverityValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def MINOR(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MINOR"))
    

    @property
    def MODERATE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MODERATE"))
    

    @property
    def SEVERE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("SEVERE"))
    

    @property
    def CATASTROPHIC(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CATASTROPHIC"))
    

    

class MedicalComplexityAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("MedicalComplexity")
        self._values: typing.Set[str] = set([ "LOW",  "MODERATE",  "HIGH",  "CATASTROPHIC", ])
        self._vals = MedicalComplexityValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "MedicalComplexityValues":
        return self._vals


class MedicalComplexityViewer(MedicalComplexityAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class MedicalComplexityValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def LOW(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LOW"))
    

    @property
    def MODERATE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MODERATE"))
    

    @property
    def HIGH(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("HIGH"))
    

    @property
    def CATASTROPHIC(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CATASTROPHIC"))
    

    

class PriorityLevelAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("PriorityLevel")
        self._values: typing.Set[str] = set([ "URGENT",  "HIGH",  "NORMAL",  "LOW", ])
        self._vals = PriorityLevelValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "PriorityLevelValues":
        return self._vals


class PriorityLevelViewer(PriorityLevelAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class PriorityLevelValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def URGENT(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("URGENT"))
    

    @property
    def HIGH(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("HIGH"))
    

    @property
    def NORMAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("NORMAL"))
    

    @property
    def LOW(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LOW"))
    

    

class RiskCategoryAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("RiskCategory")
        self._values: typing.Set[str] = set([ "FINANCIAL",  "LEGAL",  "REGULATORY",  "REPUTATIONAL",  "OPERATIONAL",  "FRAUD",  "COVERAGE", ])
        self._vals = RiskCategoryValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "RiskCategoryValues":
        return self._vals


class RiskCategoryViewer(RiskCategoryAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class RiskCategoryValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def FINANCIAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("FINANCIAL"))
    

    @property
    def LEGAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LEGAL"))
    

    @property
    def REGULATORY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REGULATORY"))
    

    @property
    def REPUTATIONAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("REPUTATIONAL"))
    

    @property
    def OPERATIONAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("OPERATIONAL"))
    

    @property
    def FRAUD(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("FRAUD"))
    

    @property
    def COVERAGE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("COVERAGE"))
    

    

class RiskLevelAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("RiskLevel")
        self._values: typing.Set[str] = set([ "LOW",  "MEDIUM",  "HIGH",  "CRITICAL", ])
        self._vals = RiskLevelValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "RiskLevelValues":
        return self._vals


class RiskLevelViewer(RiskLevelAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class RiskLevelValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def LOW(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LOW"))
    

    @property
    def MEDIUM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDIUM"))
    

    @property
    def HIGH(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("HIGH"))
    

    @property
    def CRITICAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CRITICAL"))
    

    

class TreatmentTypeAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("TreatmentType")
        self._values: typing.Set[str] = set([ "EMERGENCY_CARE",  "PHYSIOTHERAPY",  "CHIROPRACTIC",  "MASSAGE_THERAPY",  "SURGICAL",  "MEDICATION",  "DIAGNOSTIC_IMAGING",  "PSYCHOLOGICAL",  "OCCUPATIONAL_THERAPY",  "OTHER_MEDICAL", ])
        self._vals = TreatmentTypeValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "TreatmentTypeValues":
        return self._vals


class TreatmentTypeViewer(TreatmentTypeAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class TreatmentTypeValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def EMERGENCY_CARE(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("EMERGENCY_CARE"))
    

    @property
    def PHYSIOTHERAPY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PHYSIOTHERAPY"))
    

    @property
    def CHIROPRACTIC(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CHIROPRACTIC"))
    

    @property
    def MASSAGE_THERAPY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MASSAGE_THERAPY"))
    

    @property
    def SURGICAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("SURGICAL"))
    

    @property
    def MEDICATION(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDICATION"))
    

    @property
    def DIAGNOSTIC_IMAGING(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("DIAGNOSTIC_IMAGING"))
    

    @property
    def PSYCHOLOGICAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("PSYCHOLOGICAL"))
    

    @property
    def OCCUPATIONAL_THERAPY(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("OCCUPATIONAL_THERAPY"))
    

    @property
    def OTHER_MEDICAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("OTHER_MEDICAL"))
    

    

class UrgencyLevelAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("UrgencyLevel")
        self._values: typing.Set[str] = set([ "CRITICAL",  "HIGH",  "MEDIUM",  "LOW", ])
        self._vals = UrgencyLevelValues(self._bldr, self._values)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "UrgencyLevelValues":
        return self._vals


class UrgencyLevelViewer(UrgencyLevelAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    def list_values(self) -> typing.List[typing.Tuple[str, EnumValueViewer]]:
        return [(name, EnumValueViewer(self._bldr.value(name))) for name in self._values]


class UrgencyLevelValues:
    def __init__(self, enum_bldr: EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values

    

    @property
    def CRITICAL(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("CRITICAL"))
    

    @property
    def HIGH(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("HIGH"))
    

    @property
    def MEDIUM(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("MEDIUM"))
    

    @property
    def LOW(self) -> EnumValueViewer:
        return EnumValueViewer(self.__bldr.value("LOW"))
    

    


__all__ = ["TypeBuilder"]