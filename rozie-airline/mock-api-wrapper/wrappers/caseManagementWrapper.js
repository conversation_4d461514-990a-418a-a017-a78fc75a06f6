const { ApiResp } = require("../helpers/api-helper.js")
const { axiosPostRequest } = require("../helpers/axios-helper.js")

const caseManagementEndpoint = process.env.CASE_MANAGEMENT_ENDPOINT
const caseManagementApikey = process.env.CASE_MANAGEMENT_APIKEY

/**
 * @function caseManagementWrapper
 * @description 
 * This function retrieves case management details for a given customer by sending a POST request to an external API.
 * It processes the response to return only the two most recent case objects sorted by `inProgressTimeStamp`.
 * If no cases are found, an appropriate message is returned.
 * 
 * @param {string} customer_id - The ID of the customer for whom to retrieve case details.
 * 
 * @returns {Promise<Object>} - A response object containing up to 2 recent case details or an error message.
 * 
 * @throws {Error} - Throws an error if there is an issue with retrieving case details.
 */
const caseManagementWrapper = async (customer_id) => {
	const headers = {
		"Content-Type": "application/json",
		"x-api-key": caseManagementApikey
	}

	const requestBody = {
		"customerId": customer_id,
		"caseStatus": "IN PROGRESS",
		"caseCategory": "Airline"
	}

	try {
		let caseManagementResponseList = []
		const caseManagementResponse = await axiosPostRequest(caseManagementEndpoint, requestBody, headers)
        
		if (caseManagementResponse.status === 404) {
			console.log("No cases found for customer id")
			return ApiResp({
				status: "Error",
				message: "No cases found for customer_id",
				response: caseManagementResponseList
			}, 200)
		}
        
		if (caseManagementResponse && caseManagementResponse.caseList && caseManagementResponse.caseList.length > 0) {
			
			const sortedCases = caseManagementResponse.caseList.sort((a, b) => 
				new Date(b.createdAt) - new Date(a.createdAt)
			)
			const recentCase = sortedCases[0]

			return ApiResp({
				status: "Success",
				message: "Recent case details retrieved successfully",
				response: recentCase
			})
		} else {
			return ApiResp({
				status: "Error",
				message: "No case records found for the customer id",
				response: caseManagementResponseList
			}, 200)
		}

	} catch (error) {
		console.error("Error:", error)
		return ApiResp({
			status: "Error",
			message: "Internal Server Error"
		}, 500)
	}
}

module.exports = {
	caseManagementWrapper
}
