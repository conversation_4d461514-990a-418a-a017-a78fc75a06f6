import json
import requests
from .logging_helper import print_info_log


def baggage_wrapper_api(event):
    """
    This function is used to call the baggage status API and return the result
    """
    body = json.loads(event["body"])
    customer_id = body["concepts"]["CustomerId"]["CustomerId"]
    url = "https://88y2mfkbjg.execute-api.ca-central-1.amazonaws.com/dev/baggageWrapper"
    payload = json.dumps({"customer_id": customer_id})
    headers = {
        "x-api-key": "yN7l1i92cP2m16K2FqWIe8tmysaB8QK1TvQxDeKh",
        "Content-Type": "application/json",
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    print_info_log("", "response: status", response.status_code)
    status_message = ""
    if response.status_code == 200:
        response_json = response.json()
        print_info_log("", "INFO", response_json)
        if response_json["status"] == "Error":
            status_message = "Sorry, But I did not find any active baggage details associated with you profile."
        else:
            customer_details = get_customer_profile(customer_id)
            baggage_details = response_json["response"]
            schedule_details = get_schedule_details(response_json["response"][0]["flight_schedule_id"])
            status_message = format_baggage_status_message(baggage_details, customer_details, schedule_details)
        result = {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "api_result_type": "success",
                    "concepts": {
                        "BaggageStatusMessage": {"BaggageStatusMessage": status_message}
                    },
                }
            ),
        }
        print_info_log("", "result", result)
        return result
    result = {
        "statusCode": 400,
        "body": json.dumps(
            {
                "api_result_type": "failure",
                "concepts": {"BaggageStatusMessage": {"BaggageStatusMessage": ""}},
            }
        ),
    }
    print_info_log("", "result", result)
    return result


def baggage_wrapper_api_info():
    """
    Info API for Baggage Status API
    """
    return {
        "statusCode": 200,
        "body": json.dumps(
            {
                "api_name": "Baggage Status",
                "api_description": "This API return the baggage status message.",
                "input_concepts": {
                    "CustomerId": "true",
                },
                "output_concepts": {"BaggageStatusMessage": "true"},
            }
        ),
    }


def format_baggage_status_message(baggage_data, customer_details, schedule_details):
    status_groups = {}
    
    for bag in baggage_data:
        status = bag["baggage_status"]
        if status not in status_groups:
            status_groups[status] = []
        status_groups[status].append(bag)

    messages = []
    customer_msg = f"{customer_details['name']}, "
    for status, bags in status_groups.items():
        flight_msg = f"on flight {'-'.join(schedule_details['flight_id'])} from {schedule_details['departureAirport']} to {schedule_details['arrivalAirport']}"
        baggage_tags = [
            f"baggage tag id {'-'.join(bag['baggage_id'])}"
            for bag in bags
        ]

        if status.lower() == "landed":
            messages.append(
                f"Your checked bags, with {', '.join(baggage_tags)} {flight_msg} is arrived at airport. You can collect it form baggage claim area."
            )
        elif status.lower() == "in-flight":
            messages.append(
                f"Your bags, with {', '.join(baggage_tags)} {flight_msg} is still in-flight. We will keep you updated."
            )
        elif status.lower() == "delayed":
            messages.append(
                f"We apologize for the inconvenience, Your bags, with {', '.join(baggage_tags)} {flight_msg} is been delayed. We will update you shortly."
            )
        elif status.lower() == "checked-in":
            messages.append(
                f"We will load Your bags, with {', '.join(baggage_tags)} {flight_msg},once flight docked to terminal."
            )

    final_message = customer_msg + " ".join(messages)

    return final_message


def get_customer_profile(customer_id):
    url = "https://6tvdvyr6jl.execute-api.ca-central-1.amazonaws.com/dev/getCustomerProfile"
    payload = json.dumps({"customer_id": customer_id})
    headers = {
        "x-api-key": "zE2qnbBkv38CHG2Bwj4vs8B1njD8kIua5vKsfhnn",
        "Content-Type": "application/json",
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    if response.status_code == 200:
        response_json = response.json()
        return response_json["data"]
    return None

def get_schedule_details(schedule_id):
    url = "https://7kddk5snmh.execute-api.ca-central-1.amazonaws.com/dev/get_flight_status"
    payload = {"schedule_id": schedule_id}
    headers = {
        "x-api-key": "kDQwh4X5tO4Kor98sy7Vz6dfwwp1ARMA2B3Mkz6m",
        "Content-Type": "application/json",
    }
    response = requests.request("GET", url, headers=headers, params=payload)
    if response.status_code == 200:
        response_json = response.json()
        return response_json["data"]
    return None