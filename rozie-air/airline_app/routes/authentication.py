import traceback
from typing import Dict, Optional
from fastapi import APIRouter, HTTPException, Depends
from airline_app.adapters.auth_adapter import get_api_key
from airline_app.helpers.logging_helper import print_info_log

from airline_app.services.authentication import generate_otp, validate_otp


authentication_router = APIRouter(prefix="/authentication")

@authentication_router.post("/generate_otp", dependencies=[Depends(get_api_key)])
async def generate_otp_api(body: dict):
    try:
        chat_id = body.get("chat_id")
        customer_id = body.get("customer_id")
        otp = generate_otp(chat_id, customer_id)
        if not otp:
            raise HTTPException(status_code=404, detail="Error while generating OTP")
        return {
            "status": "Success",
            "suggest_action": "Let customer know that OTP is been shared to there registered email."
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})

@authentication_router.post("/validate_otp", dependencies=[Depends(get_api_key)])
async def validate_otp_api(body: dict):
    try:
        chat_id = body.get("chat_id", "")
        otp = body.get("otp", "")
        is_valid = validate_otp(chat_id, otp)

        if is_valid:
            return {
                "status": "Success",
                "suggest_action": "OTP verification successful."
            }
        return {
            "status": "Failed",
            "suggest_action": "OTP verification failed."
        }
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})