const SUCCESS = "Success"
const FAILURE = "Failure"
const SUCCESS_CODE = 200
const ERROR_CODE = 500


/**
 * Forms the response body for API responses.
 *
 * @param {string} status - The status of the response ("Success" or "Failure").
 * @param {string} message - The message associated with the response.
 * @param {Array} response - The response data (optional, defaults to an empty array).
 * @returns {Object} - The formatted response body.
 */
function formResponseBody(status, message, response = []) {
	if (status === "Success") {
		return {
			status: SUCCESS,
			statusCode: SUCCESS_CODE,
			message: message,
			response: response,
		}
	} else {
		return {
			status: FAILURE,
			statusCode: ERROR_CODE,
			message: message,
			response: response,
		}
	}
}

/**
 * Retrieves paginated results from a model.
 *
 * @param {number} page - The page number of the results.
 * @param {number} limit - The number of results per page.
 * @param {Array} model - The model containing the results.
 * @returns {Promise<Object>} - A promise that resolves with the paginated results.
 */
async function paginatedResults(page, limit, model) {
	console.log("function: paginatedResults")
	const startIndex = (page - 1) * limit
	const endIndex = page * limit
	const results = {}
	results.results = model.slice(startIndex, endIndex)
	results.currentPage = page
	results.currentPageSize = results.results.length
	results.totalSize = model.length
	return results
}

/**
 * Returns the security headers for API responses.
 *
 * @returns {Object} - The security headers.
 */
function returnSecurityHeaders() {
	return {
		"Content-Type": "application/json",
		"Access-Control-Allow-Origin": "*",
		"X-Content-Type-Options": "nosniff",
		"X-XSS-Protection": "1;mode=block",
		"Strict-Transport-Security": "max-age=63072000;includeSubDomains",
	}
}

/**
 * Generates the API response object.
 *
 * @param {Object} body - The response body.
 * @param {number} code - The response status code.
 * @returns {Object} - The API response object.
 */
const ApiResp = (body = { success: true }, code = 200) => {
	return {
		statusCode: code,
		headers: returnSecurityHeaders(),
		body: JSON.stringify(body),
	}
}

module.exports = {
	formResponseBody,
	paginatedResults,
	ApiResp
}
