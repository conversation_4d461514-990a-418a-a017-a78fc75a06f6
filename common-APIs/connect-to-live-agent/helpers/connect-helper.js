const { backOff } = require('exponential-backoff')
const {
    ConnectClient,
    StartOutboundVoiceContactCommand,
} = require('@aws-sdk/client-connect')

// Initialize AWS Connect client with the specified region
const connectClient = new ConnectClient({ region: 'ca-central-1' })

const startOutboundVoiceContact = async (instanceId, destinationPhoneNumber, sourcePhoneNumber, contactFlowId, attributes) => {
    try {
        const startOutboundVoiceContactParam = {
            DestinationPhoneNumber: destinationPhoneNumber,
            InstanceId: instanceId,
            SourcePhoneNumber: sourcePhoneNumber,
            ContactFlowId: contactFlowId,
            Attributes: { ...attributes },
        }
        const startOutboundVoiceContactCommand = new StartOutboundVoiceContactCommand(startOutboundVoiceContactParam)
        const response = await backOff(
            () => connectClient.send(startOutboundVoiceContactCommand),
            {
                jitter: 'full',
                numOfAttempts: 4,
                retry: (err) => {
                    console.log(err)
                    return true
                },
            }
        )
        return { ContactId: response.ContactId }
    } catch (error) {
        console.log(error)
        throw new Error('Error listing routing profile queues')
    }
}

module.exports = {
    startOutboundVoiceContact,
}