import datetime as dt
import uuid
import os
from fastapi import FastAP<PERSON>, Response
from fastapi.middleware.cors import CORSMiddleware
from middleware_app.common.attributes import (
    set_attributes,
    set_business_config,
    ECS_ATTRIBUTES,
)
from middleware_app.common.schemas import SuccessResponse
from middleware_app.routers import health, webchat_router
from middleware_app.helpers.logging_helper import (
    set_logging_level,
    get_log_level,
)

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
set_logging_level(get_log_level())
app.include_router(health.router, prefix="/readiness", tags=["readiness"])
app.include_router(webchat_router.router, prefix="/webchat", tags=["webchat"])


@app.get("/")
async def root(response: Response) -> SuccessResponse:
    """Root Path"""
    response.headers["Access-Control-Allow-Origin"] = "*"
    return SuccessResponse(
        id=str(uuid.uuid4()),
        object="Scc.AutoGen.Root",
        created=int(dt.datetime.now().timestamp()),
        message="Application is running.",
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("__main__:app", reload=True, host="0.0.0.0", port=80)
