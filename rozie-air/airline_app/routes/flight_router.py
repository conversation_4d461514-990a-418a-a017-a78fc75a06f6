import traceback

from decimal import Decimal
from typing import Optional, Dict
from fastapi import APIRouter, HTTPException, Depends
from airline_app.adapters.auth_adapter import get_api_key

from airline_app.model.models import FlightRequest

from airline_app.helpers.logging_helper import print_info_log


from airline_app.services.flight import create_flight_entry, get_available_flight_seat, get_flight_status, get_available_meal_services

flight_router = APIRouter(prefix="/flight")

# @flight_router.get("/available-seats/{flight_id}", response_model=List[str])
# async def get_available_seats(flight_id: str):
#     try:
#         table = get_dynamo_table()
#         response = table.query(
#             KeyConditionExpression=Key('flight_id').eq(flight_id)
#         )

#         if 'Items' not in response or not response['Items']:
#             raise HTTPException(status_code=404, detail="Flight not found or no seats available")

#         available_seats = [item['seat_id'] for item in response['Items'] if item.get('is_available', False)]
#         return available_seats

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error fetching available seats: {str(e)}")


@flight_router.post("/create-flight/", response_model=Dict)
def create_flight(flight_request: FlightRequest):
    try:
        flight = {
            "flight_id": flight_request.flight_id,
            "airline": flight_request.airline,
            "arrival_airport": flight_request.arrival_airport,
            "departure_airport": flight_request.departure_airport,
            "flight_number": flight_request.flight_number,
            "seats": [],
        }
        print(flight)
        # Process seat configuration
        for class_name, config in flight_request.seat_config.items():
            base_price = Decimal(config.base_price)

            for row in config.rows:
                for seat_type, columns in config.seats.dict().items():
                    for column in columns:
                        seat_number = f"{row}{column}"
                        price = base_price

                        if seat_type == "window":
                            price *= Decimal(1.2)
                        elif seat_type == "aisle":
                            price *= Decimal(1.1)

                        flight["seats"].append(
                            {
                                "seat_number": seat_number,
                                "type": seat_type,
                                "class": class_name,
                                "price": round(price, 2),
                            }
                        )
        print(flight)
        create_flight_entry(flight)
        return {"Success": True}
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})

@flight_router.get("/available-seats", dependencies=[Depends(get_api_key)])
async def get_available_seats(flight_id: str):
    try:
        available_seats = get_available_flight_seat(flight_id)
        if not available_seats:
            raise HTTPException(status_code=404, detail="Flight not found or no seats available")
        return available_seats
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})

@flight_router.get("/flight-status", dependencies=[Depends(get_api_key)])
async def get_flight_status_route(flight_id: str):
    try:
        flight_status = get_flight_status(flight_id)
        if not flight_status:
            raise HTTPException(status_code=404, detail="Flight not found")
        return flight_status
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})
    
@flight_router.get("/get-meal-options", dependencies=[Depends(get_api_key)])
async def get_meal_options(flight_id: str, seat_class: str):
    try:
        meal_services = get_available_meal_services(flight_id, seat_class)
        if not meal_services:
            raise HTTPException(status_code=404, detail="No meal services offered for provided seat class in provided flight")
        return meal_services
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})
