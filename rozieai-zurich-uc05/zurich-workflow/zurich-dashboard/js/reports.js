// Reports Module for Zurich Claims Analytics
class ReportsManager {
    constructor() {
        this.supabaseClient = null;
        this.claims = [];
        this.filteredClaims = [];
        this.currentFilters = {
            dateRange: 30,
            claimType: '',
            agent: ''
        };
        this.init();
    }

    async init() {
        console.log('📈 Initializing Reports Manager...');
        this.waitForAuth();
    }

    waitForAuth() {
        if (window.authManager && window.authManager.getSupabaseClient()) {
            this.supabaseClient = window.authManager.getSupabaseClient();
            this.setupEventListeners();
            console.log('✅ Reports Manager ready');
        } else {
            setTimeout(() => this.waitForAuth(), 100);
        }
    }

    setupEventListeners() {
        // Filter controls
        const dateRangeSelect = document.getElementById('date-range');
        const claimTypeSelect = document.getElementById('claim-type');
        const agentSelect = document.getElementById('agent-filter');
        const applyFiltersBtn = document.getElementById('apply-filters');

        if (dateRangeSelect) {
            dateRangeSelect.addEventListener('change', (e) => {
                this.currentFilters.dateRange = parseInt(e.target.value);
            });
        }

        if (claimTypeSelect) {
            claimTypeSelect.addEventListener('change', (e) => {
                this.currentFilters.claimType = e.target.value;
            });
        }

        if (agentSelect) {
            agentSelect.addEventListener('change', (e) => {
                this.currentFilters.agent = e.target.value;
            });
        }

        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.applyFilters();
            });
        }
    }

    async loadReportsData() {
        console.log('📊 Loading reports data...');
        
        try {
            // Load claims data
            await this.loadClaims();
            
            // Apply current filters
            this.applyFilters();
            
            // Generate charts
            this.generateCharts();
            
            console.log('✅ Reports data loaded successfully');
            
        } catch (error) {
            console.error('❌ Error loading reports data:', error);
            this.showError('Failed to load reports data. Please refresh the page.');
        }
    }

    async loadClaims() {
        try {
            // Calculate date range
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - this.currentFilters.dateRange);

            const { data: claims, error } = await this.supabaseClient
                .from('claims')
                .select('*')
                .gte('created_at', startDate.toISOString())
                .lte('created_at', endDate.toISOString())
                .order('created_at', { ascending: false });

            if (error) {
                throw error;
            }

            this.claims = claims || [];
            console.log(`📋 Loaded ${this.claims.length} claims for reports`);
            
        } catch (error) {
            console.error('❌ Error loading claims for reports:', error);
            this.claims = [];
        }
    }

    applyFilters() {
        this.filteredClaims = this.claims.filter(claim => {
            // Date range filter (already applied in loadClaims)
            
            // Claim type filter
            if (this.currentFilters.claimType && 
                claim.claim_type?.toLowerCase() !== this.currentFilters.claimType.toLowerCase()) {
                return false;
            }
            
            // Agent filter
            if (this.currentFilters.agent && 
                claim.assigned_agent?.toLowerCase() !== this.currentFilters.agent.toLowerCase()) {
                return false;
            }
            
            return true;
        });

        console.log(`📊 Filtered to ${this.filteredClaims.length} claims`);
        this.generateCharts();
    }

    generateCharts() {
        this.generateStatusChart();
        this.generateTimeChart();
    }

    generateStatusChart() {
        const statusChart = document.getElementById('status-chart');
        if (!statusChart) return;

        // Count claims by status
        const statusCounts = {};
        this.filteredClaims.forEach(claim => {
            const status = this.normalizeStatus(claim.status || claim.workflow_status);
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });

        // Create simple bar chart representation
        const chartData = Object.entries(statusCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 6); // Top 6 statuses

        if (chartData.length === 0) {
            statusChart.innerHTML = '<p>No data available for the selected filters</p>';
            return;
        }

        const maxCount = Math.max(...chartData.map(([,count]) => count));
        
        statusChart.innerHTML = `
            <div class="chart-bars">
                ${chartData.map(([status, count]) => `
                    <div class="chart-bar-container">
                        <div class="chart-bar-label">${status}</div>
                        <div class="chart-bar-wrapper">
                            <div class="chart-bar" style="width: ${(count / maxCount) * 100}%"></div>
                            <span class="chart-bar-value">${count}</span>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        // Add chart styles if not already added
        this.addChartStyles();
    }

    generateTimeChart() {
        const timeChart = document.getElementById('time-chart');
        if (!timeChart) return;

        // Group claims by week
        const weeklyData = {};
        this.filteredClaims.forEach(claim => {
            const date = new Date(claim.created_at);
            const weekStart = this.getWeekStart(date);
            const weekKey = weekStart.toISOString().split('T')[0];
            
            weeklyData[weekKey] = (weeklyData[weekKey] || 0) + 1;
        });

        const chartData = Object.entries(weeklyData)
            .sort(([a], [b]) => new Date(a) - new Date(b))
            .slice(-8); // Last 8 weeks

        if (chartData.length === 0) {
            timeChart.innerHTML = '<p>No data available for the selected time range</p>';
            return;
        }

        const maxCount = Math.max(...chartData.map(([,count]) => count));
        
        timeChart.innerHTML = `
            <div class="chart-line">
                ${chartData.map(([week, count], index) => `
                    <div class="chart-point-container">
                        <div class="chart-point" style="height: ${(count / maxCount) * 200}px">
                            <div class="chart-point-value">${count}</div>
                        </div>
                        <div class="chart-point-label">${this.formatWeek(week)}</div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    normalizeStatus(status) {
        const statusMap = {
            'NEW': 'New',
            'PROCESSING': 'Processing',
            'UNDER_REVIEW': 'Under Review',
            'PENDING_DOCUMENTS': 'Pending Docs',
            'APPROVED': 'Approved',
            'DENIED': 'Denied',
            'COMPLETED': 'Completed',
            'CANCELLED': 'Cancelled'
        };

        return statusMap[status?.toUpperCase()] || status || 'Unknown';
    }

    getWeekStart(date) {
        const d = new Date(date);
        const day = d.getDay();
        const diff = d.getDate() - day;
        return new Date(d.setDate(diff));
    }

    formatWeek(weekString) {
        const date = new Date(weekString);
        return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric' 
        });
    }

    addChartStyles() {
        if (document.getElementById('chart-styles')) return;

        const style = document.createElement('style');
        style.id = 'chart-styles';
        style.textContent = `
            .chart-bars {
                display: flex;
                flex-direction: column;
                gap: 12px;
                padding: 16px 0;
            }
            
            .chart-bar-container {
                display: flex;
                align-items: center;
                gap: 12px;
            }
            
            .chart-bar-label {
                min-width: 100px;
                font-size: 12px;
                font-weight: 500;
                color: var(--zd-color-grey-600);
            }
            
            .chart-bar-wrapper {
                flex: 1;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .chart-bar {
                height: 24px;
                background: linear-gradient(90deg, var(--zurich-blue), var(--zurich-dark-blue));
                border-radius: 4px;
                min-width: 2px;
                transition: width 0.3s ease;
            }
            
            .chart-bar-value {
                font-size: 12px;
                font-weight: 600;
                color: var(--zd-color-grey-800);
                min-width: 20px;
            }
            
            .chart-line {
                display: flex;
                align-items: end;
                justify-content: space-between;
                gap: 8px;
                padding: 16px 0;
                height: 250px;
            }
            
            .chart-point-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                flex: 1;
            }
            
            .chart-point {
                width: 100%;
                max-width: 40px;
                background: linear-gradient(180deg, var(--zurich-blue), var(--zurich-dark-blue));
                border-radius: 4px 4px 0 0;
                position: relative;
                min-height: 4px;
                display: flex;
                align-items: flex-start;
                justify-content: center;
                transition: height 0.3s ease;
            }
            
            .chart-point-value {
                position: absolute;
                top: -20px;
                font-size: 10px;
                font-weight: 600;
                color: var(--zd-color-grey-800);
                background: white;
                padding: 2px 4px;
                border-radius: 2px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            
            .chart-point-label {
                margin-top: 8px;
                font-size: 10px;
                color: var(--zd-color-grey-600);
                text-align: center;
            }
        `;
        
        document.head.appendChild(style);
    }

    exportData() {
        console.log('📤 Exporting reports data...');
        
        // Create CSV data
        const headers = ['Claim Reference', 'Status', 'Created Date', 'Assigned Agent', 'Type'];
        const csvData = [
            headers.join(','),
            ...this.filteredClaims.map(claim => [
                claim.claim_reference,
                this.normalizeStatus(claim.status || claim.workflow_status),
                new Date(claim.created_at).toLocaleDateString(),
                claim.assigned_agent || 'Unassigned',
                claim.claim_type || 'Unknown'
            ].join(','))
        ].join('\n');

        // Download CSV
        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `zurich-claims-report-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        if (window.zurichDashboard) {
            window.zurichDashboard.handleSuccess('Report exported successfully');
        }
    }

    showError(message) {
        console.error('Reports Error:', message);
        if (window.zurichDashboard) {
            window.zurichDashboard.handleError(new Error(message), 'Reports');
        }
    }

    // Public methods
    refresh() {
        this.loadReportsData();
    }

    getFilteredClaims() {
        return this.filteredClaims;
    }

    getCurrentFilters() {
        return { ...this.currentFilters };
    }
}

// Initialize reports manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.reportsManager = new ReportsManager();
});
