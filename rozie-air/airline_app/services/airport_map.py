from typing import Dict, List
from airline_app.helpers.dynamo_helper import retrieve_dynamo_table_data, query_items_by_gsi, query_items_by_primary_key
from airline_app.helpers.logging_helper import print_info_log, print_error_log
from airline_app.common.constants import AIRPORT_MAPPING_TABLE
from boto3.dynamodb.conditions import Key

def get_all_airport_map():
    """
    Retrieves all airport map data from the DynamoDB table.

    Returns:
    - dict: A dictionary containing status, message and items retrieved from the DynamoDB table.

    This function uses the `retrieve_dynamo_table_data` method from the `dynamo_helper` 
    to retrieve all items from the "rozie-air-resources-airport-mapping-dev" table.
    """
    try:
        airport_map = retrieve_dynamo_table_data(AIRPORT_MAPPING_TABLE)
        print_info_log('','airport_map',airport_map)
        return {
            "status": "Success",
            "message": "Successfully retrieved all airport map data",
            "data": airport_map
        }
    except Exception as e:
        print_error_log('', 'function: get_all_airport_map', f"Error while retrieving data from table rozie-air-resources-airport-mapping-dev: {str(e)}")
        return {
            "status": "Error",
            "message": f"Error while retrieving data from table rozie-air-resources-airport-mapping-dev: {str(e)}",
            "data": []
        }
    
def get_airport_map_details(airport_map_input, input_type) -> Dict:
    """
    Retrieve airport map data based on input type.
    :param airport_map_input: List of city names or airport codes.
    :param input_type: Specifies if the input is 'city' or 'code'.
    :return: Dictionary containing status, message, and data.
    """
    try:
        result = []
        if input_type == "city":
            for city_name in airport_map_input:
                city_name = city_name.replace(" ", "").lower()
                # Query for city using GSI
                key_condition_expression = "airportCityName = :city_name"
                expression_attribute_values = {
                    ":city_name": city_name
                }

                city_results = query_items_by_gsi(
                    table_name=AIRPORT_MAPPING_TABLE,
                    index_name="AirportCityNameIndex", 
                    key_condition_expression=key_condition_expression,
                    expression_attribute_values=expression_attribute_values
                )
                if city_results:
                    for city_result in city_results:
                        city_result.pop("airportCityName", None)
                        result.append(city_result)
        elif input_type == "code":
            for airport_code in airport_map_input:
                # Query for code using primary key
                key_condition_expression = "airportCode = :airport_code"
                expression_attribute_values = {
                    ":airport_code": airport_code
                }

                code_results = query_items_by_primary_key(
                    table_name=AIRPORT_MAPPING_TABLE,
                    key_condition_expression=key_condition_expression,
                    expression_attribute_values=expression_attribute_values
                )
                if code_results:
                    result.extend(code_results)

        
        if not result:
            return get_all_airport_map()

        return {
            "status": "Success",
            "message": f"Successfully retrieved airport map data by {input_type}",
            "data": result
        }

    except Exception as e:
        print_error_log('', 'function: get_airport_map_details', f"Error while retrieving data: {str(e)}")
        return {
            "status": "Error",
            "message": f"Error while retrieving data: {str(e)}",
            "data": []
        }