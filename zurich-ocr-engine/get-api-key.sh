#!/bin/bash

# Zurich OCR Engine - API Key Retrieval Script
# This script helps you get the API key value after deployment

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

STAGE=${1:-dev}
REGION=${2:-ca-central-1}

echo -e "${BLUE}🔑 Zurich OCR Engine - API Key Retrieval${NC}"
echo -e "${BLUE}===========================================${NC}"
echo ""

# Get API Key ID from CloudFormation stack
echo -e "${YELLOW}📋 Getting API Key information...${NC}"
STACK_NAME="zurich-ocr-engine-${STAGE}"

API_KEY_ID=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --query "Stacks[0].Outputs[?OutputKey=='ZurichOCRApiKeyId'].OutputValue" \
  --output text 2>/dev/null || echo "")

if [ -z "$API_KEY_ID" ] || [ "$API_KEY_ID" = "None" ]; then
  echo -e "${YELLOW}⚠️  Could not find API Key ID in CloudFormation outputs${NC}"
  echo -e "${YELLOW}   Searching for API Key by name...${NC}"
  
  API_KEY_NAME="zurich-ocr-engine-${STAGE}-Api-Key"
  API_KEY_ID=$(aws apigateway get-api-keys \
    --region "$REGION" \
    --query "items[?name=='$API_KEY_NAME'].id" \
    --output text 2>/dev/null || echo "")
fi

if [ -z "$API_KEY_ID" ] || [ "$API_KEY_ID" = "None" ]; then
  echo -e "${YELLOW}❌ API Key not found. Make sure the stack is deployed successfully.${NC}"
  exit 1
fi

# Get API Key Value
echo -e "${YELLOW}🔍 Retrieving API Key value...${NC}"
API_KEY_VALUE=$(aws apigateway get-api-key \
  --api-key "$API_KEY_ID" \
  --include-value \
  --region "$REGION" \
  --query "value" \
  --output text 2>/dev/null || echo "")

if [ -z "$API_KEY_VALUE" ] || [ "$API_KEY_VALUE" = "None" ]; then
  echo -e "${YELLOW}❌ Could not retrieve API Key value${NC}"
  exit 1
fi

# Get API Gateway URLs
API_GATEWAY_URL=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --query "Stacks[0].Outputs[?OutputKey=='ZurichOCRApiGatewayUrl'].OutputValue" \
  --output text 2>/dev/null || echo "")

CUSTOM_DOMAIN_URL=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --query "Stacks[0].Outputs[?OutputKey=='ZurichOCRApiCustomDomainUrl'].OutputValue" \
  --output text 2>/dev/null || echo "")

DIRECT_URL=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --query "Stacks[0].Outputs[?OutputKey=='ZurichOCRDirectUrl'].OutputValue" \
  --output text 2>/dev/null || echo "")

# Display Results
echo ""
echo -e "${GREEN}✅ API Key Retrieved Successfully!${NC}"
echo -e "${GREEN}=================================${NC}"
echo ""
echo -e "${BLUE}🔑 API Key Information:${NC}"
echo -e "   Key ID: ${API_KEY_ID}"
echo -e "   Key Value: ${GREEN}${API_KEY_VALUE}${NC}"
echo ""
echo -e "${BLUE}🌐 API Endpoints:${NC}"

if [ ! -z "$CUSTOM_DOMAIN_URL" ] && [ "$CUSTOM_DOMAIN_URL" != "None" ]; then
  echo -e "   Custom Domain: ${GREEN}${CUSTOM_DOMAIN_URL}${NC} (Recommended)"
fi

if [ ! -z "$API_GATEWAY_URL" ] && [ "$API_GATEWAY_URL" != "None" ]; then
  echo -e "   API Gateway: ${GREEN}${API_GATEWAY_URL}${NC}"
fi

if [ ! -z "$DIRECT_URL" ] && [ "$DIRECT_URL" != "None" ]; then
  echo -e "   Direct Access: ${GREEN}https://${DIRECT_URL}${NC} (No API key required)"
fi

echo ""
echo -e "${BLUE}📝 Usage Examples:${NC}"
echo ""
echo -e "${YELLOW}# Health Check (with API key)${NC}"
if [ ! -z "$CUSTOM_DOMAIN_URL" ] && [ "$CUSTOM_DOMAIN_URL" != "None" ]; then
  echo "curl -H \"x-api-key: ${API_KEY_VALUE}\" \"${CUSTOM_DOMAIN_URL}/api/v1/health\""
elif [ ! -z "$API_GATEWAY_URL" ] && [ "$API_GATEWAY_URL" != "None" ]; then
  echo "curl -H \"x-api-key: ${API_KEY_VALUE}\" \"${API_GATEWAY_URL}/api/v1/health\""
fi

echo ""
echo -e "${YELLOW}# OCR Text Extraction (with API key)${NC}"
if [ ! -z "$CUSTOM_DOMAIN_URL" ] && [ "$CUSTOM_DOMAIN_URL" != "None" ]; then
  echo "curl -X POST -H \"x-api-key: ${API_KEY_VALUE}\" \\"
  echo "  -F \"file=@document.pdf\" \\"
  echo "  \"${CUSTOM_DOMAIN_URL}/api/v1/extract-text\""
elif [ ! -z "$API_GATEWAY_URL" ] && [ "$API_GATEWAY_URL" != "None" ]; then
  echo "curl -X POST -H \"x-api-key: ${API_KEY_VALUE}\" \\"
  echo "  -F \"file=@document.pdf\" \\"
  echo "  \"${API_GATEWAY_URL}/api/v1/extract-text\""
fi

echo ""
echo -e "${YELLOW}# Direct Access (no API key required)${NC}"
if [ ! -z "$DIRECT_URL" ] && [ "$DIRECT_URL" != "None" ]; then
  echo "curl \"https://${DIRECT_URL}/api/v1/health\""
fi

echo ""
echo -e "${BLUE}💡 Notes:${NC}"
echo -e "   • API Gateway provides rate limiting and monitoring"
echo -e "   • Direct URL bypasses API Gateway (use for internal access)"
echo -e "   • Custom domain provides cleaner URLs"
echo -e "   • Store the API key securely"
echo ""
