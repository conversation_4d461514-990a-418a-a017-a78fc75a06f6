###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import baml_py
from enum import Enum

from pydantic import BaseModel, ConfigDict

from typing_extensions import TypeAlias, Literal
from typing import Dict, Generic, List, Optional, TypeVar, Union


T = TypeVar('T')
CheckName = TypeVar('CheckName', bound=str)

class Check(BaseModel):
    name: str
    expression: str
    status: str
class Checked(BaseModel, Generic[T,CheckName]):
    value: T
    checks: Dict[CheckName, Check]

def get_checks(checks: Dict[CheckName, Check]) -> List[Check]:
    return list(checks.values())

def all_succeeded(checks: Dict[CheckName, Check]) -> bool:
    return all(check.status == "succeeded" for check in get_checks(checks))



class AttachmentMismatchType(str, Enum):
    
    NO_MISMATCH = "NO_MISMATCH"
    MENTIONS_BUT_MISSING = "MENTIONS_BUT_MISSING"
    PROVIDED_BUT_NOT_MENTIONED = "PROVIDED_BUT_NOT_MENTIONED"

class CanadianProvince(str, Enum):
    
    ALBERTA = "ALBERTA"
    BRITISH_COLUMBIA = "BRITISH_COLUMBIA"
    MANITOBA = "MANITOBA"
    NEW_BRUNSWICK = "NEW_BRUNSWICK"
    NEWFOUNDLAND_LABRADOR = "NEWFOUNDLAND_LABRADOR"
    NORTHWEST_TERRITORIES = "NORTHWEST_TERRITORIES"
    NOVA_SCOTIA = "NOVA_SCOTIA"
    NUNAVUT = "NUNAVUT"
    ONTARIO = "ONTARIO"
    PRINCE_EDWARD_ISLAND = "PRINCE_EDWARD_ISLAND"
    QUEBEC = "QUEBEC"
    SASKATCHEWAN = "SASKATCHEWAN"
    YUKON = "YUKON"
    UNKNOWN = "UNKNOWN"

class ClaimType(str, Enum):
    
    LIABILITY = "LIABILITY"
    PROPERTY = "PROPERTY"
    AUTO = "AUTO"
    MEDICAL_MALPRACTICE = "MEDICAL_MALPRACTICE"
    PRODUCT_LIABILITY = "PRODUCT_LIABILITY"
    ENVIRONMENTAL = "ENVIRONMENTAL"
    WORKERS_COMPENSATION = "WORKERS_COMPENSATION"
    PROFESSIONAL_LIABILITY = "PROFESSIONAL_LIABILITY"
    CYBER_LIABILITY = "CYBER_LIABILITY"
    NOT_CLAIM = "NOT_CLAIM"

class ClaimTypeDetailed(str, Enum):
    
    AUTO = "AUTO"
    PROPERTY = "PROPERTY"
    LIABILITY = "LIABILITY"
    WORKERS_COMPENSATION = "WORKERS_COMPENSATION"
    MEDICAL_MALPRACTICE = "MEDICAL_MALPRACTICE"
    PROFESSIONAL_LIABILITY = "PROFESSIONAL_LIABILITY"
    PRODUCT_LIABILITY = "PRODUCT_LIABILITY"
    CYBER_LIABILITY = "CYBER_LIABILITY"
    ENVIRONMENTAL = "ENVIRONMENTAL"
    GENERAL = "GENERAL"
    UNKNOWN = "UNKNOWN"

class ContactRole(str, Enum):
    
    CLAIMANT = "CLAIMANT"
    INSURED = "INSURED"
    OTHER_DRIVER = "OTHER_DRIVER"
    WITNESS = "WITNESS"
    PASSENGER = "PASSENGER"
    ADJUSTER = "ADJUSTER"
    LAWYER = "LAWYER"
    POLICE_OFFICER = "POLICE_OFFICER"
    MEDICAL_PROFESSIONAL = "MEDICAL_PROFESSIONAL"
    PROPERTY_OWNER = "PROPERTY_OWNER"
    CONTRACTOR = "CONTRACTOR"
    EXPERT_WITNESS = "EXPERT_WITNESS"

class CoverageDecision(str, Enum):
    
    NOT_COVERED = "NOT_COVERED"
    COVERED = "COVERED"
    INFORMATION_REQUIRED = "INFORMATION_REQUIRED"

class CoverageReason(str, Enum):
    
    POLICY_TERMS_CLEAR = "POLICY_TERMS_CLEAR"
    LEGAL_PRECEDENT = "LEGAL_PRECEDENT"
    REGULATORY_REQUIREMENT = "REGULATORY_REQUIREMENT"
    STANDARD_INTERPRETATION = "STANDARD_INTERPRETATION"
    EXCLUSION_APPLIES = "EXCLUSION_APPLIES"
    OUTSIDE_POLICY_PERIOD = "OUTSIDE_POLICY_PERIOD"
    CONDITION_NOT_MET = "CONDITION_NOT_MET"
    INSUFFICIENT_EVIDENCE = "INSUFFICIENT_EVIDENCE"

class ExitPath(str, Enum):
    
    DOCUMENTS_INSUFFICIENT = "DOCUMENTS_INSUFFICIENT"
    POLICY_LOOKUP_NEEDED = "POLICY_LOOKUP_NEEDED"
    PROCEED_TO_LEVEL02 = "PROCEED_TO_LEVEL02"

class IncidentType(str, Enum):
    
    COLLISION = "COLLISION"
    WEATHER = "WEATHER"
    THEFT = "THEFT"
    FIRE = "FIRE"
    VANDALISM = "VANDALISM"
    MEDICAL_EVENT = "MEDICAL_EVENT"
    SLIP_FALL = "SLIP_FALL"
    WORKPLACE_INJURY = "WORKPLACE_INJURY"
    WATER_DAMAGE = "WATER_DAMAGE"
    CYBER_ATTACK = "CYBER_ATTACK"
    OTHER = "OTHER"

class InformationSource(str, Enum):
    
    CLAIMANT = "CLAIMANT"
    THIRD_PARTY = "THIRD_PARTY"
    INSURANCE_AGENT = "INSURANCE_AGENT"
    EXPERT_ASSESSMENT = "EXPERT_ASSESSMENT"
    LEGAL_COUNSEL = "LEGAL_COUNSEL"
    REGULATORY_AUTHORITY = "REGULATORY_AUTHORITY"

class InjurySeverity(str, Enum):
    
    MINOR = "MINOR"
    MODERATE = "MODERATE"
    SEVERE = "SEVERE"
    CATASTROPHIC = "CATASTROPHIC"

class MedicalComplexity(str, Enum):
    
    LOW = "LOW"
    MODERATE = "MODERATE"
    HIGH = "HIGH"
    CATASTROPHIC = "CATASTROPHIC"

class PriorityLevel(str, Enum):
    
    URGENT = "URGENT"
    HIGH = "HIGH"
    NORMAL = "NORMAL"
    LOW = "LOW"

class RiskCategory(str, Enum):
    
    FINANCIAL = "FINANCIAL"
    LEGAL = "LEGAL"
    REGULATORY = "REGULATORY"
    REPUTATIONAL = "REPUTATIONAL"
    OPERATIONAL = "OPERATIONAL"
    FRAUD = "FRAUD"
    COVERAGE = "COVERAGE"

class RiskLevel(str, Enum):
    
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class TreatmentType(str, Enum):
    
    EMERGENCY_CARE = "EMERGENCY_CARE"
    PHYSIOTHERAPY = "PHYSIOTHERAPY"
    CHIROPRACTIC = "CHIROPRACTIC"
    MASSAGE_THERAPY = "MASSAGE_THERAPY"
    SURGICAL = "SURGICAL"
    MEDICATION = "MEDICATION"
    DIAGNOSTIC_IMAGING = "DIAGNOSTIC_IMAGING"
    PSYCHOLOGICAL = "PSYCHOLOGICAL"
    OCCUPATIONAL_THERAPY = "OCCUPATIONAL_THERAPY"
    OTHER_MEDICAL = "OTHER_MEDICAL"

class UrgencyLevel(str, Enum):
    
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"

class AccidentClassification(BaseModel):
    primaryType: Union[Literal["auto_collision"], Literal["slip_fall"], Literal["occupiers_liability"], Literal["general_liability"], Literal["product_liability"]]
    specificSubtype: str
    incidentComplexity: Union[Literal["simple"], Literal["moderate"], Literal["complex"], Literal["multi_party"]]
    applicableLaw: str
    certaintyLevel: float

class AgentObtainableInformation(BaseModel):
    informationType: str
    sourceOfInformation: str
    procedureRequired: str
    timelineToObtain: str
    costToCompany: str
    reliabilityLevel: float

class AlternativeScenario(BaseModel):
    scenario: str
    probabilityChange: float
    requiredChanges: List[str]
    likelihood: Union[Literal["HIGH"], Literal["MEDIUM"], Literal["LOW"]]
    implications: List[str]

class AnalysisColorScheme(BaseModel):
    criticalColor: str
    highPriorityColor: str
    mediumPriorityColor: str
    lowPriorityColor: str

class AnalysisDetails(BaseModel):
    extractedInformation: str
    confidenceBySection: "ConfidenceBreakdown"
    identifiedGaps: List[str]
    assumptionsMade: List[str]
    qualityAssessment: str
    riskIndicators: List[str]

class AnalysisFlow(BaseModel):
    flowSteps: List["AnalysisFlowStep"]
    decisionPoints: List["DecisionPoint"]
    dataFlow: List["DataFlowStep"]
    alternativeScenarios: List["AlternativeScenario"]

class AnalysisFlowStep(BaseModel):
    stepNumber: int
    stepName: str
    stepDescription: str
    inputData: List[str]
    outcome: str
    nextSteps: List[str]
    confidence: float

class AnalysisQuality(BaseModel):
    dataCompleteness: float
    dataReliability: float
    analysisDepth: float
    analysisConsistency: float
    expertValidation: float
    overallQuality: float
    qualityNotes: List[str]

class AttachmentAnalysis(BaseModel):
    hasAttachments: bool
    attachmentCount: int
    attachments: List["AttachmentInfo"]
    mentionsAttachments: bool
    attachmentMismatch: "AttachmentMismatchType"
    suspiciousFilenames: bool

class AttachmentInfo(BaseModel):
    filename: str
    fileType: str
    isClaimRelated: bool
    documentType: str

class CanLIILegalPrecedent(BaseModel):
    caseId: str
    caseName: str
    court: str
    jurisdiction: str
    decisionDate: str
    relevanceToCase: str
    keyPrinciple: str
    supportsCoverage: bool
    confidenceLevel: float
    canliiUrl: str

class CanadianLegalAnalysis(BaseModel):
    applicableLaw: str
    provincialRegulations: List[str]
    federalStatutes: List[str]
    legalPrecedents: List["CanLIILegalPrecedent"]
    regulatoryGuidance: List[str]
    interpretationNotes: str
    jurisdictionalCompliance: bool
    legalRiskAssessment: str

class CareAssistanceAssessment(BaseModel):
    familyCareProvided: bool
    familyCareHours: float
    familyCareValue: float
    professionalCareRequired: bool
    professionalCareHours: float
    professionalCareCosts: float
    housekeepingAssistance: bool
    housekeepingCosts: float
    personalCareNeeds: List[str]
    careAssistanceDuration: str
    totalCareAssistanceCosts: float

class CauseOfLoss(BaseModel):
    primaryCause: str
    contributingFactors: List[str]
    incidentType: "IncidentType"
    atFaultParties: List[str]
    circumstances: str
    weatherConditions: Optional[str] = None
    roadConditions: Optional[str] = None
    trafficViolations: List[str]
    negligenceFactors: List[str]
    causationChain: List[str]

class CauseOfLossMapping(BaseModel):
    primaryCause: str
    proximateCause: str
    coverageApplicability: str
    causationChain: List[str]
    concurrentCauses: List[str]
    causationAnalysis: str
    canadianCausationLaw: str

class ChartAnnotation(BaseModel):
    text: str
    position: str
    style: str
    importance: Union[Literal["HIGH"], Literal["MEDIUM"], Literal["LOW"]]
    analysisRelevance: float

class ChartDataPoint(BaseModel):
    label: str
    value: float
    color: Optional[str] = None
    metadata: Optional[str] = None
    tooltip: Optional[str] = None
    analysisImpact: float

class CircumstanceDetails(BaseModel):
    environmentalFactors: "EnvironmentalFactors"
    locationDetails: List[str]
    timingFactors: List[str]
    humanBehaviorFactors: List[str]
    equipmentConditions: List[str]

class ClaimDetails(BaseModel):
    incidentDate: Optional[str] = None
    incidentTime: Optional[str] = None
    incidentLocation: Optional[str] = None
    claimantName: Optional[str] = None
    insuredParty: Optional[str] = None
    claimType: "ClaimTypeDetailed"
    damageDescription: Optional[str] = None
    estimatedAmount: Optional[str] = None
    policeReportNumber: Optional[str] = None
    emergencyServicesInvolved: bool
    injuriesReported: bool
    propertyDamage: bool
    witnessesPresent: bool
    vehicleInvolved: Optional[str] = None
    thirdPartyInvolved: bool
    medicalInformation: Optional["MedicalSummary"] = None

class ClaimDocumentInput(BaseModel):
    claimId: str
    emailContent: str
    emailSubject: str
    attachmentNames: List[str]
    attachmentsText: List[str]
    preprocessingNotes: List[str]

class ClaimIndicators(BaseModel):
    incidentKeywords: List[str]
    damageKeywords: List[str]
    legalKeywords: List[str]
    medicalKeywords: List[str]
    timeIndicators: List[str]
    locationIndicators: List[str]
    partyIndicators: List[str]

class ClaimantInformationNeeded(BaseModel):
    documentsRequired: List[str]
    clarificationsNeeded: List[str]
    evidenceRequired: List[str]
    timelineForResponse: str
    consequencesOfNonCompliance: str
    assistanceAvailable: str

class ColorLegend(BaseModel):
    color: str
    meaning: str
    examples: List[str]

class ColorSchemeMapping(BaseModel):
    entityTypeColors: List["EntityTypeColor"]
    confidenceColorScheme: "ConfidenceColorScheme"
    analysisColorScheme: "AnalysisColorScheme"

class CommunicationAction(BaseModel):
    communicationType: str
    communicationRecipient: str
    communicationPurpose: str
    communicationTimeline: str
    communicationMethod: str
    followUpRequired: bool

class ConfidenceBreakdown(BaseModel):
    claimDetailsConfidence: float
    policyDetailsConfidence: float
    causeOfLossConfidence: float
    contactDetailsConfidence: float
    overallConfidence: float

class ConfidenceColorScheme(BaseModel):
    highConfidenceColor: str
    mediumConfidenceColor: str
    lowConfidenceColor: str

class ConfidenceContributor(BaseModel):
    contributor: str
    contribution: float
    reason: str
    sourceType: Union[Literal["DOCUMENT_QUALITY"], Literal["TEXT_CLARITY"], Literal["PATTERN_MATCH"], Literal["CROSS_VALIDATION"], Literal["EXPERT_VALIDATION"]]

class ConfidenceDistribution(BaseModel):
    highConfidenceFields: List[str]
    mediumConfidenceFields: List[str]
    lowConfidenceFields: List[str]
    averageConfidence: float

class ContactInfo(BaseModel):
    name: str
    role: "ContactRole"
    phoneNumber: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    driverLicense: Optional[str] = None
    relationship: Optional[str] = None
    insuranceInfo: Optional[str] = None
    employer: Optional[str] = None
    notes: Optional[str] = None

class ContextualFactors(BaseModel):
    analysisLevel: str
    analysisContext: str
    domainSpecificFactors: List[str]
    regulatoryFactors: List[str]
    businessRules: List[str]
    qualityFactors: List[str]

class ContradictoryEvidence(BaseModel):
    evidenceType: str
    evidenceDescription: str
    contradictionReason: str
    responseToContradiction: str
    contradictionWeight: float
    mitigationStrategy: str

class ContributoryAnalysis(BaseModel):
    claimantFactors: List[str]
    awarenessLevel: Union[Literal["fully_aware"], Literal["should_have_known"], Literal["unaware"], Literal["distracted"]]
    avoidabilityFactor: Union[Literal["easily_avoidable"], Literal["avoidable_with_care"], Literal["difficult_to_avoid"], Literal["unavoidable"]]
    behaviorFactors: List[str]
    mitigatingCircumstances: List[str]
    aggravatingCircumstances: List[str]
    contributionPercentage: float

class CoverageAnalysisDetails(BaseModel):
    analysisCompleteness: float
    analysisConfidence: float
    keyFindings: List[str]
    analysisLimitations: List[str]
    assumptionsMade: List[str]
    uncertaintyAreas: List[str]
    additionalAnalysisNeeded: List[str]

class CoverageDecisionSupport(BaseModel):
    supportingEvidence: List["SupportingEvidence"]
    contradictoryEvidence: List["ContradictoryEvidence"]
    evidenceWeighting: "EvidenceWeighting"
    expertOpinions: List["ExpertOpinion"]
    documentationQuality: "DocumentationQuality"
    evidenceGaps: List[str]

class CoverageJustification(BaseModel):
    primaryReason: "CoverageReason"
    detailedReasoning: str
    policyBasis: str
    legalBasis: str
    factualBasis: str
    industryPractice: str
    precedentSupport: List[str]
    riskFactors: List[str]
    alternativeInterpretations: List[str]
    decisionStrength: float

class CoverageMapping(BaseModel):
    causeOfLoss: str
    policySection: str
    coverageRationale: str
    coverageStrength: float
    counterArguments: List[str]
    supportingArguments: List[str]
    industryPractice: str

class DataCompletenessAssessment(BaseModel):
    overallCompletenessScore: float
    level01AnalysisCompleteness: float
    claimDetailsCompleteness: float
    ocrDocumentsCompleteness: float
    attachmentsCompleteness: float
    level01AnalysisImportance: float
    claimDetailsImportance: float
    ocrDocumentsImportance: float
    attachmentsImportance: float
    criticalMissingItems: List[str]
    minorMissingItems: List[str]
    dataQualityScore: float
    analysisReadiness: str
    improvementRecommendations: List[str]
    alternativeDataSources: List[str]
    claimTypeSpecificNeeds: str
    riskFactorsFromGaps: List[str]
    assessmentConfidence: float
    uncertaintyAreas: List[str]

class DataFlowStep(BaseModel):
    stepName: str
    sourceData: List[str]
    processing: str
    outputData: List[str]
    dataQuality: float

class DataItem(BaseModel):
    itemName: str
    isPresent: bool
    qualityScore: float
    importanceForClaimType: float
    alternativeSources: List[str]
    impactOnAnalysis: str

class DecisionPoint(BaseModel):
    decisionName: str
    decisionDescription: str
    inputFactors: List[str]
    outcome: str
    confidence: float
    alternativeOptions: List[str]

class DiagnosticResult(BaseModel):
    diagnosticType: str
    findings: str
    datePerformed: str
    facility: str
    abnormalFindings: bool
    supportsCausation: bool

class DocumentHighlight(BaseModel):
    documentId: str
    documentName: str
    pageNumber: int
    highlights: List["HighlightRegion"]

class DocumentationAction(BaseModel):
    documentType: str
    documentPurpose: str
    documentRecipient: str
    documentTimeline: str
    documentImportance: Union[Literal["CRITICAL"], Literal["HIGH"], Literal["NORMAL"], Literal["LOW"]]

class DocumentationQuality(BaseModel):
    completeness: float
    reliability: float
    consistency: float
    timeliness: float
    authentication: float
    qualityNotes: str

class EmailAttachment(BaseModel):
    filename: str
    contentType: str
    size: Optional[int] = None

class EmailForClassification(BaseModel):
    subject: str
    body: str
    senderEmail: str
    senderName: Optional[str] = None
    receivedDate: Optional[str] = None
    attachments: List["EmailAttachment"]

class EntityMapping(BaseModel):
    fieldName: str
    extractedValue: str
    originalText: str
    sourceDocument: str
    pageNumber: int
    lineNumber: Optional[int] = None
    startPosition: Optional[int] = None
    endPosition: Optional[int] = None
    boundingBox: Optional[List[float]] = None
    extractionMethod: str
    confidence: float
    highlightColor: str
    entityType: str
    relevanceScore: float

class EntityTypeColor(BaseModel):
    entityType: str
    primaryColor: str
    secondaryColor: str
    textColor: str
    weight: float

class EnvironmentalFactors(BaseModel):
    weatherConditions: str
    lightingConditions: str
    surfaceConditions: str
    visibilityFactors: List[str]
    crowdingLevel: Union[Literal["empty"], Literal["light"], Literal["moderate"], Literal["heavy"], Literal["overcrowded"]]

class EvidenceQuality(BaseModel):
    witnessStatements: List[str]
    documentaryEvidence: List[str]
    physicalEvidence: List[str]
    expertOpinions: List[str]
    overallReliability: float
    evidenceGaps: List[str]

class EvidenceWeighting(BaseModel):
    documentaryEvidence: float
    witnessTestimony: float
    expertOpinion: float
    physicalEvidence: float
    circumstantialEvidence: float
    weightingRationale: str

class ExclusionAnalysis(BaseModel):
    potentialExclusions: List["PolicyExclusion"]
    applicableExclusions: List["PolicyExclusion"]
    exclusionJustification: str
    canadianExclusionPrecedents: List["CanLIILegalPrecedent"]
    exclusionInterpretation: str
    exclusionRisk: "RiskLevel"

class ExitAnalysis(BaseModel):
    exitReason: str
    analysisProvided: "AnalysisDetails"
    nextStepsRequired: "NextSteps"
    estimatedTimeToResolution: str
    priorityLevel: "PriorityLevel"
    automationOpportunities: List[str]
    humanReviewItems: List[str]

class ExpertOpinion(BaseModel):
    expertType: str
    expertCredentials: str
    opinionSummary: str
    opinionBasis: str
    opinionReliability: float
    opinionImpact: str
    conflictingOpinions: List[str]

class ExplainabilityInsight(BaseModel):
    analysisType: Union[Literal["LIME"], Literal["SHAP"], Literal["INTEGRATED_GRADIENTS"], Literal["ATTENTION"]]
    decisionContext: str
    featureImportances: List["FeatureImportance"]
    topPositiveFactors: List["ExplanationFactor"]
    topNegativeFactors: List["ExplanationFactor"]
    confidenceContributors: List["ConfidenceContributor"]
    alternativeScenarios: List["AlternativeScenario"]
    visualizationData: "VisualizationData"
    methodologyNotes: List[str]
    contextualFactors: "ContextualFactors"

class ExplanationFactor(BaseModel):
    factor: str
    impact: float
    explanation: str
    sourceLocation: Optional[str] = None
    evidenceStrength: Union[Literal["STRONG"], Literal["MODERATE"], Literal["WEAK"]]
    supportingEvidence: List[str]

class FaultGuidance(BaseModel):
    suggestedPrimaryFault: float
    suggestedSecondaryFault: float
    faultRationale: str
    uncertaintyAreas: List[str]
    comparisonCases: List[str]
    recommendedRuleSet: str
    confidenceInGuidance: float

class FaultImpactOnQuantum(BaseModel):
    claimantFaultPercentage: float
    faultReductionApplicable: bool
    specialDamagesReduction: float
    generalDamagesReduction: float
    futureCareDamagesReduction: float
    thresholdConsiderations: List[str]
    faultImpactOnSettlement: str
    netRecoverableAmount: float

class FeatureImportance(BaseModel):
    featureName: str
    importance: float
    direction: Union[Literal["POSITIVE"], Literal["NEGATIVE"], Literal["NEUTRAL"]]
    explanation: str
    sourceText: Optional[str] = None
    confidence: float
    relatedFields: List[str]
    analysisContext: str

class FieldConfidence(BaseModel):
    fieldName: str
    confidence: float
    contributingFactors: List[str]
    uncertaintyReasons: List[str]
    sourceQuality: float
    validationStrength: float

class FieldConfidenceBreakdown(BaseModel):
    overallConfidence: float
    fieldConfidences: List["FieldConfidence"]
    uncertaintyFactors: List[str]
    confidenceDistribution: "ConfidenceDistribution"
    analysisCertainty: float

class FutureCareAssessment(BaseModel):
    futureMedicalTreatment: bool
    futureTreatmentCosts: float
    futureTherapyCosts: float
    futureCarePeriod: str
    assistiveDevices: List[str]
    assistiveDeviceCosts: float
    homeModifications: List[str]
    homeModificationCosts: float
    attendantCareRequired: bool
    attendantCareCosts: float
    totalFutureCareCosts: float
    futureCareUncertainty: float

class GeneralDamagesAssessment(BaseModel):
    painLevel: Union[Literal["minimal"], Literal["mild"], Literal["moderate"], Literal["severe"], Literal["extreme"]]
    sufferingLevel: Union[Literal["minimal"], Literal["mild"], Literal["moderate"], Literal["severe"], Literal["extreme"]]
    functionalImpairment: Union[Literal["none"], Literal["minimal"], Literal["moderate"], Literal["significant"], Literal["severe"], Literal["unknown"]]
    lifestyleImpact: Union[Literal["none"], Literal["minimal"], Literal["moderate"], Literal["significant"], Literal["severe"], Literal["unknown"]]
    psychologicalImpact: Union[Literal["none"], Literal["minimal"], Literal["moderate"], Literal["significant"], Literal["severe"], Literal["unknown"]]
    relationshipImpact: Union[Literal["none"], Literal["minimal"], Literal["moderate"], Literal["significant"], Literal["severe"], Literal["unknown"]]
    ageAtTimeOfAccident: int
    genderConsiderations: str
    permanentDisabilityFactor: float
    comparableAwards: List[str]
    generalDamagesRange: str
    recommendedGeneralDamages: float
    ontarioBenchmarkCategory: Union[Literal["minor"], Literal["moderate"], Literal["severe"], Literal["catastrophic"], Literal["unknown"]]
    benchmarkJustification: str
    conservativeEstimate: bool

class HighlightRegion(BaseModel):
    fieldName: str
    text: str
    startPos: int
    endPos: int
    boundingBox: Optional[List[float]] = None
    highlightColor: str
    confidence: float
    entityType: str
    analysisRelevance: float

class IncomeLossAssessment(BaseModel):
    preAccidentIncome: float
    employmentStatus: Union[Literal["employed"], Literal["self_employed"], Literal["unemployed"], Literal["student"], Literal["retired"], Literal["unknown"]]
    jobTitle: str
    timeOffWork: str
    workDaysLost: int
    wageReplacementAmount: float
    returnToWorkStatus: Union[Literal["full_capacity"], Literal["reduced_capacity"], Literal["modified_duties"], Literal["unable_to_return"]]
    futureEarningImpact: Union[Literal["no_impact"], Literal["temporary_reduction"], Literal["permanent_reduction"], Literal["total_disability"]]
    documentedIncomeLoss: float
    projectedFutureIncomeLoss: float
    incomeLossCertainty: float
    returnToWorkCapacity: float
    ageFactorAdjustment: Union[Literal["none"], Literal["minor"], Literal["significant"], Literal["unknown"]]

class InformationRequest(BaseModel):
    informationType: str
    informationSource: "InformationSource"
    informationDetails: str
    justification: str
    urgencyLevel: Union[Literal["IMMEDIATE"], Literal["HIGH"], Literal["NORMAL"], Literal["LOW"]]
    timelineRequired: str
    alternativeSources: List[str]
    impactOnDecision: str

class InjuryDetail(BaseModel):
    bodyPart: str
    injuryType: str
    severity: "InjurySeverity"
    description: str
    isPermanent: bool
    causationClear: bool
    priorHistory: bool

class InjuryImpactAnalysis(BaseModel):
    injurySeverityFactor: Union[Literal["minor"], Literal["moderate"], Literal["severe"], Literal["catastrophic"]]
    medicalCausationCertainty: float
    treatmentComplexity: Union[Literal["simple"], Literal["moderate"], Literal["complex"], Literal["ongoing_care"]]
    functionalImpact: Union[Literal["minimal"], Literal["moderate"], Literal["significant"], Literal["life_altering"]]
    workImpact: Union[Literal["no_time_off"], Literal["short_absence"], Literal["extended_absence"], Literal["permanent_disability"]]
    recoveryPrognosis: Union[Literal["full_recovery"], Literal["partial_recovery"], Literal["ongoing_limitations"], Literal["permanent_impairment"]]
    preExistingFactors: List[str]
    medicalDocumentationQuality: float
    liabilityAggravationFactor: float

class InteractiveElement(BaseModel):
    elementType: Union[Literal["HOVER_DETAIL"], Literal["CLICK_DRILL_DOWN"], Literal["FILTER"], Literal["ZOOM"], Literal["COMPARE"]]
    targetField: str
    action: str
    description: str
    context: str

class InvestigationAction(BaseModel):
    investigationType: str
    investigationScope: str
    investigationTimeline: str
    investigationResources: str
    investigationCost: str
    investigationExpectedOutcome: str

class LegalAction(BaseModel):
    legalActionType: str
    legalBasis: str
    legalTimeline: str
    legalCost: str
    legalRisk: str
    alternativesToLegalAction: List[str]

class Level01Analysis(BaseModel):
    claimDetails: "ClaimDetails"
    policyDetails: "PolicyDetails"
    causeOfLoss: "CauseOfLoss"
    contactDetails: List["ContactInfo"]
    documentsSufficient: bool
    policyNumberAvailable: bool
    policyDetailsComplete: bool
    exitPath: "ExitPath"
    exitAnalysis: "ExitAnalysis"
    sparkNlpInsights: Optional["SparkNlpEnhancedData"] = None
    confidenceScore: float
    processingNotes: List[str]
    dataQualityScore: float
    canadianJurisdiction: Optional[str] = None
    legalConsiderations: List[str]
    regulatoryNotes: List[str]
    uiMetadata: "UIMetadata"
    explainabilityInsights: List["ExplainabilityInsight"]
    analysisTimestamp: str
    modelVersion: str
    processingTimeMs: int

class Level01Summary(BaseModel):
    claimId: str
    claimType: str
    policyNumber: str
    incidentDate: str
    primaryCause: str
    level01Confidence: float
    level01ExitPath: str
    keyFindings: List[str]

class Level02AnalysisInput(BaseModel):
    claimId: str
    level01Analysis: "Level01Summary"
    policyDocuments: List[str]
    additionalEvidence: List[str]
    humanInputs: List[str]
    processingNotes: List[str]
    urgencyLevel: Union[Literal["IMMEDIATE"], Literal["HIGH"], Literal["NORMAL"], Literal["LOW"]]
    specialInstructions: List[str]

class Level02CoverageAnalysis(BaseModel):
    coverageDecision: "CoverageDecision"
    confidenceScore: float
    policyAnalysis: "PolicyCoverageAnalysis"
    exclusionAnalysis: "ExclusionAnalysis"
    coverageMapping: "CoverageMapping"
    causeMapping: "CauseOfLossMapping"
    coverageJustification: "CoverageJustification"
    decisionSupport: "CoverageDecisionSupport"
    canadianLegalAnalysis: "CanadianLegalAnalysis"
    medicalImpactAssessment: Optional["MedicalImpactAssessment"] = None
    claimantInformation: Optional["ClaimantInformationNeeded"] = None
    thirdPartyInformation: List["ThirdPartyInformationNeeded"]
    agentInformation: List["AgentObtainableInformation"]
    allInformationRequests: List["InformationRequest"]
    exitAnalysis: "Level02ExitAnalysis"
    analysisQuality: "AnalysisQuality"
    riskAssessment: "Level02RiskAssessment"
    uncertaintyAreas: List["UncertaintyArea"]
    uiMetadata: "UIMetadata"
    explainabilityInsights: List["ExplainabilityInsight"]
    level01Data: "Level01Summary"
    analysisTimestamp: str
    processingTimeMs: int
    modelVersion: str
    analystId: str

class Level02ExitAnalysis(BaseModel):
    exitPath: "CoverageDecision"
    exitReason: str
    coverageAnalysisProvided: "CoverageAnalysisDetails"
    nextStepsRequired: "Level02NextSteps"
    riskAssessment: "Level02RiskAssessment"
    humanReviewRequired: bool
    legalCounselRequired: bool
    timelineForResolution: str
    priorityLevel: "PriorityLevel"
    escalationTriggers: List[str]

class Level02NextSteps(BaseModel):
    immediateActions: List[str]
    shortTermActions: List[str]
    longTermActions: List[str]
    documentationRequired: List["DocumentationAction"]
    communicationRequired: List["CommunicationAction"]
    investigationRequired: List["InvestigationAction"]
    legalActionRequired: List["LegalAction"]

class Level02RiskAssessment(BaseModel):
    coverageRisk: "RiskLevel"
    legalRisk: "RiskLevel"
    financialRisk: "RiskLevel"
    reputationalRisk: "RiskLevel"
    regulatoryRisk: "RiskLevel"
    overallRisk: "RiskLevel"
    riskMitigationStrategies: List[str]
    riskMonitoringRequired: bool

class Level03AnalysisInput(BaseModel):
    claimReference: str
    province: str
    level01Analysis: str
    level02Coverage: str
    emailContent: List[str]
    sparkNlpInsights: str
    ocrTexts: List[str]
    attachmentDetails: List[str]

class Level04AnalysisInput(BaseModel):
    claimReference: str
    province: str
    level01Analysis: str
    level02Coverage: str
    level03Fault: str
    emailContent: List[str]
    sparkNlpInsights: str
    ocrTexts: List[str]
    attachmentDetails: List[str]

class LiabilityFactorExtraction(BaseModel):
    accidentClassification: "AccidentClassification"
    negligenceFactors: "NegligenceAnalysis"
    contributoryFactors: "ContributoryAnalysis"
    evidenceQuality: "EvidenceQuality"
    circumstanceDetails: "CircumstanceDetails"
    structuredForRules: "StructuredCircumstances"
    injuryImpactAnalysis: Optional["InjuryImpactAnalysis"] = None
    faultGuidance: "FaultGuidance"
    uiMetadata: "UIMetadata"
    explainabilityInsights: List["ExplainabilityInsight"]

class MedicalDamageAssessment(BaseModel):
    emergencyCareCosts: float
    ongoingTreatmentCosts: float
    diagnosticCosts: float
    medicationCosts: float
    medicalEquipmentCosts: float
    treatmentSessions: int
    treatmentDuration: str
    medicalProviders: List[str]
    medicalComplexity: Union[Literal["simple"], Literal["moderate"], Literal["complex"], Literal["severe"], Literal["unknown"]]
    documentedMedicalCosts: float
    estimatedFutureMedical: float
    medicalCostCertainty: float
    currentTreatmentStatus: Union[Literal["ongoing"], Literal["completed"], Literal["planned"], Literal["unknown"]]
    recoveryPrognosis: Union[Literal["excellent"], Literal["good"], Literal["fair"], Literal["poor"], Literal["unknown"]]

class MedicalImpactAssessment(BaseModel):
    injurySeverityLevel: "MedicalComplexity"
    treatmentComplexity: "MedicalComplexity"
    functionalImpairment: str
    returnToWorkLikelihood: Union[Literal["full_return"], Literal["modified_duties"], Literal["partial_return"], Literal["unlikely"]]
    futureCareCosts: str
    permanentDisability: bool
    medicalCausationClear: bool
    preExistingConditions: List[str]
    medicalDocumentationQuality: Union[Literal["excellent"], Literal["good"], Literal["adequate"], Literal["poor"]]
    expertMedicalOpinionNeeded: bool

class MedicalSummary(BaseModel):
    injuriesReported: bool
    injuryDetails: List["InjuryDetail"]
    treatmentRecords: List["TreatmentRecord"]
    diagnosticResults: List["DiagnosticResult"]
    medicalTimeline: "MedicalTimeline"
    medicalProfessionals: List["ContactInfo"]
    medicalCosts: str
    futureCareneeds: str
    impactOnDailyLife: str
    returnToWorkPrognosis: str
    medicalDocumentsAvailable: List[str]
    medicalDocumentsNeeded: List[str]

class MedicalTimeline(BaseModel):
    incidentDate: str
    firstTreatmentDate: str
    emergencyTreatment: bool
    timeOffWork: str
    returnToWorkDate: str
    treatmentEndDate: str
    functionalLimitations: List[str]

class NegligenceAnalysis(BaseModel):
    propertyOwnerFactors: List[str]
    vehicleOperatorFactors: List[str]
    thirdPartyFactors: List[str]
    institutionalFactors: List[str]
    maintenanceFailures: List[str]
    warningDeficiencies: List[str]
    dutyOfCareBreaches: List[str]
    statutoryViolations: List[str]

class NextSteps(BaseModel):
    documentsNeeded: List[str]
    informationNeeded: List[str]
    contactsToReach: List[str]
    timelineForResponse: str
    escalationRequired: bool
    automatedActions: List[str]
    manualActions: List[str]

class OntarioIncomeLossStandards(BaseModel):
    averageWeeklyEarnings: str
    maximumBenefitPeriod: str
    returnToWorkAssumption: str
    ageFactors: str
    documentationRequired: str
    ageSpecificGuidelines: str
    evidenceBasedCalculations: str
    conservativeApproach: str

class OntarioMedicalGuidelines(BaseModel):
    emergencyCareTypical: str
    physiotherapySessionCost: float
    chiropracticSessionCost: float
    diagnosticImagingCosts: str
    maximumTreatmentDuration: str
    documentationRequirement: str
    evidenceBasedLimits: str
    moderateInjuryMedicalCap: float
    ageAdjustedRecovery: str

class OntarioPainSufferingGuide(BaseModel):
    minorInjuryRange: str
    moderateInjuryRange: str
    severeInjuryRange: str
    catastrophicInjuryRange: str
    slipFallBenchmarks: str
    andrewsCap2025: float
    deductible2025: float
    conservativeApproach: str

class OntarioQuantumBenchmarks(BaseModel):
    painAndSufferingBenchmarks: "OntarioPainSufferingGuide"
    medicalCostGuidelines: "OntarioMedicalGuidelines"
    incomeLossStandards: "OntarioIncomeLossStandards"
    statutoryThresholds: "OntarioStatutoryThresholds"

class OntarioStatutoryThresholds(BaseModel):
    motorVehicleThreshold: str
    nonEconomicLossDeductible: float
    andrewsCapAmount: float
    catastrophicThreshold: str
    faultReductionRules: str

class PolicyCoverageAnalysis(BaseModel):
    applicableCoverageTypes: List[str]
    coverageLimits: str
    deductibleAmount: str
    policyConditions: List[str]
    conditionsMet: bool
    coverageInterpretation: str
    ambiguousTerms: List[str]
    industryStandards: str

class PolicyDetails(BaseModel):
    policyNumber: Optional[str] = None
    policyHolder: Optional[str] = None
    insuredVehicle: Optional[str] = None
    effectiveDate: Optional[str] = None
    expiryDate: Optional[str] = None
    coverageTypes: List[str]
    deductibleAmount: Optional[str] = None
    policyLimits: Optional[str] = None
    insuranceCompany: Optional[str] = None
    agentDetails: Optional[str] = None
    hasCompleteInformation: bool

class PolicyExclusion(BaseModel):
    exclusionType: str
    exclusionText: str
    applicabilityReason: str
    legalBasis: str
    precedentSupport: List[str]
    interpretationChallenges: List[str]

class PriorityRiskAssessment(BaseModel):
    overallPriorityLevel: "PriorityLevel"
    overallRiskScore: float
    priorityDrivers: List[str]
    timelineSensitivity: str
    stakeholderImpact: str
    identifiedRisks: List["RiskFactor"]
    highestRiskCategory: "RiskCategory"
    riskMitigationPriority: List[str]
    requiresHumanReview: bool
    requiresLegalCounsel: bool
    requiresSpecialistReview: bool
    reviewReason: str
    recommendedProcessingTimeline: str
    escalationTriggers: List[str]
    urgencyJustification: str
    estimatedFinancialExposure: str
    costOfDelay: str
    assessmentConfidence: float
    uncertaintyFactors: List[str]
    immediateActions: List[str]
    processOptimizations: List[str]
    preventiveRecommendations: List[str]

class QuantumCalculationGuidance(BaseModel):
    totalSpecialDamages: float
    totalGeneralDamages: float
    totalFutureCareDamages: float
    grossDamagesTotal: float
    netDamagesTotal: float
    quantumConfidence: float
    quantumRationale: str
    provincialFactors: List[str]
    uncertaintyAreas: List[str]
    recommendedExpertReports: List[str]
    settlementRange: str
    litigationRisk: Union[Literal["low"], Literal["moderate"], Literal["high"], Literal["very_high"], Literal["unknown"]]
    settlementStrategy: Union[Literal["negotiate"], Literal["mediate"], Literal["litigate"], Literal["settle_fast"], Literal["unknown"]]

class QuantumDamageExtraction(BaseModel):
    medicalDamages: "MedicalDamageAssessment"
    incomeLossDamages: "IncomeLossAssessment"
    careAndAssistanceCosts: "CareAssistanceAssessment"
    specialDamages: "SpecialDamagesBreakdown"
    generalDamages: "GeneralDamagesAssessment"
    futureCareCosts: "FutureCareAssessment"
    faultImpactAnalysis: "FaultImpactOnQuantum"
    quantumGuidance: "QuantumCalculationGuidance"
    ontarioBenchmarks: "OntarioQuantumBenchmarks"
    validationChecks: "QuantumValidationChecks"
    uiMetadata: "UIMetadata"
    explainabilityInsights: List["ExplainabilityInsight"]

class QuantumValidationChecks(BaseModel):
    painSufferingValidation: str
    totalDamagesValidation: str
    medicalCostValidation: str
    settlementRangeValidation: str
    overvaluationRisk: Union[Literal["low"], Literal["moderate"], Literal["high"], Literal["critical"]]
    recommendedAdjustments: List[str]
    comparisonToActualClaims: str

class Resume(BaseModel):
    name: str
    email: str
    experience: List[str]
    skills: List[str]

class RiskAssessment(BaseModel):
    liabilityRisk: Union[Literal["low"], Literal["medium"], Literal["high"], Literal["critical"]]
    financialRisk: Union[Literal["low"], Literal["medium"], Literal["high"], Literal["critical"]]
    reputationalRisk: Union[Literal["low"], Literal["medium"], Literal["high"], Literal["critical"]]
    regulatoryRisk: Union[Literal["low"], Literal["medium"], Literal["high"], Literal["critical"]]
    overallRiskScore: float

class RiskFactor(BaseModel):
    category: "RiskCategory"
    severity: Union[Literal["LOW"], Literal["MEDIUM"], Literal["HIGH"], Literal["CRITICAL"]]
    description: str
    likelihood: float
    potentialImpact: str
    mitigationStrategy: str
    timeframeToResolve: str
    requiresSpecialistReview: bool

class SparkNlpDateEntity(BaseModel):
    text: str
    normalizedDate: str
    dateType: Union[Literal["INCIDENT_DATE"], Literal["POLICY_DATE"], Literal["REPORT_DATE"], Literal["EFFECTIVE_DATE"], Literal["EXPIRY_DATE"], Literal["OTHER"]]
    confidence: float
    context: str

class SparkNlpEnhancedData(BaseModel):
    enhancedEntities: List["SparkNlpEntity"]
    financialEntities: List["SparkNlpFinancialEntity"]
    extractedDates: List["SparkNlpDateEntity"]
    locationEntities: List["SparkNlpLocationEntity"]
    confidenceBoost: float
    processingNotes: List[str]

class SparkNlpEntity(BaseModel):
    text: str
    entityType: str
    confidence: float
    context: str
    startPosition: Optional[int] = None
    endPosition: Optional[int] = None

class SparkNlpFinancialEntity(BaseModel):
    text: str
    entityType: Union[Literal["MONETARY_AMOUNT"], Literal["POLICY_NUMBER"], Literal["CLAIM_NUMBER"], Literal["PERCENTAGE"], Literal["DEDUCTIBLE"], Literal["COVERAGE_LIMIT"]]
    normalizedValue: Optional[str] = None
    confidence: float
    context: str

class SparkNlpLocationEntity(BaseModel):
    text: str
    addressType: Union[Literal["INCIDENT_LOCATION"], Literal["MAILING_ADDRESS"], Literal["BUSINESS_ADDRESS"], Literal["PROPERTY_ADDRESS"], Literal["OTHER"]]
    normalizedAddress: Optional[str] = None
    confidence: float
    context: str

class SpecialDamagesBreakdown(BaseModel):
    transportationCosts: float
    accommodationCosts: float
    prescriptionCosts: float
    medicalSuppliesCosts: float
    parkingFees: float
    lostBenefits: float
    otherOutOfPocketCosts: float
    outOfPocketReceipts: List[str]
    specialDamagesTotal: float
    specialDamagesCertainty: float

class StructuredCircumstances(BaseModel):
    trafficViolations: List[str]
    rightOfWayFactors: List[str]
    vehicleConditions: List[str]
    roadConditions: List[str]
    visitorStatus: Union[Literal["invitee"], Literal["licensee"], Literal["trespasser"], Literal["employee"]]
    locationType: Union[Literal["commercial"], Literal["residential"], Literal["industrial"], Literal["public"], Literal["recreational"]]
    hazardType: str
    warningsPosted: bool
    mitigationEfforts: List[str]
    dutyOfCareLevel: Union[Literal["low"], Literal["standard"], Literal["high"], Literal["statutory"]]
    breachFactors: List[str]
    causationChain: List[str]
    contributingFactors: List[str]
    mitigatingFactors: List[str]
    atFaultParties: List[str]

class SupportingEvidence(BaseModel):
    evidenceType: str
    evidenceDescription: str
    evidenceSource: str
    evidenceStrength: float
    evidenceReliability: float
    evidenceImpact: str

class ThirdPartyInformationNeeded(BaseModel):
    thirdPartyType: str
    thirdPartyContact: str
    informationNeeded: str
    legalBasisForRequest: str
    voluntaryVsMandatory: Union[Literal["VOLUNTARY"], Literal["SUBPOENA_REQUIRED"], Literal["COURT_ORDER_NEEDED"]]
    costImplications: str
    alternativeApproaches: List[str]

class TreatmentRecord(BaseModel):
    treatmentType: "TreatmentType"
    provider: str
    treatmentDate: str
    treatmentDetails: str
    treatmentDuration: str
    treatmentCost: str
    ongoing: bool

class UIMetadata(BaseModel):
    entityMappings: List["EntityMapping"]
    explainabilityData: List["ExplainabilityInsight"]
    documentHighlights: List["DocumentHighlight"]
    confidenceBreakdown: "FieldConfidenceBreakdown"
    colorScheme: "ColorSchemeMapping"
    analysisFlow: "AnalysisFlow"

class UncertaintyArea(BaseModel):
    uncertaintyType: str
    uncertaintyDescription: str
    uncertaintyImpact: str
    resolutionApproach: str
    resolutionTimeline: str
    uncertaintyRisk: "RiskLevel"

class VisualizationData(BaseModel):
    chartType: Union[Literal["BAR"], Literal["WATERFALL"], Literal["HEATMAP"], Literal["SANKEY"], Literal["FEATURE_IMPORTANCE"], Literal["FLOW_DIAGRAM"]]
    chartData: List["ChartDataPoint"]
    colorScheme: List[str]
    annotations: List["ChartAnnotation"]
    interactiveElements: List["InteractiveElement"]

class ZurichEmailClassificationResult(BaseModel):
    isClaimRelated: bool
    claimType: "ClaimType"
    workflowAction: Union[Literal["PROCEED_TO_ZENDESK"], Literal["IGNORE_EMAIL"], Literal["REQUEST_ATTACHMENTS"], Literal["HUMAN_REVIEW_REQUIRED"]]
    confidenceScore: float
    urgencyLevel: "UrgencyLevel"
    canadianJurisdiction: "CanadianProvince"
    attachmentAnalysis: "AttachmentAnalysis"
    claimIndicators: Optional["ClaimIndicators"] = None
    riskAssessment: Optional["RiskAssessment"] = None
    classificationReasoning: str
    recommendedNextSteps: List[str]
    flagsForHumanReview: List[str]
    processingNotes: List[str]
    canadianLegalConsiderations: List[str]
