---
default_language_version:
  python: python3

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: fix-byte-order-marker
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-toml
      - id: check-yaml
        args: ["--unsafe"]
      - id: detect-private-key
      - id: end-of-file-fixer
      - id: mixed-line-ending
      - id: trailing-whitespace
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.1.5
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
  - repo: https://github.com/psf/black-pre-commit-mirror
    rev: 23.10.1
    hooks:
      - id: black-jupyter
        name: black-src
        alias: black
  - repo: https://github.com/adamchainz/blacken-docs
    rev: 1.16.0
    hooks:
      - id: blacken-docs
        name: black-docs-text
        alias: black
        types_or: [rst, markdown, tex]
        additional_dependencies: [black==23.10.1]
        # Using PEP 8's line length in docs prevents excess left/right scrolling
        args: [--line-length=79]
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.3
    hooks:
      - id: prettier
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.6
    hooks:
      - id: codespell
        additional_dependencies: [tomli]
        args: ["--ignore-words-list", "nin"]
