service: rozie-mock-airline-flight-util


provider:
  name: aws
  runtime: nodejs18.x
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:Scan
        - dynamodb:DeleteItem
        - dynamodb:UpdateItem
        - dynamodb:Query
      Resource: "*"
  apiGateway:
    apiKeys:
      - ${self:service}-${self:provider.stage}-Api-Key

functions:
  flight-util:
    name: ${self:provider.stage}-${self:service}-lambda
    description: "Function to handle flight util operations"
    handler: index.lambda_handler
    environment:
      AIRPORT_CODE_TABLE: rozie-mock-airline-airport-table-${self:provider.stage}
        
    events:
      - http:
          path: /convertAirlineCode
          method: post
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - sessionid
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
resources:
  extensions:
    ServerlessDeploymentBucket:
      Properties:
        PublicAccessBlockConfiguration:
          BlockPublicAcls: True
          BlockPublicPolicy: True
          IgnorePublicAcls: True
          RestrictPublicBuckets: True
  Resources:
    AirportTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: airportCode
            AttributeType: S
          - AttributeName: airportName
            AttributeType: S
        KeySchema:
          - AttributeName: airportCode
            KeyType: HASH  
        GlobalSecondaryIndexes:
          - IndexName: AirportNameIndex
            KeySchema:
              - AttributeName: airportName
                KeyType: HASH  
            Projection:
              ProjectionType: ALL
        BillingMode: PAY_PER_REQUEST
        TableName: rozie-mock-airline-airport-table-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
