import os
from twilio.rest import Client

def send_sms_twilio(recipient_phone, message):
    """This function used for sending sms"""
    account_sid = os.environ["TWILIO_ACC_SID"]
    auth_token = os.environ["TWILIO_AUTH_TOKEN"]
    client = Client(account_sid, auth_token)
    sms = client.messages.create(
        from_= os.environ["TWILIO_PHONE_NUMBER"],
        body=message,
        to=recipient_phone
        )
    
    print(sms.sid)