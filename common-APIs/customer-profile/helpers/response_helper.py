"""
Helper module for formatting Lambda responses.
"""

import json


def format_response(status_code, body, message=None):
    """
    Helper function to format the Lambda response.

    Args:
        status_code (int): HTTP status code of the response.
        body (dict or list or str): The body of the response, typically in JSON format.
        message (str, optional): An optional message to include in the response. Defaults to None.

    Returns:
        dict: The formatted response ready to be returned from the Lambda function.
    """
    response = {
        "statusCode": status_code,
        "body": json.dumps({
            "message": message,
            "data": body
        }),
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*", 
            "Access-Control-Allow-Methods": "OPTIONS, GET, POST, PUT, DELETE",  
            "Access-Control-Allow-Headers": "*"  
        }
    }
    return response
