import traceback
from typing import Dict, Optional
from fastapi import APIRouter, HTTPException, Depends
from airline_app.adapters.auth_adapter import get_api_key

from airline_app.model.models import CaseCreation

from airline_app.helpers.logging_helper import print_info_log


from airline_app.services.baggage import get_baggage_details_by_pnr, get_baggage_details_by_baggage_id
from airline_app.services.pnr import get_pnr_details

baggage_router = APIRouter(prefix="/baggage")

@baggage_router.post("/get_baggage_details", dependencies=[Depends(get_api_key)])
async def get_baggage_details(data : Dict):
    try:
        pnr_id = data.get("pnr_id", None)
        baggage_id = data.get("baggage_id", "")
        last_name = data.get("last_name")
        baggage_details = None
        if baggage_id:
            baggage_details = get_baggage_details_by_baggage_id(baggage_id)
            if baggage_details.get("status") == "Error":
                return baggage_details
            pnr_id = baggage_details.get("pnr_id")
            if pnr_id:
                details = get_pnr_details(pnr_id, last_name)
                if not isinstance(details, list):
                    return {
                        "status": "Error",
                        "message": "Last name and Baggage ID do not match",
                        "suggested_action": "Asks user to verify the captured details (Baggage id and Customer Last name) and correct if something wrongly captured."
                    }
        if pnr_id:
            details = get_pnr_details(pnr_id, last_name)
            if not isinstance(details, list):
                return {
                    "status": "Error",
                    "message": "Last name and PNR ID do not match",
                    "suggested_action": "Asks user to verify the captured details (PNR id and Customer Last name) and correct if something wrongly captured."
                }
            baggage_details = get_baggage_details_by_pnr(pnr_id)

        if not baggage_details:
            raise HTTPException(status_code=404, detail="baggage details not found")
        return baggage_details
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"status": "Error occurred", "error": str(e)})