from datetime import datetime, <PERSON><PERSON><PERSON>
import json
import os


# Get the directory of the current file (utils.py)
dir_path = os.path.dirname(os.path.realpath(__file__))
branch_data = {}
service_map = {}
# Since `data` is at the same level as `helpers`, adjust the path accordingly
json_file_path = os.path.join(os.path.abspath(os.path.join(dir_path, '..')), 'data', 'possible_values_map.json')
json_file_path_service_map = os.path.join(os.path.abspath(os.path.join(dir_path, '..')), 'data', 'service_map.json')
# Error handling for reading the JSON file
try:
    with open(json_file_path, 'r') as f:
        branch_data = json.load(f)
        print("Successfully loaded JSON data from", json_file_path)
    with open(json_file_path_service_map, 'r') as f:
        service_map = json.load(f)
        print("Successfully loaded service_map data from", service_map)
except FileNotFoundError:
    print("Error: JSON file not found at path:", json_file_path)
    data = {}
    service_map = {}

def generate_available_slots(branch_name, service_names, booked_slots, start_time_str=None, end_time_str=None, stylist_id=None):
    """
    Generates available time slots for given services and stylist within the specified time range.

    Parameters:
        branch_name (str): The name of the branch where the service is offered.
        service_names (list): The names of the services for which slots are to be generated.
        booked_slots (list): A list of already booked slots to avoid overlap.
        start_time_str (str): The start time in ISO 8601 format (YYYY-MM-DDTHH:MM:SS.000Z).
        end_time_str (str): The end time in ISO 8601 format (YYYY-MM-DDTHH:MM:SS.000Z).
        stylist_id (str): The ID of the stylist to check for availability (optional).
        branch_data (dict): The branch-specific data, including service and stylist information.

    Returns:
        dict: A dictionary containing the available slots or an error message.
    """
    service_names = json.loads(service_names)
    try:
        # Parse start and end times from the provided strings
        start_time = datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M:%S.000Z")

        # If end_time_str is not provided, assign it to today's date at 17:00:00Z
        if not end_time_str:
            today = datetime.utcnow().replace(hour=17, minute=0, second=0, microsecond=0)
            end_time = today
        else:
            end_time = datetime.strptime(end_time_str, "%Y-%m-%dT%H:%M:%S.000Z")

        # Define the maximum allowed gap of 4 weeks
        max_gap = timedelta(weeks=4)

        # Check if the gap between start_time and end_time exceeds 4 weeks
        if end_time - start_time > max_gap:
            # If the gap is more than 4 weeks, adjust the end_time to maintain the gap of 4 weeks
            end_time = start_time + max_gap

        # Step 1: Calculate total duration for all requested services
        total_duration_minutes = 0

        for service_name in service_names:
            service_info = next((s for s in service_map[branch_name] if s['serviceId'] == service_name), None)
            if service_info:
                total_duration_minutes += service_info['duration']
            else:
                return {"error": f"Service '{service_name}' not found."}

        # Convert total duration to timedelta
        slot_duration = timedelta(minutes=total_duration_minutes)

        available_slots = []

        # Step 2: Check if the stylist (if provided) or any stylists provide all services
        def stylist_provides_all_services(stylist, branch_services):
            """Helper function to check if a stylist provides all requested services."""
            return all(stylist in branch_services[service]['stylists'] for service in service_names)

        # Filter the stylists who provide all services
        eligible_stylists = {}

        # If a specific stylist is provided, check if they provide all services
        if stylist_id:
            if stylist_provides_all_services(stylist_id, branch_data['branches'][branch_name]['services']):
                eligible_stylists[stylist_id] = True
            else:
                return {"warning": "The selected stylist does not provide all requested services. Please book separately."}
        else:
            # No stylist provided, check all available stylists
            for service_name in service_names:
                for stylist in branch_data['branches'][branch_name]['services'][service_name]['stylists']:
                    if stylist_provides_all_services(stylist, branch_data['branches'][branch_name]['services']):
                        eligible_stylists[stylist] = True

            # If no stylist provides all requested services, return early
            if not eligible_stylists:
                return {"error": "No single stylist provides all requested services. Please book separately."}

        # Step 3: Generate slots only for stylists who provide all requested services
        current_day = start_time.date()
        while current_day <= end_time.date():
            # Skip Sundays (weekday() == 6)
            if current_day.weekday() == 6:
                current_day += timedelta(days=1)
                continue

            # Define the working hours for the current day
            working_start_time = datetime.combine(current_day, datetime.strptime("09:00", "%H:%M").time())
            working_end_time = datetime.combine(current_day, datetime.strptime("17:00", "%H:%M").time())

            # Start generating slots from the maximum of working_start_time or input start_time
            current_slot_start = max(working_start_time, start_time)

            while current_slot_start + slot_duration <= working_end_time and current_slot_start < end_time:
                next_slot_end = current_slot_start + slot_duration

                # Check availability for the provided stylist (if any)
                if stylist_id:
                    if not is_time_in_booked_range(stylist_id, current_slot_start, next_slot_end, booked_slots):
                        available_slots.append({
                            "serviceId": service_names,
                            "startTime": current_slot_start.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
                            "endTime": next_slot_end.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
                            "staffId": stylist_id,
                        })
                else:
                    # Check availability for all eligible stylists
                    for stylist in eligible_stylists:
                        if not is_time_in_booked_range(stylist, current_slot_start, next_slot_end, booked_slots):
                            available_slots.append({
                                "serviceId": service_names,
                                "startTime": current_slot_start.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
                                "endTime": next_slot_end.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
                                "staffId": stylist,
                            })
                current_slot_start += slot_duration

            current_day += timedelta(days=1)

        if not available_slots:
            return {"error": "No available slots found."}

        return {"data": available_slots}

    except ValueError as ve:
        return {"error": f"Invalid date format: {str(ve)}"}
    except KeyError as e:
        return {"error": f"Missing key in data: {str(e)}"}
    except Exception as e:
        return {"error": "An unexpected error occurred."}

def is_time_in_booked_range(stylist_id, start, end, booked_slots):
    """
    Checks if a given time range overlaps with any booked slots for the specified stylist.
    
    Parameters:
        stylist_id (str): The ID of the stylist whose schedule is being checked.
        start (datetime): The start time of the slot.
        end (datetime): The end time of the slot.
        booked_slots (list): A list of booked time slots.

    Returns:
        bool: True if the time range overlaps with a booked slot, otherwise False.
    """
    if booked_slots is None:
        return False
    for booked in booked_slots:
        if booked['staffId'] == stylist_id:
            booked_start = datetime.strptime(booked['startTime'], "%Y-%m-%dT%H:%M:%S.000Z")
            booked_end = datetime.strptime(booked['endTime'], "%Y-%m-%dT%H:%M:%S.000Z")
            if (start < booked_end and end > booked_start):
                return True
    return False
