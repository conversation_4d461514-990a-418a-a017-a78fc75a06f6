"""
Module for handling Salon Appointment CRUD operations
"""

import json
import os
from helpers.logging_helper import get_log_level, set_logging_level, print_info_log
from helpers.decorator_helper import exception_handler
from helpers.dynamo_helper import (
    get_item_by_keys,
    put_table_item,
    query_table_with_filter,
    upsert_item,
    delete_item,
)
from helpers.response_helper import format_response
from helpers.utils import generate_available_slots
from datetime import datetime

@exception_handler
def lambda_handler(event, context):
    """
    Lambda handler for Salon Appointment CRUD operation APIs
    """
    set_logging_level(get_log_level())
    print_info_log("", "Salon Appointment api invoked", event)
    http_method = event.get("httpMethod", "")
    path = event.get("path", "")

    if http_method == "GET" and path == "/appointment":
        return get_available_appointment_slots(event)
    if http_method == "POST" and path == "/appointment":
        return create_appointment(event)
    if http_method == "PUT" and path == "/appointment": 
        return update_appointment(event)
    if http_method == "DELETE" and path == "/appointment":
        return delete_appointment(event)
    return format_response(400, None, "Invalid request")


def create_appointment(event):
    """
    Create new appointment record in the dynamodb table
    """
    appointment_data = json.loads(event.get("body", "{}"))
    client_id = appointment_data.get("client_id")
    startTime = appointment_data.get("startTime")
    appointment_data["start_time_client_id"] = f'{startTime}_{client_id}'
    status, _ = put_table_item(
        os.environ.get("TABLE_NAME"), appointment_data, "branch_id","start_time_client_id"
    )
    if status == "Update":
        return format_response(200, None, "Requested slot is already booked")
    elif status == "Error":
        return format_response(500, None, "Encountered Technical Issue")
    else:
        return format_response(
            200, None, f"Appointment booked successfully, client_id: {client_id}"
        )


def get_available_appointment_slots(event):
    """
    Get available slots for booking appointment for provided service id on provided date and time(if provided)
    """
    query_params = event.get("queryStringParameters") or {}
    appointment_data = []
    if query_params.get("staffId"):
        appointment_data = query_table_with_filter(
                os.environ.get("TABLE_NAME"), query_params.get("branch_id"), query_params.get("start_time"), query_params.get("end_time"), query_params.get("staffId")
            )
    else:
        appointment_data = query_table_with_filter(
                os.environ.get("TABLE_NAME"), query_params.get("branch_id"), query_params.get("start_time"), query_params.get("end_time")
            )
    appointment_data = generate_available_slots(query_params.get("branch_id"), query_params.get("serviceId"), appointment_data["data"], query_params.get("start_time"), query_params.get("end_time"),query_params.get("staffId"))
    if appointment_data:
        return format_response(200, appointment_data)
    return format_response(404, None, "appointment not found")


def update_appointment(event):
    """
    Update the appointment with provided clientId
    """
    appointment_data = json.loads(event.get("body", "{}"))
    status = upsert_item(
        os.environ.get("TABLE_NAME"), appointment_data, "phone_number", "appointment_type"
    )
    if status == "Error":
        return format_response(500, None, "Encountered Technical Issue")
    else:
        return format_response(200, None, "Salon Appointment Updated Successfully")


def delete_appointment(event):
    """
    Update the appointment with provided clientId
    """
    query_params = event.get("queryStringParameters", {})
    status = delete_item(
        os.environ.get("TABLE_NAME"),
        {"phone_number": query_params.get("phone_number"), "appointment_type":query_params.get("appointment_type")}
    )
    if status == "Error":
        return format_response(500, None, "Encountered Technical Issue")
    else:
        return format_response(200, None, "Salon Appointment Deleted Successfully")
