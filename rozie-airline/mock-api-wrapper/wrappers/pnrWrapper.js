const { ApiResp } = require("../helpers/api-helper.js")
const { axiosPostRequest } = require("../helpers/axios-helper.js")

const getCustomerPNREndPoint = process.env.GET_CUSTOMER_PNR_ENDPOINT
const getCustomerPNRApikey = process.env.GET_CUSTOMER_PNR_APIKEY

/**
 * @function pnrWrapper
 * @description  This function retrieves the most recent Passenger Name Record (PNR) based on the arrivalTimestamp 
 * for a given customer by sending a POST request to an external API. It returns the PNR with the closest 
 * future arrival timestamp if multiple PNRs exist.
 * 
 * @param {string} customer_id - The ID of the customer for whom to retrieve PNR and baggage details.
 * 
 * @returns {Promise<Object>} - A response object containing the most recent PNR details or an error message.
 * 
 * @throws {Error} - Throws an error if there is an issue with retrieving the PNR details.
 */
const pnrWrapper = async (customer_id) => {
	const headersPNR = {
		"Content-Type": "application/json",
		"x-api-key": getCustomerPNRApikey
	}

	const requestBodyPNR = {
		"customer_id": customer_id
	}

	try {
		let pnrDetails = []
		const pnrResponse = await axiosPostRequest(getCustomerPNREndPoint, requestBodyPNR, headersPNR)
		console.log("All pnrResponse:",pnrResponse)
		if (pnrResponse.status === 404) {
			console.log("No PNR found for customer id")
			return ApiResp({
				status: "Error",
				message: "No PNR found for customer_id",
				response: pnrDetails
			}, 200)
		}

		if (pnrResponse && pnrResponse.response && pnrResponse.response.length > 0) {
			// Get today's date
			const today = new Date()

			// Sort PNRs based on arrivalTimestamp
			const sortedPNRs = pnrResponse.response
				.filter(pnr => new Date(pnr.flight_schedule_details.arrivalTimestamp) > today) // Only future PNRs
				.sort((a, b) => new Date(a.flight_schedule_details.arrivalTimestamp) - new Date(b.flight_schedule_details.arrivalTimestamp))
            
			console.log("sortedPNRs:",sortedPNRs)
			if (sortedPNRs.length > 0) {
				// Return the most recent PNR
				const mostRecentPNR = sortedPNRs[0]
				console.log("mostRecentPNR",mostRecentPNR)
				return ApiResp({
					status: "Success",
					message: "Most recent active PNR retrieved successfully",
					response: mostRecentPNR
				})
			} else {
				// No future PNRs
				return ApiResp({
					status: "Error",
					message: "No upcoming PNR records found for the customer",
					response: pnrDetails
				}, 200)
			}

		} else {
			return ApiResp({
				status: "Error",
				message: "No PNR records found for the customer",
				response: pnrDetails
			}, 200)
		}

	} catch (error) {
		console.error("Error:", error)
		return ApiResp({
			status: "Error",
			message: "Internal Server Error"
		}, 500)
	}
}

module.exports = {
	pnrWrapper
}
