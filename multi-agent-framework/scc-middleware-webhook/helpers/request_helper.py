"""
api call helper module
"""

import asyncio
import json
import os
import requests
from requests.auth import HTTPBasicAuth

from helpers.logging_helper import print_info_log, print_error_log


def call_twilio_assist_api(params, initial_contact_id=""):
    """
    call llm request
    """
    data = json.dumps(params)
    options = {
        "url": f"{os.getenv('TWILIO_ASSIST_URL')}",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "x-api-key": os.getenv("TWILIO_ASSIST_URL_API_KEY"),
        },
        "data": data,
        "timeout": 20,
    }
    try:
        response = requests.request(**options)
        print_info_log(initial_contact_id, "response status_code", response.status_code)
        return response.json() if response.status_code == 200 else {}
    except requests.RequestException as e:
        print_error_log(initial_contact_id, e)
        raise e


def sync_get_request(url, query_params=None, username=None, password=None, headers=None):
    """
    This function used for invoking get method and wait for result.
    """
    param = {"url": url, "timeout": 30}
    if headers:
        param["headers"] = headers
    if query_params:
        param["params"] = query_params
    if username and password:
        param["auth"] = (username, password)
    try:
        response = requests.get(**param)

        if response.status_code != 200:
            print(response.json())
            return "Failed", {}

        response = response.json()
        return "Success", response

    except Exception as e:
        print_error_log("", e)
        return "Failed", str(e)


def async_post_request(url, body=None, headers=None):
    param = {
        "url": url,
        "timeout": 1,
    }
    if headers:
        param["headers"] = headers
    else:
        param["headers"] = {"Content-Type": "application/json"}
    if body:
        param["data"] = json.dumps(body)
    try:
        requests.post(**param)

        return "Success"

    except Exception as e:
        print_error_log("", e)
        return "Failed"


def sync_post_request(url, data=None, headers=None):
    """
    This function is used for invoking the POST method and waits for the result.
    """
    param = {"url": url, "timeout": 30}

    if data:
        param["data"] = json.dumps(data)

    if headers:
        param["headers"] = headers
    else:
        param["headers"] = {}

    try:
        response = requests.post(**param)

        if response.status_code != 200:
            print(response.json())
            return "Failed", {}

        response = response.json()
        return "Success", response

    except Exception as e:
        print_error_log("", e)
        return "Failed", str(e)


async def delayed_post_request(url, data=None, headers=None, account_sid=None, auth_token=None):
    """
    This function is used for invoking the POST method and waits for the result.
    """
    param = {"url": url, "timeout": 10}

    if data:
        param["data"] = json.dumps(data)

    if headers:
        param["headers"] = headers
    else:
        param["headers"] = {}

    if auth_token:
        param["auth"] = HTTPBasicAuth(account_sid, auth_token)

    try:
        await asyncio.sleep(4)
        print(param)
        response = requests.post(**param)

        if response.status_code != 200:
            print(response.json())
            return "Failed", {}

        response = response.json()
        return "Success", response

    except Exception as e:
        print_error_log("", e)
        return "Failed", str(e)
