const { ApiResp } = require("../helpers/api-helper.js")
const { queryDynamodbTableItemGSI, retrieveDynamoEntry } = require("../helpers/dynamo-helper.js")

/**
 * @function getCustomerPNR
 * @description This function retrieves all PNR entries from a DynamoDB table based on the customer ID,
 * and filters the PNRs whose flights have not yet arrived. It defaults to retrieving 'confirmed' PNRs
 * but can also retrieve 'completed' PNRs if specified via the flag.
 * 
 * @param {Object} event - The event object containing the customer_id and pnr_status_flag.
 * @param {string} event.customer_id - The customer ID to fetch PNRs for.
 * @param {string} [event.pnr_status_flag] - Optional flag to fetch 'completed' PNRs instead of 'confirmed'.
 * 
 * @returns {Promise<Object>} - The response object containing the list of PNRs based on status or an error message.
 * 
 * @throws {Error} - Throws an error if there is an issue with fetching the PNR entries.
 */
const getCustomerPNR = async (customer_id, pnr_status_flag) => {
	try {
		console.log(`Function getCustomerPNR initiated for customer ID: ${customer_id}`)
		console.log("pnr_status_flag", pnr_status_flag)

		const params = {
			TableName: process.env.PNR_TABLE_NAME,
			IndexName: "CustomerIndex",
			KeyConditionExpression: "customer_id = :customer_id",
			ExpressionAttributeValues: {
				":customer_id": customer_id
			},
			ScanIndexForward: false
		}

		const customerPNRs = await queryDynamodbTableItemGSI(params)

		if (!customerPNRs || customerPNRs.length === 0) {
			return ApiResp({
				status: "Error",
				message: "No PNRs found for this customer",
				response: []
			}, 404)
		}

		const currentDateTime = new Date()
		const targetStatus = pnr_status_flag === "completed" ? "completed" : "confirmed"
		console.log("targetStatus", targetStatus)
		const filteredPNRs = []

		for (const pnr of customerPNRs) {
			const { flight_schedule_id, pnr_status } = pnr

			if (pnr_status === targetStatus && flight_schedule_id) {
				const flightScheduleDetails = await retrieveDynamoEntry(process.env.FLIGHT_SCHEDULE_TABLE, "scheduleId", flight_schedule_id)

				if (flightScheduleDetails) {
					delete flightScheduleDetails.seating

					pnr.flight_schedule_details = flightScheduleDetails

					// If the PNR is 'confirmed', filter by future flight arrival
					if (targetStatus === "confirmed") {
						const arrivalTimestamp = new Date(flightScheduleDetails.arrivalTimestamp)
						if (arrivalTimestamp > currentDateTime) {
							filteredPNRs.push(pnr)  // Add 'confirmed' PNRs with future arrival time
						}
					} else {
						// For 'completed' PNRs, no filtering by arrival timestamp
						filteredPNRs.push(pnr)
					}
				} else {
					pnr.flight_schedule_details = {}
				}
			}
		}

		console.log("filteredPNRs", filteredPNRs)

		if (filteredPNRs.length > 0) {
			return ApiResp({
				status: "Success",
				message: `${targetStatus.charAt(0).toUpperCase() + targetStatus.slice(1)} PNRs retrieved successfully`,
				response: filteredPNRs
			})
		} else {
			return ApiResp({
				status: "Error",
				message: `No ${targetStatus} PNRs found for customer id`
			}, 404)
		}

	} catch (error) {
		console.log("Error in getCustomerPNR:", error)
		return ApiResp({
			status: "Error",
			message: "Error retrieving customer PNRs"
		}, 500)
	}
}

module.exports = {
	getCustomerPNR
}
