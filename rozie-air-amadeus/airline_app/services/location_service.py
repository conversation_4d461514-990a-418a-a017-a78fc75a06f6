import logging
import aiohttp
from typing import Dict, List, Optional
from airline_app.services.base_service import BaseApiService

logger = logging.getLogger(__name__)

class LocationService(BaseApiService):
    def __init__(self):
        super().__init__()
        self.endpoint = "/v1/reference-data/locations"

    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None) -> Dict:
        """
        Make an HTTP request to the Amadeus API.
        
        Args:
            method (str): HTTP method (GET, POST, etc.)
            endpoint (str): API endpoint
            params (Dict, optional): Query parameters
            
        Returns:
            Dict: API response
        """
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.request(method, url, headers=headers, params=params) as response:
                    if response.status == 401:
                        self._handle_unauthorized()
                        # Retry the request with new token
                        headers = self._get_headers()
                        async with session.request(method, url, headers=headers, params=params) as retry_response:
                            retry_response.raise_for_status()
                            return await retry_response.json()
                    
                    response.raise_for_status()
                    return await response.json()
        except Exception as e:
            logger.error(f"Error making request to {url}: {str(e)}")
            raise

    async def search_locations(self, keyword: str, sub_type: str = "CITY") -> List[Dict]:
        """
        Search for locations using the Amadeus Reference Data API.
        
        Args:
            keyword (str): The keyword to search for (e.g., city name, airport code)
            sub_type (str): The type of location to search for (default: CITY)
            
        Returns:
            List[Dict]: List of locations with city and country names
        """
        try:
            params = {
                "keyword": keyword,
                "subType": sub_type
            }
            
            response = await self._make_request(
                method="GET",
                endpoint=self.endpoint,
                params=params
            )
            
            if response and "data" in response:
                # Transform the response to only include city and country names
                return [
                    {
                        "city": location["address"]["cityName"],
                        "country": location["address"]["countryName"]
                    }
                    for location in response["data"]
                ]
            return []
            
        except Exception as e:
            logger.error(f"Error searching locations: {str(e)}")
            raise 