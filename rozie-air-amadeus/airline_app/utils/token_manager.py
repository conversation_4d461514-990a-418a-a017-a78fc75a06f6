import requests
import logging
from datetime import datetime, timedelta
from airline_app.core import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TokenManager:
    def __init__(self):
        self.token = None
        self.expires_at = None
        self.base_url = "https://test.api.amadeus.com" if config.AMADEUS_HOST == "test" else "https://api.amadeus.com"
        
    def get_valid_token(self):
        """
        Get a valid token, refreshing if necessary.
        Returns the current valid token.
        """
        current_time = datetime.now()
        
        # If token is expired or not set, get a new one
        if not self.token or not self.expires_at or current_time >= self.expires_at:
            logger.info("Token expired or not set. Refreshing token...")
            self._refresh_token()
            
        return self.token
    
    def _refresh_token(self):
        """
        Refresh the access token from Amadeus API.
        Updates the token and expiration time.
        """
        token_endpoint = f"{self.base_url}/v1/security/oauth2/token"
        payload = {
            'client_id': config.AMADEUS_CLIENT_ID,
            'client_secret': config.AMADEUS_CLIENT_SECRET,
            'grant_type': 'client_credentials'
        }
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        try:
            response = requests.post(token_endpoint, data=payload, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            self.token = data.get('access_token')
            # Set expiry time 5 minutes before actual expiry to avoid token expiration during requests
            self.expires_at = datetime.now() + timedelta(seconds=data.get('expires_in', 1800) - 300)
            logger.info(f"Token refreshed successfully. Expires at {self.expires_at}")
        except Exception as e:
            logger.error(f"Failed to refresh token: {str(e)}")
            raise Exception(f"Failed to authenticate with Amadeus API: {str(e)}")
    
    def is_token_valid(self):
        """
        Check if the current token is still valid.
        Returns True if token exists and hasn't expired.
        """
        if not self.token or not self.expires_at:
            return False
        return datetime.now() < self.expires_at

# Create a singleton instance
token_manager = TokenManager()

def get_token_manager():
    """
    Get the singleton token manager instance.
    """
    return token_manager 