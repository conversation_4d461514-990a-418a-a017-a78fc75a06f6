/* eslint-disable no-mixed-spaces-and-tabs */
const { getAllItemsFromDynamoDBTable , updateDynamoTableDataWithParams } = require("../helpers/dynamo-helper.js")
const { baggageStatus} = require("./baggage-status.js")
const {updatePNRStatus} = require("./pnr-status.js")

/**
 * @function createPNR
 * @description This function creates a PNR entry in a DynamoDB table based on the provided data.
 * 
 * @param {Object} body - The request body containing customer and booking details.
 * @param {string} body.customer_id - The customer ID for the PNR.
 * @param {string} body.flight_schedule_id - The flight schedule ID.
 * @param {number} body.payment_amount - The payment amount.
 * @param {Array} body.travelers_details - The list of travelers details, including their names and seat numbers.
 * 
 * @returns {Promise<Object>} - The response object indicating the result of the PNR creation.
 * 
 * @throws {Error} - Throws an error if there is an issue during the PNR creation process.
 */
const FLIGHT_SCHEDULE_TABLE = process.env.FLIGHT_SCHEDULE_TABLE
const scheduleFlightStatus = async () => {
	try {
		const flightScheduleResult = await getAllItemsFromDynamoDBTable(FLIGHT_SCHEDULE_TABLE)
		console.log("flightScheduleResult",flightScheduleResult)
		const currentDate = new Date()
		// Filter the flights for today's date
		console.log("currentDate",currentDate)
		const filteredFlights = flightScheduleResult.filter(flight => {
			console.log("new Date(flight.date).toDateString()", new Date(flight.date).toDateString() , flight.date )
			console.log( "currentDate.toDateString()", currentDate.toDateString())
			return new Date(flight.date).toDateString() === currentDate.toDateString()
		})
		console.log("filteredFlights",filteredFlights)
			
		// Process each flight to add the status based on timestamps
		    await Promise.all(filteredFlights.map(async flight => {
			const currentTime = currentDate.getTime()
			let status = "scheduled" // default status
			let baggageStataus ="not-defined"
			let pnrStatus = "confirmed"
	
		  	if (flight.departureTimestamp && flight.arrivalTimestamp) {
				const departureTime = new Date(flight.departureTimestamp).getTime()
				console.log("departureTime", departureTime)
				const arrivalTime = new Date(flight.arrivalTimestamp).getTime()
				console.log("arrivalTime", arrivalTime)
				// Check if the flight has departed
				if (currentTime > departureTime && currentTime < arrivalTime) {
					status = "departed"
					baggageStataus ="in-flight"
					pnrStatus = "confirmed"
				}
				if (currentTime >= arrivalTime) {  
			  status = "arrived"
					baggageStataus ="landed"
					pnrStatus = "completed"
				}
		  }
		  // If the status has changed, update it in DynamoDB
		  if (flight.flightStatus !== status) {
            console.log("params1234", status)
				const updateParams = {
					TableName: process.env.FLIGHT_SCHEDULE_TABLE,
					Key: { scheduleId: flight.scheduleId },
					UpdateExpression: "set flightStatus = :flightStatus",
					ExpressionAttributeValues: {
						":flightStatus": status
					},
					ReturnValues: "UPDATED_NEW"
				}
				console.log("params123", updateParams)
				await updateDynamoTableDataWithParams(updateParams)
				await baggageStatus(flight.scheduleId,baggageStataus)
				await updatePNRStatus(flight.scheduleId, pnrStatus)
			}
	
		  return flight
		}))
	} catch (error) {
		console.error("Error in shceduleFlightStatus:", error)
	}
}
module.exports = {
	scheduleFlightStatus
}