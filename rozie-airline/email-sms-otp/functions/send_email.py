import os
import json
import uuid
from helpers.logging_helper import (
    get_log_level,
    set_logging_level,
    print_info_log,
)
from helpers.decorator_helper import exception_handler
from helpers.sendgrid_helper import (
    create_mail,
    create_attachments,
    add_attachments,
    send_mail,
    create_bcc,
    add_bcc
)
from helpers.ses_helper import send_mail_via_ses
from helpers.response_helper import format_response 
from helpers.dynamo_helper import (
    get_resource_item_by_partition_key,
    get_resource_item_by_primary_key
)
CONFIG_TABLE = os.environ["CONFIG_TABLE"]


def send_email_handler(event):
    """
    Lambda handler for email service using either AWS SES or SendGrid.
    """
    print_info_log("", "Send email Invoked, Event", event)
    global_config = get_resource_item_by_partition_key(
    CONFIG_TABLE, "organizationId", "1"
    )
    print("global config :- ", global_config)
    EMAIL_SERVICE_PROVIDER = global_config["EMAIL_SERVICE_PROVIDER"] 

    reference_id = event.get("reference_id", str(uuid.uuid4()))
    to_emails = event.get("to")
    bcc_emails = event.get("bcc")
    subject = event.get("subject")
    html_body = event.get("html_body")
    send_separate = event.get("send_separate")
    attachment_config = event.get("attachment_config")

    try:
        print_info_log(reference_id, "reference ID", reference_id)
        
        if EMAIL_SERVICE_PROVIDER == "sendgrid":
            # Create the mail object for SendGrid
            from_email = global_config["sendGridEmail"]
            mail = create_mail(
                from_email=from_email,
                to_emails=to_emails,
                subject=subject,
                html_content=html_body,
                send_separate=send_separate,
            )
            print("MAIL :- ", mail)
            # Add attachments if provided
            if attachment_config:
                attachments = create_attachments(attachment_config)
                mail = add_attachments(mail, attachments)

            # Add BCC if provided
            if bcc_emails:
                for email in bcc_emails:
                    bbc = create_bcc(email)
                    mail = add_bcc(mail, bbc)

            # Send the email using SendGrid
            response = send_mail(mail, sendgird_api_key=os.environ["EMAIL_API_KEY"])
            print("response :- ", response)
            # Log details
            print_info_log(reference_id, "Send email (SendGrid), status_code", response.status_code)
            print_info_log(reference_id, "Send email (SendGrid), body", response.body)
            print_info_log(reference_id, "Send email (SendGrid), headers", response.headers)

            # Return success response
            return format_response(
                status_code=response.status_code,
                body={"reference_id": reference_id, "status": "Success"},
                message="Email sent successfully via SendGrid"
            )

        elif EMAIL_SERVICE_PROVIDER == "aws":
            # Send the email using AWS SES
            from_email = global_config["sesEmail"]
            response = send_mail_via_ses(
                sender=from_email,
                recipients=to_emails,
                message=html_body  
            )

            if response:
                print_info_log(reference_id, "Send email (SES), Message ID", response["MessageId"])
                return format_response(
                    status_code=200,
                    body={"reference_id": reference_id, "status": "Success"},
                    message="Email sent successfully via AWS SES"
                )
            else:
                return format_response(
                    status_code=500,
                    body={"reference_id": reference_id, "status": "Failed"},
                    message="Error occurred while sending email via AWS SES"
                )
        else:
            return format_response(400, {}, "Invalid email service specified. Use 'sendgrid' or 'ses'.")

    except Exception as e:
        print_info_log(reference_id, "Error while sending email", e)
        
        # Return error response using format_response
        return format_response(
            status_code=500,
            body={"reference_id": reference_id, "status": "Failed", "error": str(e)},
            message="Error occurred while sending email"
        )
