const { DynamoDBClient } = require('@aws-sdk/client-dynamodb')
const { DynamoDBDocumentClient, PutCommand, GetCommand } = require('@aws-sdk/lib-dynamodb')
const dynamoDbClient = DynamoDBDocumentClient.from(new DynamoDBClient())

/**
 * Put  items into a DynamoDB table.
 *
 * @param {string} tableName - The name of the DynamoDB table to retrieve items from.
 * @param {Object} Item - The item to put into the table.
 * @returns {Promise<Object>} - A promise that resolves with the response from the DynamoDB API.
 */
async function putDynamodbTableItem(tableName, Item) {
	const params = {
		TableName: tableName,
		Item: Item,
	}
	console.log('Params for putDynamodbTableItem()', params)
	const command = new PutCommand(params)

	const response = await dynamoDbClient.send(command)
	console.log(response)
	return response
}

/**
 * get the entry from DynamoDB table.
 *
 * @param {string} tableName - The name of the DynamoDB table to check.
 * @param {string} pKey - primaryKey to get.
 * @param {string} pValue - primaryKey value to get.
 * @returns {Promise<object>} - A promise that resolves with the item if it exists else empty.
 */
const retrieveDynamoEntry = async (tableName, pKey, pValue) => {
    console.log('Function retrieveDynamoEntry')
    try {
        const command = new GetCommand({
            TableName: tableName,
            Key: {
                [pKey]: pValue,
            }
        })
        console.log(`command => ${JSON.stringify(command)}`)
        const response = await dynamoDbClient.send(command)
        console.log(`response => ${JSON.stringify(response.Item)}`)
        return response.Item
    } catch (e) {
        console.log('Error while retrieving Entry from table:', e)
        return {}
    }
}

module.exports = {
    putDynamodbTableItem,
	retrieveDynamoEntry
}