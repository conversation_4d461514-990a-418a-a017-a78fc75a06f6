// ================================================================================================
// AI-DRIVEN PRIORITY AND RISK ASSESSMENT - BAML IMPLEMENTATION
// ================================================================================================
// Purpose: Intelligent priority scoring and risk assessment for insurance claims
// Enhancement: Replaces rule-based logic with AI-driven contextual assessment
// ================================================================================================

// ================================================================================================
// PRIORITY AND RISK SCHEMAS
// ================================================================================================

enum RiskCategory {
  FINANCIAL @description("Financial exposure risk")
  LEGAL @description("Legal liability or compliance risk")
  REGULATORY @description("Regulatory or statutory risk")
  REPUTATIONAL @description("Reputational damage risk")
  OPERATIONAL @description("Operational or processing risk")
  FRAUD @description("Potential fraud or misrepresentation risk")
  COVERAGE @description("Coverage interpretation or dispute risk")
}

class RiskFactor {
  category RiskCategory @description("Category of risk")
  severity "LOW" | "MEDIUM" | "HIGH" | "CRITICAL" @description("Severity level of this risk factor")
  description string @description("Detailed description of the risk factor")
  likelihood float @description("Likelihood of this risk materializing (0.0-1.0)")
  potentialImpact string @description("Potential impact if risk materializes")
  mitigationStrategy string @description("Recommended mitigation strategy")
  timeframeToResolve string @description("Expected timeframe to resolve or mitigate")
  requiresSpecialistReview bool @description("Whether this risk requires specialist review")
}

class PriorityRiskAssessment {
  overallPriorityLevel PriorityLevel @description("Overall priority level for this claim")
  overallRiskScore float @description("Overall risk score (0.0-1.0, higher = more risk)")
  
  // Priority factors
  priorityDrivers string[] @description("Key factors driving the priority level")
  timelineSensitivity string @description("How time-sensitive this claim is")
  stakeholderImpact string @description("Impact on key stakeholders if delayed")
  
  // Risk analysis
  identifiedRisks RiskFactor[] @description("All identified risk factors")
  highestRiskCategory RiskCategory @description("Category with highest risk exposure")
  riskMitigationPriority string[] @description("Prioritized list of risks to address first")
  
  // Human review requirements
  requiresHumanReview bool @description("Whether human expert review is required")
  requiresLegalCounsel bool @description("Whether legal counsel review is required")
  requiresSpecialistReview bool @description("Whether specialist review is required")
  reviewReason string @description("Primary reason for human/legal/specialist review")
  
  // Timeline and urgency
  recommendedProcessingTimeline string @description("Recommended processing timeline")
  escalationTriggers string[] @description("Conditions that would trigger escalation")
  urgencyJustification string @description("Justification for the assigned priority level")
  
  // Financial considerations
  estimatedFinancialExposure string @description("Estimated financial exposure range")
  costOfDelay string @description("Estimated cost of processing delays")
  
  // Quality and confidence
  assessmentConfidence float @description("Confidence in this priority/risk assessment (0.0-1.0)")
  uncertaintyFactors string[] @description("Factors that create uncertainty in assessment")
  
  // Recommendations
  immediateActions string[] @description("Actions that should be taken immediately")
  processOptimizations string[] @description("Opportunities to optimize processing")
  preventiveRecommendations string[] @description("Recommendations to prevent similar issues")
}

// ================================================================================================
// AI-DRIVEN PRIORITY AND RISK ASSESSMENT FUNCTION
// ================================================================================================

function AssessPriorityAndRisk(
  claimId: string,
  claimType: string,
  coverageDecision: string,
  coverageConfidence: float,
  level01Analysis: string,
  claimDetails: string,
  policyInformation: string,
  dataCompleteness: float,
  additionalContext: string
) -> PriorityRiskAssessment {
  client "openai/gpt-4o"
  prompt #"
    You are an AI expert in insurance risk management and claims prioritization, with deep knowledge of Canadian insurance regulations and industry best practices.
    
    MISSION: Intelligently assess priority level and risk factors for this insurance claim, considering financial exposure, legal complexity, regulatory requirements, and operational efficiency.
    
    CLAIM INFORMATION:
    Claim ID: {{ claimId }}
    Claim Type: {{ claimType }}
    Coverage Decision: {{ coverageDecision }}
    Coverage Confidence: {{ coverageConfidence }}
    Data Completeness: {{ dataCompleteness }}
    
    DETAILED ANALYSIS DATA:
    
    LEVEL 01 ANALYSIS:
    {{ level01Analysis }}
    
    CLAIM DETAILS:
    {{ claimDetails }}
    
    POLICY INFORMATION:
    {{ policyInformation }}
    
    ADDITIONAL CONTEXT:
    {{ additionalContext }}
    
    INTELLIGENT PRIORITY AND RISK ASSESSMENT REQUIREMENTS:
    
    1. PRIORITY LEVEL DETERMINATION:
       
       CRITICAL Priority if:
       - High financial exposure (>$500K potential liability)
       - Imminent legal deadlines or regulatory requirements
       - Potential fraud indicators
       - Media attention or reputational risk
       - Time-sensitive coverage disputes
       - Catastrophic claims requiring immediate response
       
       HIGH Priority if:
       - Significant financial exposure ($100K-$500K)
       - Legal complexity requiring specialist attention
       - Coverage disputes with precedent implications
       - Third-party liability with injury
       - Regulatory reporting requirements
       - Customer VIP status or business relationship impact
       
       MEDIUM Priority if:
       - Moderate financial exposure ($25K-$100K)
       - Standard liability or property claims
       - Clear coverage with minor complications
       - Routine regulatory compliance needs
       - Standard processing timelines adequate
       
       LOW Priority if:
       - Low financial exposure (<$25K)
       - Straightforward coverage decisions
       - No legal or regulatory complications
       - Routine administrative processing
       - No time-sensitive factors
    
    2. COMPREHENSIVE RISK ANALYSIS:
       
       FINANCIAL RISKS:
       - Assess total potential financial exposure
       - Consider policy limits, deductibles, and coverage gaps
       - Evaluate cost of delays vs. expedited processing
       - Factor in legal costs and settlement ranges
       
       LEGAL RISKS:
       - Identify potential litigation exposure
       - Assess regulatory compliance requirements
       - Consider precedent-setting implications
       - Evaluate limitation periods and deadlines
       
       REGULATORY RISKS:
       - Provincial insurance regulatory requirements
       - Federal compliance obligations
       - Industry reporting standards
       - Consumer protection considerations
       
       REPUTATIONAL RISKS:
       - Public visibility of claim
       - Social media or news coverage potential
       - Customer relationship implications
       - Industry reputation considerations
       
       OPERATIONAL RISKS:
       - Processing complexity and resource requirements
       - Data quality and completeness issues
       - Specialist expertise availability
       - Technology or system limitations
       
       FRAUD RISKS:
       - Unusual claim patterns or characteristics
       - Inconsistent information or documentation
       - Timing or circumstance red flags
       - Historical fraud indicators
    
    3. HUMAN REVIEW REQUIREMENTS:
       
       Require HUMAN EXPERT REVIEW if:
       - High financial exposure or complex liability
       - Unusual or precedent-setting circumstances
       - Data quality concerns affecting decision confidence
       - Coverage interpretation requiring judgment
       - Customer relationship management needs
       
       Require LEGAL COUNSEL REVIEW if:
       - Potential litigation exposure
       - Complex regulatory compliance issues
       - Coverage disputes with legal implications
       - Third-party claims with significant liability
       - Precedent-setting coverage decisions
       
       Require SPECIALIST REVIEW if:
       - Technical expertise needed (engineering, medical, etc.)
       - Industry-specific knowledge required
       - Complex valuation or assessment needs
       - Regulatory specialist input required
    
    4. TIMELINE AND URGENCY ASSESSMENT:
       
       Consider:
       - Statutory deadlines and limitation periods
       - Customer service commitments
       - Regulatory reporting requirements
       - Business impact of delays
       - Resource availability and capacity
       - Complexity of required analysis
    
    5. CONTEXT-SPECIFIC FACTORS:
       
       For {{ claimType }} claims, particularly consider:
       - Industry-specific risks and regulations
       - Typical processing timelines and complexities
       - Common dispute areas and precedents
       - Stakeholder expectations and requirements
       
       For {{ coverageDecision }} decisions with {{ coverageConfidence }} confidence:
       - Higher confidence may reduce priority
       - Lower confidence may increase human review needs
       - INFORMATION_REQUIRED decisions need expedited data gathering
       - NOT_COVERED decisions may require enhanced documentation
    
    6. CANADIAN INSURANCE CONTEXT:
       
       Factor in:
       - Provincial insurance regulations and requirements
       - Canadian court precedents and legal framework
       - Industry best practices and standards
       - Consumer protection obligations
       - Regulatory oversight and reporting requirements
    
    ASSESSMENT METHODOLOGY:
    
    - Use contextual intelligence rather than rigid rules
    - Consider interdependencies between factors
    - Provide specific, actionable recommendations
    - Balance efficiency with thoroughness
    - Account for unique circumstances and exceptions
    - Consider both immediate and long-term implications
    
    Be comprehensive, practical, and focused on enabling optimal claim processing decisions that balance efficiency, accuracy, and risk management.
    
    {{ ctx.output_format }}
  "#
} 