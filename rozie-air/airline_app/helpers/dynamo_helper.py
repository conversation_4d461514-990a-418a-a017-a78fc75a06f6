"""
This module contains DynamoDB helper functions used in the lambda functions.

Functions:
 - query_dynamodb_table_item_gsi
 - get_item_by_primary_key
"""

from logging import info
import boto3
from boto3.dynamodb.conditions import Key, Attr
from botocore.exceptions import ClientError
import logging

dynamodb = boto3.resource("dynamodb")


def query_dynamodb_table_item_gsi(table_name, index_name, key_conditions, scan_index_forward=False):
    table = dynamodb.Table(table_name)

    # Construct the KeyConditionExpression dynamically
    key_condition_expression = None
    expression_attribute_values = {}

    for idx, (key, value) in enumerate(key_conditions.items()):
        condition = Key(key).eq(value)  # Assuming equality conditions for simplicity
        key_condition_expression = (
            condition if key_condition_expression is None else key_condition_expression & condition
        )

    # Perform the query
    response = table.query(
        IndexName=index_name,
        KeyConditionExpression=key_condition_expression,
        ScanIndexForward=scan_index_forward,
    )
    return response.get("Items", [])


def get_item_by_primary_key(table, primary_key_id, primary_key_value):
    table = dynamodb.Table(table)
    key = {primary_key_id: primary_key_value}
    response = table.get_item(Key=key)
    return response.get("Item", {})


def put_record_into_dynamodb(table_name, record):
    """
    puts item in table
    """
    table = dynamodb.Table(table_name)
    table.put_item(Item=record)
    
def scan_table_with_filter(
    table_name, filter_expression_dict=None, projection_expression_list=None
):
    """
    Scan the DynamoDB table with optional filter and projection expressions.

    Args:
        table (string) : table name
        filter_expression_dict (dict): A dictionary of key-value pairs to filter the scan results.
        projection_expression_list (list): A list of attribute names to include in the scan results.

    Returns:
        list: A list of items from the DynamoDB table that match the filter conditions and
      include only the specified attributes.
    """
    try:
        table = dynamodb.Table(table_name)
        params = {}
        filter_expression = None
        projection_expression = None
        expression_attribute_names = None
        if filter_expression_dict:
            for key, value in filter_expression_dict.items():
                if isinstance(value, list):
                    attr_condition = None
                    for v in value:
                        condition = Attr(key).eq(v)
                        if attr_condition is None:
                            attr_condition = condition
                        else:
                            attr_condition = attr_condition | condition
                else:
                    attr_condition = Attr(key).eq(value)
                if filter_expression is None:
                    filter_expression = attr_condition
                else:
                    filter_expression = filter_expression & attr_condition
            params["FilterExpression"] = filter_expression

        if projection_expression_list:
            projection_expression = ", ".join(
                f"#{attr}" for attr in projection_expression_list
            )
            params["ProjectionExpression"] = projection_expression
            expression_attribute_names = {
                f"#{attr}": attr for attr in projection_expression_list
            }
            params["ExpressionAttributeNames"] = expression_attribute_names

        items = []
        while True:
            response = table.scan(**params)
            items.extend(response.get("Items", []))
            last_evaluated_key = response.get("LastEvaluatedKey")
            if not last_evaluated_key:
                break
            params["ExclusiveStartKey"] = last_evaluated_key

        return response.get("Items", [])
    except Exception as error:
        raise error

# def delete_item(table, primary_key_id, primary_key_value):
#     """
#     Deletes an item from the specified DynamoDB table.

#     Args:
#         table (string) : table name
#         primary_key_id (string): The primary key attribute name of the item to get from the table.
#         primary_key_value (string): The primary key value of the item to get from the table.

#     Returns:
#         dict: A dictionary indicating the success or failure of the operation.
#     """
#     table = dynamodb.Table(table)
#     try:
#         key = {primary_key_id: primary_key_value}
#         response = table.delete_item(Key=key)
#         return response
#     except Exception as error:
#         logging.info("Error while deleting item from %s ", table)
#         raise error


# def put_ddb_table_item(table_name, item_dict, primary_key, sort_key=None):
#     """
#     Gets an item from the specified DynamoDB table.

#     Args:
#         table_name (string): The DynamoDB table name.
#         item_dict (dict): dictionary of attribute and value to update
#         primary_key: "primary key to check if item exists"
#         sort_key: "sort key to check if item exists"

#     Returns:
#         dict: A dictionary indicating the success or failure of the operation.
#     """
#     try:
#         table = dynamodb.Table(table_name)
#         condition_expression = f"attribute_not_exists({primary_key})"
#         if sort_key:
#             condition_expression = f"attribute_not_exists({primary_key}) and attribute_not_exists({sort_key})"
#         param = {"Item": item_dict, "ConditionExpression": condition_expression}
#         table.put_item(**param)
#         return "Success", {}
#     except ClientError as exception:
#         if exception.response["Error"]["Code"] == "ConditionalCheckFailedException":
#             key_dict = {primary_key: item_dict[primary_key]}
#             if sort_key:
#                 key_dict[sort_key] = item_dict[sort_key]
#             print_info_log("WARN", "put_table_item", f"key found in table {table_name}")
#             return "Update", key_dict
#         print_info_log(
#             "ERROR",
#             "put_table_item",
#             f"Error while added {str(item_dict)} in table {table_name}: {str(exception)}",
#         )
#         return "Error", {}


def update_ddb_table_item(table_name, key_dict, item_dict):
    """
    update a specific item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        key_dict (dict): key dictionary
        item_dict (dict): dictionary of attribute and value to update

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)
        for _key in key_dict:
            del item_dict[_key]

        update_expression = ",".join(
            list(map(lambda key: f"{key} = :{key}", list(item_dict.keys())))
        )  # converts keys in 'key = :key' format
        expression_attribute_values = {}
        for key in item_dict:
            expression_attribute_values[f":{key}"] = item_dict[key]
        param = {
            "Key": key_dict,
            "UpdateExpression": f"SET {update_expression}",
            "ExpressionAttributeValues": expression_attribute_values,
        }
        table.update_item(**param)
        return "Success"
    except ClientError as exception:
        print(
            "ERROR",
            "update_table_item",
            f"Error while updating {str(item_dict)} in table {table_name}: {str(exception)}",
        )
        return "Error"


# def remove_ddb_table_item_attribute(table_name, key_dict, attributes_to_remove):
#     """
#     update a specific item from the specified DynamoDB table.

#     Args:
#         table_name (string): The DynamoDB table name.
#         key_dict (dict): key dictionary
#         item_dict (dict): dictionary of attribute and value to update

#     Returns:
#         dict: A dictionary indicating the success or failure of the operation.
#     """
#     try:
#         table = dynamodb.Table(table_name)

#         update_expression = ",".join(attributes_to_remove)
#         param = {"Key": key_dict, "UpdateExpression": f"REMOVE {update_expression}"}
#         table.update_item(**param)
#         return "Success"
#     except ClientError as exception:
#         print_info_log(
#             "ERROR",
#             "update_table_item",
#             f"Error while removing {str(attributes_to_remove)} in table {table_name}: {str(exception)}",
#         )
#         return "Error"

# def upsert_item(table_name, item_dict, primary_key, sort_key=None):

#     status, key_dict = put_ddb_table_item(table_name, item_dict, primary_key, sort_key)

#     if status == "Update":
#         status = update_ddb_table_item(table_name, key_dict, item_dict)

#     return status

def retrieve_dynamo_table_data(table_name):
    """
    Retrieves data from the specified DynamoDB table.

    Parameters:
    - table_name (str): The name of the DynamoDB table to retrieve data from.

    Returns:
    - list: A list containing items retrieved from the DynamoDB table.

    This function connects to DynamoDB using the boto3 library, performs a scan
    operation on the specified table, and returns the items retrieved. If any
    error occurs during the process, it is logged and re-raised.
    """
    try:
        logging.info(f"Retrieving data from table {table_name}")
        table = dynamodb.Table(table_name)
        response = table.scan()
        logging.info(f"Data retrieved from table {table_name}")
        return response.get("Items", [])
    except Exception as e:
        logging.error(f"Error while retrieving data from table {table_name}: {str(e)}")
        raise e
    

def query_items_by_gsi(table_name, index_name, key_condition_expression, expression_attribute_values):
    """
    Query items from a DynamoDB table using a Global Secondary Index.

    Parameters:
    - table_name (str): The name of the DynamoDB table
    - index_name (str): Name of the GSI to query
    - key_condition_expression (str): The key condition expression for the query
    - expression_attribute_values (dict): Values for the expression attributes

    Returns:
    - list: List of items matching the query criteria
    """
    try:
        logging.info(f"Querying items from table {table_name} using GSI {index_name}")
        table = dynamodb.Table(table_name)
        response = table.query(
            IndexName=index_name,
            KeyConditionExpression=key_condition_expression,
            ExpressionAttributeValues=expression_attribute_values
        )
        logging.info(f"Items retrieved from table {table_name} using GSI {index_name}")
        return response.get("Items", [])
    except Exception as e:
        logging.error(f"Error while querying items from table {table_name} using GSI {index_name}: {str(e)}")
        raise e

def query_items_by_primary_key(table_name, key_condition_expression, expression_attribute_values):
    """
    Query items from a DynamoDB table using the primary key.

    Parameters:
    - table_name (str): The name of the DynamoDB table
    - key_condition_expression (str): The key condition expression for the query
    - expression_attribute_values (dict): Values for the expression attributes

    Returns:
    - list: List of items matching the query criteria
    """
    try:
        logging.info(f"Querying items from table {table_name} using primary key")
        table = dynamodb.Table(table_name)
        response = table.query(
            KeyConditionExpression=key_condition_expression,
            ExpressionAttributeValues=expression_attribute_values
        )
        logging.info(f"Items retrieved from table {table_name} using primary key")
        return response.get("Items", [])
    except Exception as e:
        logging.error(f"Error while querying items from table {table_name} using primary key: {str(e)}")
        raise e
