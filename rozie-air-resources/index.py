import os
import json
from helpers.logging_helper import get_log_level, set_logging_level, print_info_log
from helpers.dynamo_helper import put_record_into_dynamodb
from helpers.decorator_helper import exception_handler
from helpers.response_helper import format_response

from twilio.twiml.voice_response import VoiceResponse

from decimal import Decimal


def read_json_file(file_path):
    """
    Reads a JSON file and returns its contents as a Python object.

    :param file_path: Path to the JSON file
    :return: Parsed JSON data as a Python object (list or dict)
    """
    try:
        with open(file_path, "r") as file:
            data = json.load(file)
        return data
    except FileNotFoundError:
        print(f"Error: The file at '{file_path}' was not found.")
    except json.JSONDecodeError as e:
        print(f"Error: Failed to decode JSON - {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")


def create_flights():
    """
    Creates an array of flight entries with seat configurations and booking details.
    """
    # Load data
    prices = {}
    flights_data = read_json_file("resources/flights.json")
    if not flights_data:
        return []

    mock_flight_data = []

    # Process each flight in flights_data
    for flight in flights_data:

        flight_detail = {
            "flight_id": flight["flight_id"],
            "airline": flight["airline"],
            "arrival_airport": flight["arrival_airport"],
            "departure_airport": flight["departure_airport"],
            "arrival_airport_city": flight["arrival_airport_city"],
            "departure_airport_city": flight["departure_airport_city"],
            "flight_number": flight["flight_number"],
            "arrival": flight["arrival"],
            "departure": flight["departure"],
            "delay": flight["delay"],
            "delay_reason": flight["delay_reason"],
            "seats": [],
            "offered_services": {},
            "included_services":{},
            "services": flight["services"],
        }

        if flight["arrival"] < 0 and flight["departure"] < 0:
            prices[flight["flight_id"]] = {}
        # Process seat configuration
        for class_name, config in flight["seat_config"].items():
            base_price = Decimal(config["base_price"])

            for row in config["rows"]:

                for seat_type, columns in config["seats"].items():
                    for column in columns:
                        seat_number = f"{row}{column}"
                        price = base_price

                        if seat_type == "window":
                            price *= Decimal(1.2)
                        elif seat_type == "aisle":
                            price *= Decimal(1.1)

                        flight_detail["seats"].append(
                            {
                                "seat_number": seat_number,
                                "type": seat_type,
                                "class": class_name,
                                "price": round(price, 2),  # Convert to float for JSON compatibility
                            }
                        )
                        if flight["flight_id"] in prices:
                            prices[flight["flight_id"]][f"{class_name}-{seat_number}"] = round(price, 2)
        
            flight_detail["included_services"][class_name] = config["included_services"]

        flight_detail["offered_services"] = flight["flight_services"]

        # Append the processed flight data
        mock_flight_data.append(flight_detail.copy())

    return prices, mock_flight_data


@exception_handler
def lambda_handler(event, context):
    """
    Lambda handler generating mock airline data
    """
    set_logging_level(get_log_level())
    print_info_log("", "mock-data-gen invoked", event)
    loyalty_details = read_json_file("resources/loyalty.json")
    customers_details = read_json_file("resources/customers.json")
    pnrs_details = read_json_file("resources/pnrs.json")
    airport_mapping_data = read_json_file("resources/airport_mapping.json")
    baggage_data = read_json_file("resources/baggage.json")

    flight_prices, flights_details = create_flights()
    for flight in flights_details:
        put_record_into_dynamodb(os.environ.get("FLIGHTS_TABLE"), flight)
    print_info_log("", "flights_details", "added")

    for customer in customers_details:
        put_record_into_dynamodb(os.environ.get("CUSTOMERS_TABLE"), customer)
    print_info_log("", "customers_details", "added")

    for pnr in pnrs_details:
        if pnr["flight_id"] in flight_prices:
            key = f"points for PNR {pnr['pnr_id']}"
            value = 0
            for passenger in pnr["passengers"]:
                _key = f"{passenger['passenger_class']}-{passenger['seat']}"
                value = value + flight_prices[pnr["flight_id"]][_key]
            loyalty_details[pnr["customer_id"]]["transaction_history"].append({key: value})
        put_record_into_dynamodb(os.environ.get("PNRS_TABLE"), pnr)

    print_info_log("", "loyalty_details", "added")

    for customer_id, loyalty in loyalty_details.items():
        put_record_into_dynamodb(os.environ.get("LOYALTY_TABLE"), loyalty)

    print_info_log("", "pnrs_details", "added")

    for airport in airport_mapping_data:
        put_record_into_dynamodb(os.environ.get("AIRPORT_MAPPING_TABLE"), airport)
    print_info_log("", "airport_mapping_data", "added")

    for baggage in baggage_data:
        put_record_into_dynamodb(os.environ.get("BAGGAGE_TABLE"), baggage)
    print_info_log("", "baggage_data", "added")

    if event.get("headers").get("X-Twilio-Signature"):
        response = VoiceResponse()
        response.say("Rozie Air Data reset successful.")
        response.hangup()
        return {
            "statusCode": 200,
            "headers": {"Content-Type": "application/xml"},
            "body": str(response),
        }
    return format_response(200, {}, "Success")


# os.environ["FLIGHTS_TABLE"] = "rozie-air-resources-flights-dev"
# os.environ["CUSTOMERS_TABLE"] = "rozie-air-resources-customers-dev"
# os.environ["PNRS_TABLE"] = "rozie-air-resources-pnrs-dev"
# os.environ["LOYALTY_TABLE"] = "rozie-air-resources-loyalty-details-dev"
#os.environ["AIRPORT_MAPPING_TABLE"] = "rozie-air-resources-airport-mapping-dev"

# lambda_handler({}, {})
