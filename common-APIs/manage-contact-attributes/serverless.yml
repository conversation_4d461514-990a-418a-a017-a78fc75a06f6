service: rozie-mock-contact-attributes
custom:
  dev:
    CONNECT_INSTANCE_ID: 710e5888-0655-4bf1-bf5c-23bacfd46d22
    SOURCE_PHONE_NUMBER: +18557528270
    OUTBOUND_CONTACT_FLOW_ID: f537ba5f-dac3-43ce-8eec-61f264f5d154
provider:
  name: aws
  runtime: nodejs18.x
  endpointType: regional
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 60
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:Scan
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - connect:* 
      Resource: "*"
  apiGateway:
    apiKeys:
      - ${self:service}-${self:provider.stage}-Api-Key

functions:
  customer-profile:
    name: ${self:service}-${self:provider.stage}
    handler: index.handler
    description: function to manage the contact attributes
    environment:
      ENABLE_LOG: TRUE
      CONNECT_INSTANCE_ID: ${self:custom.${self:provider.stage}.CONNECT_INSTANCE_ID}
      SOURCE_PHONE_NUMBER: ${self:custom.${self:provider.stage}.SOURCE_PHONE_NUMBER}
      OUTBOUND_CONTACT_FLOW_ID: ${self:custom.${self:provider.stage}.OUTBOUND_CONTACT_FLOW_ID}
      REGION: ${aws:region}
      TABLE_NAME: ${self:service}-${self:provider.stage}
    events:
      - http:
          path: /contactAttributes
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang

      - http:
          path: /contactAttributes
          method: get
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang

resources:
  Resources:
    ConactAttributesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: toPhoneNumber
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: toPhoneNumber
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True  
