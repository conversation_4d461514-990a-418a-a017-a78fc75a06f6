"""
This module contains lambda helper functions used in the lambda functions.

Functions:
 - invoke_lambda

"""

import json
import boto3

lambda_client = boto3.client("lambda")


def invoke_lambda(function_name, invoke_args):
    """
    Invokes lambda function asynchronously

    Args:
        function_name (string): function name to be invoked
        invoke_args (dict): input parameter for the functions


    Returns:
        dict: returns the response
    """
    params = {
        "FunctionName": function_name,
        "InvokeArgs": json.dumps(invoke_args, default=str),
    }
    response = lambda_client.invoke_async(**params)

    return response
