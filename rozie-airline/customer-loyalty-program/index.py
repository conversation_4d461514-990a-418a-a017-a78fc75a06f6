"""
Module for handling Airline's loyalty program
"""

import json
import os
import random
import datetime
from helpers.logging_helper import get_log_level, set_logging_level, print_info_log
from helpers.decorator_helper import exception_handler
from helpers.dynamo_helper import (
    get_item,
    get_item_gsi_query,
    put_table_item,
    scan_table_with_filter,
    upsert_item,
    delete_item,
)
from helpers.response_helper import format_response


@exception_handler
def lambda_handler(event, context):
    """
    Lambda handler for flight details crud operation APIs
    """
    set_logging_level(get_log_level())
    print_info_log("", "Flight Details api invoked", event)
    http_method = event.get("httpMethod", "")
    path = event.get("path", "")

    if http_method == "POST" and path == "/loyalty/create-member":
        return create_member(event)
    if http_method == "POST" and path == "/loyalty/add-points":
        return add_loyalty_points(event)
    if http_method == "POST" and path == "/loyalty/use-points":
        return use_loyalty_points(event)
    if http_method == "GET" and path == "/loyalty/get-points":
        return get_available_loyalty_points(event)
    if http_method == "GET" and path == "/loyalty/get-points-history":
        return get_loyalty_points_history(event)
    if http_method == "DELETE" and path == "/loyalty/delete-member":
        return delete_loyalty_member(event)
    return format_response(400, None, "Invalid request")


def check_customer_valid(customer_id=None, phone_number=None):
    """
    Check if customer is valid
    """

    if customer_id:
        profile_data = get_item(
            os.environ.get("CUSTOMER_DETAILS_TABLE"), "customer_id", customer_id
        )
    elif phone_number:
        if phone_number.startswith(" "):
            phone_number = "+" + phone_number.strip()
        profile_data = get_item_gsi_query(
            os.environ.get("CUSTOMER_DETAILS_TABLE"),
            "PhoneNumberIndex",
            "phone_number",
            phone_number,
        )
    if profile_data:
        return True, profile_data
    return False, None


def membership_number_generate():
    number = str(random.randint(1000000, 9999999))
    item = get_item_gsi_query(
        os.environ.get("LOYALTY_DETAILS_TABLE"),
        os.environ.get("LOYALTY_NUMBER_GSI"),
        "loyalty_number",
        number,
    )
    if item:
        membership_number_generate()
    return number


def create_member(event):
    """
    Create member for loyalty program
    """
    body = json.loads(event.get("body", "{}"))
    status, customer_profile = check_customer_valid(customer_id=body.get("customer_id"))
    if status:
        loyalty_number = membership_number_generate()
        item = {
            "customer_id": body.get("customer_id"),
            "loyalty_number": loyalty_number,
            "membership_tier": "Gold",
            "membership_status": "Active",
            "loyalty_points": 0,
            "point_history": [],
        }
        put_table_item(os.environ.get("LOYALTY_DETAILS_TABLE"), item, "phone_number")
        return format_response(200, None, "Member created successfully")
    return format_response(
        409, None, "CUstomer With provided phone number does not exist."
    )


def add_loyalty_points(event):
    """
    Add loyalty points to customer
    """
    body = json.loads(event.get("body", "{}"))
    if body.get("customer_id") is None:
        return format_response(400, None, "Customer ID is required")
    status, customer_profile = check_customer_valid(customer_id=body.get("customer_id"))
    if status:
        loyalty_details = get_item(
            os.environ.get("LOYALTY_DETAILS_TABLE"),
            "customer_id",
            body.get("customer_id"),
        )
        if loyalty_details:
            loyalty_details["loyalty_points"] += body.get("loyalty_points")
            loyalty_details["point_history"].append(
                {
                    "transaction_message": body.get("transaction_message"),
                    "transaction_type": "DEBIT",
                    "loyalty_points": body.get("loyalty_points"),
                    "transaction_date": datetime.datetime.now().isoformat(),
                }
            )
            upsert_item(
                os.environ.get("LOYALTY_DETAILS_TABLE"),
                loyalty_details,
                "customer_id",
            )
            return format_response(200, None, "Loyalty points added successfully")
        return format_response(409, None, "Customer is not a member")
    return format_response(409, None, "Customer With provided ID does not exist.")


def use_loyalty_points(event):
    """
    Use loyalty points from customer
    """
    body = json.loads(event.get("body", "{}"))
    if body.get("customer_id") is None:
        return format_response(400, None, "Customer ID is required")
    status, customer_profile = check_customer_valid(customer_id=body.get("customer_id"))
    if status:
        loyalty_details = get_item(
            os.environ.get("LOYALTY_DETAILS_TABLE"),
            "customer_id",
            body.get("customer_id"),
        )
        if loyalty_details:
            if loyalty_details["loyalty_points"] >= body.get("loyalty_points"):
                loyalty_details["loyalty_points"] -= body.get("loyalty_points")
                loyalty_details["point_history"].append(
                    {
                        "transaction_message": body.get("transaction_message"),
                        "transaction_type": "CREDIT",
                        "loyalty_points": body.get("loyalty_points"),
                        "transaction_date": datetime.datetime.now().isoformat(),
                    }
                )
                upsert_item(
                    os.environ.get("LOYALTY_DETAILS_TABLE"),
                    loyalty_details,
                    "customer_id",
                )
                return format_response(200, None, "Loyalty points used successfully")
            return format_response(409, None, "Insufficient loyalty points")
        return format_response(409, None, "Customer is not a member")
    return format_response(409, None, "Customer With provided ID does not exist.")


def get_available_loyalty_points(event):
    """
    Get available loyalty points for customer
    """
    query_params = event.get("queryStringParameters") or {}
    if query_params.get("customer_id") is None:
        return format_response(400, None, "Customer ID is required")
    status, customer_profile = check_customer_valid(customer_id=query_params.get("customer_id"))
    if status:
        loyalty_details = get_item(
            os.environ.get("LOYALTY_DETAILS_TABLE"),
            "customer_id",
            query_params.get("customer_id"),
        )
        if loyalty_details:
            return format_response(
                200,
                loyalty_details,
                "Loyalty points retrieved successfully",
            )
        return format_response(409, None, "Customer is not a member")
    return format_response(409, None, "Customer With provided ID does not exist.")


def get_loyalty_points_history(event):
    """
    Get loyalty points history for customer
    """
    query_params = event.get("queryStringParameters") or {}
    if query_params.get("customer_id") is None:
        return format_response(400, None, "Customer ID is required")
    status, customer_profile = check_customer_valid(customer_id=query_params.get("customer_id"))
    if status:
        loyalty_details = get_item(
            os.environ.get("LOYALTY_DETAILS_TABLE"),
            "customer_id",
            query_params.get("customer_id"),
        )
        if loyalty_details:
            return format_response(
                200,
                loyalty_details["point_history"],
                "Loyalty points history retrieved successfully",
            )
        return format_response(409, None, "Customer is not a member")
    return format_response(409, None, "Customer With provided ID does not exist.")


def delete_loyalty_member(event):
    """
    Delete loyalty member
    """
    body = json.loads(event.get("body", "{}"))
    if body.get("customer_id") is None:
        return format_response(400, None, "Customer ID is required")
    status, customer_profile = check_customer_valid(customer_id=body.get("customer_id"))
    if status:
        loyalty_details = get_item(
            os.environ.get("LOYALTY_DETAILS_TABLE"),
            "customer_id",
            body.get("customer_id"),
        )
        if loyalty_details:
            delete_item(
                os.environ.get("LOYALTY_DETAILS_TABLE"),
                {"customer_id": body.get("customer_id")},
            )
            return format_response(200, None, "Loyalty member deleted successfully")
        return format_response(409, None, "Customer is not a member")
    return format_response(409, None, "Customer With provided ID does not exist.")
