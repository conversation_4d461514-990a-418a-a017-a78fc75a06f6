/* eslint-disable no-case-declarations */
const { ApiResp } = require("./helpers/api-helper.js")
const { pnrWrapper } = require("./wrappers/pnrWrapper.js")
const { baggageWrapper } = require("./wrappers/baggageWrapper.js")
const { caseManagementWrapper } = require("./wrappers/caseManagementWrapper.js")

const lambda_handler = async (event) => {
	console.log(JSON.stringify(event))
	let body = JSON.parse(event.body)
	const { customer_id } = body
	const path = event.resource
    
	try {
		switch (path) {
		case "/pnrWrapper":
			const pnrWrapperResponse = await pnrWrapper(customer_id)
			return pnrWrapperResponse

		case "/baggageWrapper":
			const baggageWrapperResponse = await baggageWrapper(customer_id)
			return baggageWrapperResponse

		case "/caseManagementWrapper":
			const caseWrapperResponse = await caseManagementWrapper(customer_id)
			return caseWrapperResponse
		
		default:
			return ApiResp({
				status: "Error",
				message: "Invalid resource path"
			}, 400)
		}
	} catch (error) {
		console.log(error)
		return ApiResp({
			status: "Error",
			message: "Internal Server Error"
		}, 500)
	}
}

module.exports = {
	lambda_handler,
}
