
from middleware_app.helpers.dynamo_helper import get_item_by_primary_key, upsert_item

TWILIO_CONTACT_ATTRIBUTE = "twilio-contact-attribute-dev"

def get_previous_interaction_concept(chat_Id):
    item = get_item_by_primary_key(
        TWILIO_CONTACT_ATTRIBUTE, "contact_id", chat_Id
    )
    if item:
        return item.get("concept_id", None)

def update_previous_interaction_concept(chat_Id, concept_id):
    item = {
        "contact_id": chat_Id,
        "concept_id": concept_id,
    }
    upsert_item(
        TWILIO_CONTACT_ATTRIBUTE, item, "contact_id"
    )

def convert_floats_to_str(data):
    if isinstance(data, dict):
        return {k: convert_floats_to_str(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_floats_to_str(v) for v in data]
    elif isinstance(data, float):
        return str(data)
    else:
        return data

def update_middleware_response(response, chat_Id):
    print("update_middleware_response", response)
    if response:
        response = convert_floats_to_str(response)
        item = {
            "contact_id": chat_Id,
            "middleware_response": response
        }
        upsert_item(
            TWILIO_CONTACT_ATTRIBUTE, item, "contact_id"
        )
