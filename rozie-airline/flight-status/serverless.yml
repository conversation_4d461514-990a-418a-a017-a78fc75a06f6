service: rozie-mock-airline-flight-status


provider:
  name: aws
  runtime: nodejs18.x
  endpointType: regional
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:Scan
        - dynamodb:DeleteItem
        - dynamodb:UpdateItem
        - dynamodb:Query
      Resource: "*"
  apiGateway:
    apiKeys:
      - ${self:service}-${self:provider.stage}-Api-Key

functions:
  flight-status:
    name: ${self:service}-${self:provider.stage}
    handler: index.lambda_handler
    description: function to manage the pnr details
    timeout : 900
    events:
      - schedule:
          rate: rate(10 minutes)


    environment:
      PNR_TABLE_NAME: rozie-mock-airline-pnr-details-${self:provider.stage}
      FLIGHT_SCHEDULE_TABLE: rozie-mock-airline-flight-schedule-${self:provider.stage}
      BAGGAGE_TABLE_NAME: rozie-mock-airline-baggage-details-${self:provider.stage}

resources:
  extensions:
    ServerlessDeploymentBucket:
      Properties:
        PublicAccessBlockConfiguration:
          BlockPublicAcls: True
          BlockPublicPolicy: True
          IgnorePublicAcls: True
          RestrictPublicBuckets: True
