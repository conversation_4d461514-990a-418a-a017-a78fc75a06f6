
:root {
  /* Colors */
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);
  --color-select-caret: rgba(19, 52, 59, 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-primary-rgb: 33, 128, 141;
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);
    --color-surface: rgba(38, 40, 40, 1);
    --color-text: rgba(245, 245, 245, 1);
    --color-text-secondary: rgba(167, 169, 169, 0.7);
    --color-primary: rgba(50, 184, 198, 1);
    --color-primary-hover: rgba(45, 166, 178, 1);
    --color-primary-active: rgba(41, 150, 161, 1);
    --color-secondary: rgba(119, 124, 124, 0.15);
    --color-secondary-hover: rgba(119, 124, 124, 0.25);
    --color-secondary-active: rgba(119, 124, 124, 0.3);
    --color-border: rgba(119, 124, 124, 0.3);
    --color-error: rgba(255, 84, 89, 1);
    --color-success: rgba(50, 184, 198, 1);
    --color-warning: rgba(230, 129, 97, 1);
    --color-info: rgba(167, 169, 169, 1);
    --color-focus-ring: rgba(50, 184, 198, 0.4);
    --color-btn-primary-text: rgba(19, 52, 59, 1);
    --color-card-border: rgba(119, 124, 124, 0.2);
    --color-card-border-inner: rgba(119, 124, 124, 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(119, 124, 124, 0.2);
    --color-border-secondary: rgba(119, 124, 124, 0.2);
    --color-select-caret: rgba(245, 245, 245, 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-primary-rgb: 50, 184, 198;
    --color-success-rgb: 50, 184, 198;
    --color-error-rgb: 255, 84, 89;
    --color-warning-rgb: 230, 129, 97;
    --color-info-rgb: 167, 169, 169;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
  --color-card-border: rgba(119, 124, 124, 0.15);
  --color-card-border-inner: rgba(119, 124, 124, 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(119, 124, 124, 0.2);
  --color-select-caret: rgba(245, 245, 245, 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-primary-rgb: 50, 184, 198;
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;
}

[data-color-scheme="light"] {
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);

  /* RGB versions for light mode */
  --color-primary-rgb: 33, 128, 141;
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--zendesk {
  background: #0066CC;
  border: 1px solid #0066CC;
  color: white;
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.btn--zendesk:hover {
  background: #0052A3;
  border-color: #0052A3;
}

.btn--zendesk .btn-icon {
  font-size: 14px;
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* Claims Liability Reviewer Styles */

/* Header Styles */
.claim-header {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  padding: var(--space-16) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-sm);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-24);
}

.claim-info {
  flex: 1;
}

.claim-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.claim-reference {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.reference-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.claim-status-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-12);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.status-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.confidence-display {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.confidence-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  white-space: nowrap;
}

.confidence-meter {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.confidence-bar {
  width: 120px;
  height: 8px;
  background: var(--color-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.confidence-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--color-warning) 0%, var(--color-success) 100%);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
}

.confidence-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  min-width: 30px;
}

.quick-actions {
  display: flex;
  gap: var(--space-8);
}

/* Dashboard Layout */
.dashboard-container {
  display: grid;
  grid-template-columns: 350px 1fr 320px;
  gap: var(--space-16);
  padding: var(--space-16);
  min-height: calc(100vh - 120px);
}

/* Panel Styles */
.document-panel,
.analysis-panel,
.details-panel {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  height: fit-content;
  max-height: calc(100vh - 160px);
}

/* Center panel (Documents) gets more prominence */
.document-panel {
  border: 2px solid var(--color-primary);
  box-shadow: var(--shadow-md);
}

/* Left panel (Analysis) is more compact */
.analysis-panel {
  max-height: calc(100vh - 160px);
  overflow-y: auto;
}

.panel-header {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0;
}

/* Document Panel */
.document-content {
  flex: 1;
  overflow: hidden;
}

.document-tabs {
  border-bottom: 1px solid var(--color-card-border-inner);
}

.tab-list {
  display: flex;
  padding: 0 var(--space-16);
}

.tab-btn {
  background: none;
  border: none;
  padding: var(--space-12) var(--space-16);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all var(--duration-fast) var(--ease-standard);
}

.tab-btn.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

.tab-btn:hover {
  color: var(--color-text);
}

.document-viewer {
  height: auto;
  max-height: none;
  overflow-y: auto;
  padding: 0;
  background: var(--color-bg-secondary);
}

/* Unified Document View Styles */
.unified-document-view {
  padding: var(--space-20);
  max-height: 100%;
  overflow-y: auto;
}

.document-section {
  background: var(--color-bg-primary);
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.section-header {
  background: var(--color-bg-secondary);
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-border);
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.email-content-unified {
  padding: 20px;
}

.email-meta {
  background: var(--color-bg-secondary);
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid var(--color-border);
}

.meta-row {
  display: flex;
  margin-bottom: 8px;
}

.meta-row:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-weight: 600;
  color: var(--color-text-secondary);
  min-width: 80px;
}

.meta-value {
  color: var(--color-text);
}

.email-body {
  line-height: 1.6;
  color: var(--color-text);
}

.email-body p {
  margin-bottom: 16px;
}

.documents-list-unified {
  padding: 0;
}

.document-item-unified {
  border-bottom: 1px solid var(--color-border);
}

.document-item-unified:last-child {
  border-bottom: none;
}

.document-header-unified {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border);
}

.document-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text);
}

.document-type-badge {
  background: var(--color-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.document-content-unified {
  padding: 20px;
}

.ocr-text-content {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--color-text);
}

.ocr-line {
  margin-bottom: 8px;
}

.ocr-line:last-child {
  margin-bottom: 0;
}

/* AI-Based Intelligent Highlighting System */
.ocr-line:hover {
  background-color: rgba(64, 196, 255, 0.15);
  cursor: pointer;
  border-radius: 2px;
  padding: 2px 4px;
  margin: -2px -4px;
  transition: all 0.2s ease;
}

.ocr-line.highlighted {
  background-color: rgba(64, 196, 255, 0.25);
  border-radius: 2px;
  padding: 2px 4px;
  margin: -2px -4px;
}

/* Intelligent Highlighting Classes */
.intelligent-highlight {
  background: linear-gradient(120deg, rgba(64, 196, 255, 0.3) 0%, rgba(64, 196, 255, 0.1) 100%);
  border-radius: 3px;
  padding: 2px 4px;
  margin: -2px -4px;
  border-left: 3px solid var(--color-primary);
  transition: all 0.3s ease;
  cursor: pointer;
}

.intelligent-highlight.analysis-triggered {
  background: linear-gradient(120deg, rgba(255, 193, 7, 0.3) 0%, rgba(255, 193, 7, 0.1) 100%);
  border-left-color: #ffc107;
}

.intelligent-highlight.semantic-related {
  background: linear-gradient(120deg, rgba(76, 175, 80, 0.3) 0%, rgba(76, 175, 80, 0.1) 100%);
  border-left-color: #4caf50;
}

.intelligent-highlight.high-confidence {
  box-shadow: 0 0 8px rgba(64, 196, 255, 0.4);
}

.intelligent-highlight.user-search {
  background: linear-gradient(120deg, rgba(255, 235, 59, 0.4) 0%, rgba(255, 235, 59, 0.2) 100%);
  border-left-color: #ffeb3b;
  font-weight: bold;
}

/* Auto-Scroll Emphasis Effect */
.scroll-emphasis {
  animation: scrollPulse 2s ease-in-out;
  transform-origin: center;
}

@keyframes scrollPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(64, 196, 255, 0.7);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 20px 10px rgba(64, 196, 255, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(64, 196, 255, 0);
  }
}

/* Two-Way Highlighting Connection Lines */
.highlight-connection {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
}

.highlight-connection::before {
  content: '';
  position: absolute;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, var(--color-primary), transparent);
  left: 50%;
  transform: translateX(-50%);
  animation: connectionPulse 2s infinite;
}

@keyframes connectionPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

/* Analysis Card Highlighting States */
.analysis-card.related-active {
  border-color: var(--color-primary);
  box-shadow: 0 4px 12px rgba(64, 196, 255, 0.3);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

.analysis-card.semantic-match {
  background: linear-gradient(135deg, rgba(64, 196, 255, 0.05) 0%, transparent 100%);
}

/* Smart Hover Preview */
.preview-highlight {
  background-color: rgba(64, 196, 255, 0.1);
  border-radius: 2px;
  transition: all 0.2s ease;
}

/* Confidence Indicators */
.confidence-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.confidence-high {
  background-color: #4caf50;
}

.confidence-medium {
  background-color: #ff9800;
}

.confidence-low {
  background-color: #f44336;
}

.email-content,
.documents-content {
  display: none;
}

.email-content.active,
.documents-content.active {
  display: block;
}

.email-header {
  margin-bottom: var(--space-16);
}

.email-header h4 {
  font-size: var(--font-size-md);
  margin: 0 0 var(--space-8) 0;
}

.email-meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.email-body {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  white-space: pre-wrap;
}

.highlight {
  background: rgba(255, 204, 0, 0.3);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background var(--duration-fast) var(--ease-standard);
}

.highlight:hover {
  background: rgba(255, 204, 0, 0.5);
}

.highlight.active {
  background: rgba(33, 128, 141, 0.3);
}

.document-item {
  padding: var(--space-12);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  margin-bottom: var(--space-12);
}

.document-item h5 {
  margin: 0 0 var(--space-8) 0;
  font-size: var(--font-size-md);
}

.document-type {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-8) 0;
}

.document-preview {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Analysis Panel */
.analysis-panel {
  flex: 1;
}

.decision-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
  padding: var(--space-16);
}

.decision-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  padding: var(--space-16);
  transition: all var(--duration-normal) var(--ease-standard);
}

.decision-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.decision-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-12);
}

.decision-card .card-header h4 {
  font-size: var(--font-size-md);
  margin: 0;
}

.decision-indicator {
  display: flex;
  align-items: center;
}

.decision-status {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  background: var(--color-secondary);
  color: var(--color-text);
}

.decision-status.covered {
  background: rgba(var(--color-success-rgb), 0.15);
  color: var(--color-success);
}

.decision-status.not-covered {
  background: rgba(var(--color-error-rgb), 0.15);
  color: var(--color-error);
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.confidence-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
}

.mini-confidence-meter {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.mini-confidence-bar {
  width: 60px;
  height: 4px;
  background: var(--color-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.mini-confidence-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--color-warning) 0%, var(--color-success) 100%);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
}

.decision-details {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.fault-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.fault-party {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-8);
  background: var(--color-secondary);
  border-radius: var(--radius-sm);
}

.fault-party-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.fault-percentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.quantum-amount {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.quantum-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.quantum-item {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-sm);
}

.quantum-item.total {
  font-weight: var(--font-weight-bold);
  border-top: 1px solid var(--color-border);
  padding-top: var(--space-6);
}

.risk-factors {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.risk-factor {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  font-size: var(--font-size-sm);
}

.risk-factor-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-warning);
}

.review-timeline {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.expand-btn {
  margin-top: auto;
}

/* Expanded Details Styles */
.expanded-details {
  margin-top: var(--space-16);
  padding: var(--space-16);
  background: var(--color-background);
  border-radius: var(--radius-base);
  border: 1px solid var(--color-card-border-inner);
  transition: all var(--duration-normal) var(--ease-standard);
}

.expanded-detail-group {
  margin-bottom: var(--space-16);
}

.expanded-detail-group:last-child {
  margin-bottom: 0;
}

.expanded-detail-group h5 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-12) 0;
}

.expanded-detail-group h6 {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  margin: var(--space-12) 0 var(--space-8) 0;
}

.expanded-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--space-6) 0;
  border-bottom: 1px solid var(--color-card-border-inner);
  font-size: var(--font-size-sm);
}

.expanded-detail-item:last-child {
  border-bottom: none;
}

.expanded-detail-item .label {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  flex-shrink: 0;
  margin-right: var(--space-12);
}

.expanded-detail-item .value {
  color: var(--color-text);
  text-align: right;
  word-break: break-word;
}

.fault-party-expanded {
  margin-bottom: var(--space-8);
  padding: var(--space-8);
  background: var(--color-secondary);
  border-radius: var(--radius-sm);
}

.party-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.party-type {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-transform: capitalize;
}

.party-percentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.party-factors {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6) 0;
  border-bottom: 1px solid var(--color-card-border-inner);
  font-size: var(--font-size-sm);
}

.breakdown-item:last-child {
  border-bottom: none;
}

.breakdown-item.total {
  font-weight: var(--font-weight-bold);
  border-top: 1px solid var(--color-border);
  padding-top: var(--space-8);
}

.risk-factors-expanded ul,
.risk-recommendations ul {
  margin: 0;
  padding-left: var(--space-16);
  font-size: var(--font-size-sm);
}

.risk-factors-expanded li,
.risk-recommendations li {
  margin-bottom: var(--space-4);
  color: var(--color-text-secondary);
}

/* Details Panel */
.details-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-16);
}

.analysis-sections {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.analysis-section {
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-base);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-12) var(--space-16);
  background: var(--color-secondary);
  cursor: pointer;
  transition: background var(--duration-fast) var(--ease-standard);
}

.section-header:hover {
  background: var(--color-secondary-hover);
}

.section-header h4 {
  font-size: var(--font-size-sm);
  margin: 0;
}

.collapse-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: var(--space-2);
}

.collapse-icon {
  transition: transform var(--duration-fast) var(--ease-standard);
}

.collapse-btn.collapsed .collapse-icon {
  transform: rotate(-90deg);
}

.section-content {
  padding: var(--space-16);
}

.section-content.collapsed {
  display: none;
}

.detail-group {
  margin-bottom: var(--space-16);
}

.detail-group h5 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--space-6) 0;
  border-bottom: 1px solid var(--color-card-border-inner);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item .label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  flex-shrink: 0;
  margin-right: var(--space-12);
}

.detail-item .value {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  text-align: right;
  word-break: break-word;
}

.fault-parties {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.fault-party-detail {
  padding: var(--space-8);
  background: var(--color-secondary);
  border-radius: var(--radius-sm);
}

.fault-party-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.fault-party-type {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-transform: capitalize;
}

.fault-party-percentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.fault-factors {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.quantum-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.quantum-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6) 0;
  border-bottom: 1px solid var(--color-card-border-inner);
}

.quantum-detail-item:last-child {
  border-bottom: none;
  font-weight: var(--font-weight-bold);
}

.quantum-detail-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.quantum-detail-value {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
}

/* Loading and Placeholders */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--color-background), 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--color-secondary);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-16) auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-placeholder {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-style: italic;
  text-align: center;
  padding: var(--space-16);
}

/* Tooltip */
.tooltip {
  position: absolute;
  z-index: 1000;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-lg);
  padding: var(--space-12);
  max-width: 300px;
  pointer-events: none;
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
}

.tooltip-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.tooltip-confidence {
  font-size: var(--font-size-xs);
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
}

.tooltip-body {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-container {
    grid-template-columns: 300px 1fr 280px;
  }
}

@media (max-width: 1024px) {
  .dashboard-container {
    grid-template-columns: 1fr;
    gap: var(--space-16);
  }
  
  .document-panel,
  .details-panel {
    max-height: 400px;
  }
  
  .decision-cards {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--space-16);
  }
  
  .claim-status-section {
    flex-direction: row;
    gap: var(--space-24);
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--space-8);
  }
  
  .decision-cards {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    padding: var(--space-12);
  }
  
  .panel-header {
    padding: var(--space-12);
  }
  
  .document-viewer,
  .details-content {
    padding: var(--space-12);
  }
  
  .quick-actions {
    flex-wrap: wrap;
    gap: var(--space-6);
  }
  
  .confidence-bar {
    width: 80px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .decision-card {
    border-width: 2px;
  }
  
  .highlight {
    outline: 2px solid var(--color-primary);
  }
  
  .confidence-progress,
  .mini-confidence-progress {
    background: var(--color-primary);
  }
}

/* Document Display and Navigation Enhancements */
.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-12);
}

.document-type {
  background: var(--color-secondary);
  color: var(--color-text-secondary);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.confidence-badge {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.document-content {
  margin-bottom: var(--space-16);
}

.document-preview {
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-12);
}

.document-full-text {
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  padding: var(--space-16);
  background: var(--color-surface);
  max-height: 400px;
  overflow-y: auto;
}

.ocr-content {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  white-space: pre-wrap;
  word-break: break-word;
}

.document-actions {
  display: flex;
  gap: var(--space-8);
  padding-top: var(--space-8);
  border-top: 1px solid var(--color-border);
}



/* Analysis Field Clickability */
.analysis-field-clickable {
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  border-radius: var(--radius-sm);
  padding: var(--space-4);
  margin: calc(var(--space-4) * -1);
}

.analysis-field-clickable:hover {
  background: var(--color-secondary);
}

/* Progress Indicators removed per user request */

/* NER Entity Highlighting Styles */
.ner-entity {
  position: relative;
  display: inline-block;
  margin: 1px 2px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9em;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.ner-entity:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

.ner-label {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.65em;
  font-weight: bold;
  padding: 2px 4px;
  border-radius: 2px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 1000;
}

.ner-entity:hover .ner-label {
  opacity: 1;
}

/* Entity type specific styles */
.ner-entity[data-entity-type="PERSON"] {
  background-color: #4CAF50 !important;
  border-left: 3px solid #2E7D32;
}

.ner-entity[data-entity-type="ORG"],
.ner-entity[data-entity-type="ORGANIZATION"] {
  background-color: #2196F3 !important;
  border-left: 3px solid #1565C0;
}

.ner-entity[data-entity-type="DATE"],
.ner-entity[data-entity-type="TIME"] {
  background-color: #FF9800 !important;
  border-left: 3px solid #E65100;
}

.ner-entity[data-entity-type="MONEY"] {
  background-color: #9C27B0 !important;
  border-left: 3px solid #6A1B9A;
}

.ner-entity[data-entity-type="LOCATION"],
.ner-entity[data-entity-type="GPE"] {
  background-color: #00BCD4 !important;
  border-left: 3px solid #00838F;
}

.ner-entity[data-entity-type="POLICY"] {
  background-color: #795548 !important;
  border-left: 3px solid #4E342E;
}

.ner-entity[data-entity-type="CLAIM"] {
  background-color: #E91E63 !important;
  border-left: 3px solid #AD1457;
}

.ner-entity[data-entity-type="INCIDENT"] {
  background-color: #FF5722 !important;
  border-left: 3px solid #D84315;
}

.ner-entity[data-entity-type="MISC"] {
  background-color: #607D8B !important;
  border-left: 3px solid #37474F;
}

/* Confidence indicator */
.ner-entity[data-confidence]:after {
  content: attr(data-confidence);
  position: absolute;
  top: -8px;
  right: -8px;
  background: rgba(0,0,0,0.7);
  color: white;
  font-size: 0.6em;
  padding: 1px 3px;
  border-radius: 50%;
  min-width: 12px;
  text-align: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.ner-entity:hover[data-confidence]:after {
  opacity: 1;
}

/* Search Highlighting Styles */
.search-highlight {
  background-color: yellow !important;
  color: black !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-weight: bold !important;
  box-shadow: 0 0 5px rgba(255, 255, 0, 0.5) !important;
  animation: pulse 1s ease-in-out 3 !important;
}

/* Click-to-search styles */
.value, .field-value, .analysis-value, .claim-detail-value, .policy-detail-value,
#incidentDate, #incidentLocation, #claimant, #policyNumber, #policyHolder, #coverageLimit {
  cursor: pointer !important;
  text-decoration: underline !important;
  color: #2196F3 !important;
  transition: all 0.2s ease !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
}

.value:hover, .field-value:hover, .analysis-value:hover, .claim-detail-value:hover, .policy-detail-value:hover,
#incidentDate:hover, #incidentLocation:hover, #claimant:hover, #policyNumber:hover, #policyHolder:hover, #coverageLimit:hover {
  background-color: rgba(33, 150, 243, 0.1) !important;
  transform: scale(1.02) !important;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2) !important;
}

/* Search animations */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(255, 255, 0, 0.5); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 0, 0.8); }
  100% { box-shadow: 0 0 5px rgba(255, 255, 0, 0.5); }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}



/* Real-time update animations */
.analysis-completed {
  animation: analysis-success 3s ease-in-out;
  border: 2px solid var(--color-success) !important;
}

.analysis-updated {
  animation: analysis-update 2s ease-in-out;
  border: 2px solid var(--color-primary) !important;
}

@keyframes analysis-success {
  0% { 
    background-color: transparent;
    box-shadow: none;
  }
  25% { 
    background-color: rgba(var(--color-success-rgb), 0.1);
    box-shadow: 0 0 20px rgba(var(--color-success-rgb), 0.3);
  }
  50% { 
    background-color: rgba(var(--color-success-rgb), 0.2);
    box-shadow: 0 0 30px rgba(var(--color-success-rgb), 0.4);
  }
  100% { 
    background-color: transparent;
    box-shadow: none;
  }
}

@keyframes analysis-update {
  0% { 
    background-color: transparent;
    transform: scale(1);
  }
  50% { 
    background-color: rgba(var(--color-primary-rgb), 0.1);
    transform: scale(1.02);
  }
  100% { 
    background-color: transparent;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.95);
  }
}

/* Highlight pulse animation */
.highlight-pulse {
  animation: highlight-pulse 1.5s ease-in-out;
}

@keyframes highlight-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(var(--color-primary-rgb), 0);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.3);
  }
}

/* Enhanced Document Display */
.document-title {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
}

.document-icon {
  font-size: var(--font-size-lg);
}

.document-meta {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  flex-wrap: wrap;
}

.file-size {
  background: var(--color-neutral-100);
  color: var(--color-text-secondary);
  padding: var(--space-2) var(--space-6);
  border-radius: var(--radius-xs);
  font-size: var(--font-size-xs);
}

.processing-status {
  padding: var(--space-2) var(--space-6);
  border-radius: var(--radius-xs);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.status-completed {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.status-processing {
  background: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.status-failed {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.document-content-wrapper {
  margin-bottom: var(--space-12);
}

.document-content {
  background: var(--color-surface);
  padding: var(--space-16);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 600px;
  overflow-y: auto;
}

/* Enhanced Highlighting Styles */
.highlight {
  padding: 2px 4px;
  border-radius: 3px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  margin: 0 1px;
  display: inline;
}

.highlight:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 5;
}

.highlight.active {
  box-shadow: 0 0 0 2px var(--color-primary);
  z-index: 10;
}

/* Entity Type Colors */
.entity-date { background-color: #ffeb3b; color: #000; }
.entity-person { background-color: #4caf50; color: #fff; }
.entity-policy_number { background-color: #2196f3; color: #fff; }
.entity-medical_cost { background-color: #ff9800; color: #fff; }
.entity-location { background-color: #9c27b0; color: #fff; }
.entity-currency { background-color: #4caf50; color: #fff; }
.entity-coveragedecision { background-color: #4caf50; color: #fff; }
.entity-medical_expense { background-color: #ff9800; color: #fff; }
.entity-fault_percentage { background-color: #f44336; color: #fff; }
.entity-settlement_amount { background-color: #4caf50; color: #fff; }
.entity-incident_date { background-color: #ffeb3b; color: #000; }
.entity-incident_location { background-color: #9c27b0; color: #fff; }
.entity-claimant_name { background-color: #4caf50; color: #fff; }
.entity-policy_holder { background-color: #2196f3; color: #fff; }

/* Confidence Levels */
.high-confidence {
  border: 2px solid rgba(76, 175, 80, 0.3);
}

.medium-confidence {
  border: 2px solid rgba(255, 193, 7, 0.3);
}

.low-confidence {
  border: 2px solid rgba(244, 67, 54, 0.3);
}

/* Tooltip Enhancements */
.tooltip {
  position: absolute;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--space-12);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-width: 300px;
  font-size: var(--font-size-sm);
  display: none;
}

.tooltip-detail {
  margin-bottom: var(--space-4);
}

.tooltip-action {
  margin-top: var(--space-8);
  padding-top: var(--space-8);
  border-top: 1px solid var(--color-border);
  font-style: italic;
  color: var(--color-text-secondary);
}

/* Field Navigation Enhancements */
.analysis-field-clickable {
  cursor: pointer;
  transition: background-color 0.2s;
  padding: var(--space-4);
  border-radius: var(--radius-sm);
  margin: -2px;
}

.analysis-field-clickable:hover {
  background-color: var(--color-hover);
}

.analysis-field-highlighted {
  background-color: var(--color-accent-light);
  border: 2px solid var(--color-accent);
  border-radius: var(--radius-sm);
}

.field-highlight-flash {
  animation: fieldFlash 2s ease-in-out;
}

@keyframes fieldFlash {
  0%, 100% { background-color: transparent; }
  25% { background-color: rgba(59, 130, 246, 0.2); }
  75% { background-color: rgba(59, 130, 246, 0.1); }
}



/* Enhanced Document Structure Styles */
.document-section {
  margin-bottom: 24px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--color-surface);
}

.document-header {
  background: var(--color-secondary);
  padding: var(--space-12) var(--space-16);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.email-section .document-header {
  background: rgba(var(--color-primary-rgb), 0.1);
}

.ocr-section .document-header {
  background: rgba(var(--color-info-rgb), 0.1);
}

.document-meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.document-type {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  font-size: var(--font-size-sm);
}

.file-name {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  font-size: var(--font-size-sm);
}

.document-status {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  padding: 2px 8px;
  background: var(--color-success);
  border-radius: var(--radius-sm);
  color: white;
}

.ocr-confidence {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  padding: 2px 8px;
  background: var(--color-info);
  border-radius: var(--radius-sm);
  color: white;
}

.email-meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.email-subject {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--space-12) 0;
  color: var(--color-text);
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-border);
}

.email-body, .ocr-content {
  padding: var(--space-16);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  white-space: pre-wrap;
  word-wrap: break-word;
  background: var(--color-surface);
  max-height: 400px;
  overflow-y: auto;
}

/* Entity Highlighting Styles */
.entity-highlight {
  background-color: rgba(255, 193, 7, 0.3);
  border-radius: 3px;
  padding: 1px 2px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.entity-highlight:hover {
  background-color: rgba(255, 193, 7, 0.6);
  transform: scale(1.02);
}

.entity-highlight.entity-person { 
  background-color: rgba(76, 175, 80, 0.3); 
}

.entity-highlight.entity-date { 
  background-color: rgba(255, 235, 59, 0.3); 
}

.entity-highlight.entity-currency { 
  background-color: rgba(76, 175, 80, 0.3); 
}

.entity-highlight.entity-location { 
  background-color: rgba(156, 39, 176, 0.3); 
}

/* Panel Highlighting */
.analysis-field-highlighted {
  background-color: rgba(var(--color-primary-rgb), 0.1) !important;
  border: 2px solid var(--color-primary) !important;
  border-radius: var(--radius-sm);
  animation: highlight-pulse 1s ease-in-out;
}

/* Flash Animation for Document Highlights */
.highlight-flash {
  animation: highlight-flash 2s ease-in-out;
}

@keyframes highlight-flash {
  0%, 100% { 
    background-color: rgba(255, 193, 7, 0.3);
    transform: scale(1);
  }
  50% { 
    background-color: rgba(255, 193, 7, 0.8);
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
  }
}

@keyframes highlight-pulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 0 0 rgba(var(--color-primary-rgb), 0);
  }
  50% { 
    transform: scale(1.02);
    box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.3);
  }
}



/* Enhanced OCR Document Rendering Styles */
.ocr-table-container {
  margin: var(--space-16) 0;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--color-surface);
}

.ocr-table-header {
  background: var(--color-secondary);
  padding: var(--space-8) var(--space-12);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  border-bottom: 1px solid var(--color-border);
  font-size: var(--font-size-sm);
}

.ocr-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-tight);
}

.ocr-table th,
.ocr-table td {
  padding: var(--space-6) var(--space-8);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  vertical-align: top;
  word-wrap: break-word;
  max-width: 200px;
}

.ocr-table th {
  background: var(--color-secondary);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-header-row {
  background: var(--color-secondary);
}

.ocr-table tr:hover {
  background: rgba(var(--color-primary-rgb), 0.05);
}

.ocr-narrative-container {
  margin: var(--space-16) 0;
  padding: var(--space-16);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
}

.ocr-line {
  margin: var(--space-2) 0;
  padding: var(--space-2) 0;
  border-bottom: 1px solid rgba(var(--color-border-rgb), 0.1);
}

.ocr-line:last-child {
  border-bottom: none;
}

.ocr-line-header {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  background: rgba(var(--color-primary-rgb), 0.1);
  padding: var(--space-4) var(--space-8);
  margin: var(--space-8) 0;
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--color-primary);
}

.ocr-line-spacer {
  height: var(--space-8);
}

.section-separator {
  margin: var(--space-24) 0;
  border: none;
  border-top: 2px solid var(--color-border);
  opacity: 0.5;
}

/* Entity highlighting in tables and narrative */
.ocr-table .entity-highlight,
.ocr-narrative-container .entity-highlight {
  background-color: rgba(255, 193, 7, 0.3);
  border-radius: 3px;
  padding: 1px 2px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ocr-table .entity-highlight:hover,
.ocr-narrative-container .entity-highlight:hover {
  background-color: rgba(255, 193, 7, 0.6);
  transform: scale(1.02);
}

.ocr-table .entity-highlight.entity-person,
.ocr-narrative-container .entity-highlight.entity-person {
  background-color: rgba(76, 175, 80, 0.3);
}

.ocr-table .entity-highlight.entity-date,
.ocr-narrative-container .entity-highlight.entity-date {
  background-color: rgba(255, 235, 59, 0.3);
}

.ocr-table .entity-highlight.entity-currency,
.ocr-narrative-container .entity-highlight.entity-currency {
  background-color: rgba(76, 175, 80, 0.3);
}

.ocr-table .entity-highlight.entity-location,
.ocr-narrative-container .entity-highlight.entity-location {
  background-color: rgba(156, 39, 176, 0.3);
}

/* Flash animation for document highlights */
.highlight-flash {
  animation: highlight-flash 2s ease-in-out;
}

@keyframes highlight-flash {
  0%, 100% { 
    background-color: rgba(255, 193, 7, 0.3);
    transform: scale(1);
  }
  50% { 
    background-color: rgba(255, 193, 7, 0.8);
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
  }
}

/* Responsive table */
@media (max-width: 768px) {
  .ocr-table {
    font-size: 10px;
  }
  
  .ocr-table th,
  .ocr-table td {
    padding: var(--space-4) var(--space-6);
    max-width: 120px;
  }
  
  .ocr-narrative-container {
    font-size: var(--font-size-xs);
    padding: var(--space-12);
  }
}

/* Tab content visibility */
.email-content,
.documents-content {
  display: none;
}

.email-content.active,
.documents-content.active {
  display: block;
}

.no-documents {
  text-align: center;
  padding: var(--space-32);
  color: var(--color-text-secondary);
  font-style: italic;
}

/* Enhanced Document Display Styles */
.document-header-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: var(--space-12) 0 var(--space-6) 0;
  padding: var(--space-6) 0;
  border-bottom: 2px solid var(--color-border);
}

.document-type {
  background: var(--color-secondary);
  color: var(--color-text);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sender-info {
  font-style: italic;
  color: var(--color-text-secondary);
}

.confidence-badge {
  background: var(--color-success);
  color: var(--color-btn-primary-text);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
}

.processing-status {
  background: var(--color-info);
  color: var(--color-btn-primary-text);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
}

.email-body-content {
  background: var(--color-secondary);
  padding: var(--space-12);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--color-primary);
  line-height: 1.6;
  white-space: pre-wrap;
  margin: var(--space-8) 0;
}

/* Temporary highlight for search results */
.temp-highlight {
  background-color: #ff0 !important;
  animation: pulse 2s ease-in-out;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Analysis field highlighting */
.analysis-field-highlighted {
  background-color: rgba(var(--color-primary-rgb), 0.1) !important;
  border: 2px solid var(--color-primary) !important;
  border-radius: var(--radius-sm);
  animation: highlight-pulse 1s ease-in-out;
}

/* Notification animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}