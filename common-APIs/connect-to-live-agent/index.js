const { ApiResp } = require('./helpers/api-helper.js')
const {
  validatePhoneNumber,
} = require('./helpers/pinpoint-helper.js')
const {
  startOutboundVoiceContact,
} = require('./helpers/connect-helper.js')
const {
  putDynamodbTableItem
} = require('./helpers/dynamodb-helper.js')

const { CONNECT_INSTANCE_ID, OUTBOUND_CONTACT_FLOW_ID, SOURCE_PHONE_NUMBER, TABLE_NAME} = process.env

const CustomError = class extends Error {
  constructor(name, message) {
    super(message)
    this.name = name
  }
}

const handler = async (event) => {
  console.log(JSON.stringify(event))
  try {
    const body = JSON.parse(event.body)
    console.log("event body", body)
    if (!body.toPhoneNumber) {
      throw new CustomError('MissingParameters', 'MissingParameters')
    }
    const result= await putDynamodbTableItem(TABLE_NAME, body)
    console.log("contact attributes stored successfully", result)
    if(result){
      await validatePhoneNumber(body.toPhoneNumber)
    }else{
      throw new CustomError('error', 'while storing contact attributes')
    }
    const makeOutboundCallResult = await startOutboundVoiceContact(CONNECT_INSTANCE_ID, body.toPhoneNumber, SOURCE_PHONE_NUMBER, OUTBOUND_CONTACT_FLOW_ID)
    return ApiResp({ success: true, data: { ContactId: makeOutboundCallResult.ContactId, error: null } })
  } catch (err) {
    console.error(err)
    return ApiResp({ success: true, data: { error: err?.name ?? 'generic_error' } })
  }
}

module.exports = {
  handler
}