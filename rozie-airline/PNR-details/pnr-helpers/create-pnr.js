const { ApiResp } = require("../helpers/api-helper.js")
const { putDynamodbTableItem, retrieveDynamoEntry, updateDynamoTableDataWithParams } = require("../helpers/dynamo-helper.js")

/**
 * @function createPNR
 * @description This function creates a PNR entry in a DynamoDB table based on the provided data.
 * 
 * @param {Object} body - The request body containing customer and booking details.
 * @param {string} body.customer_id - The customer ID for the PNR.
 * @param {string} body.flight_schedule_id - The flight schedule ID.
 * @param {number} body.payment_amount - The payment amount.
 * @param {Array} body.travelers_details - The list of travelers details, including their names and seat numbers.
 * 
 * @returns {Promise<Object>} - The response object indicating the result of the PNR creation.
 * 
 * @throws {Error} - Throws an error if there is an issue during the PNR creation process.
 */
const createPNR = async (body) => {
	try {
		console.log("function: createPNR body:", body)
		const { customer_id, flight_schedule_id, payment_amount, travelers_details } = body

		const validationError = await validatePNRInputs(customer_id, flight_schedule_id, travelers_details)
		if (validationError) {
			return ApiResp({ status: "Error", message: validationError }, 200)
		}

		const timestamp = Date.now().toString(36)
		const pnr_id = timestamp.slice(-5)
		const pnr_status = "confirmed"
		const payment_status = "completed"
		const created_date = new Date().toISOString()

		const pnrData = {
			pnr_id,
			customer_id,
			flight_schedule_id,
			pnr_status,
			payment_status,
			payment_amount,
			created_date,
			travelers_details
		}

		const response = await putDynamodbTableItem(process.env.PNR_TABLE_NAME, pnrData)

		console.log("function: createPNR response:", response)

		if (response) {
			const responseBody = {
				pnr_id: pnr_id,
				pnr_status: pnr_status,
				payment_status: payment_status,
				payment_amount: payment_amount,
				travelers_details: travelers_details
			}
			return ApiResp({ status: "Success", message: "PNR created successfully", response: responseBody }, 200)
		} else {
			return ApiResp({ status: "Error", message: "Internal server error while creating PNR" }, 500)
		}
	} catch (error) {
		console.error("Error in createPNR:", error)
		return ApiResp({ status: "Error", message: "Internal server error while creating PNR" }, 500)
	}
}


const validatePNRInputs = async (customer_id, flight_schedule_id, travelers_details) => {
	if (!customer_id || !flight_schedule_id) {
		return "Customer ID or Flight schedule ID is missing"
	}

	const flight_schedule_data = await retrieveDynamoEntry(process.env.FLIGHT_SCHEDULE_TABLE, "scheduleId", flight_schedule_id)

	if (!flight_schedule_data || flight_schedule_data.flightStatus != "scheduled") {
		return "Flight schedule is not available"
	}

	if (!travelers_details || travelers_details.length === 0) {
		return "Travelers details are missing or invalid"
	}

	for (const traveler of travelers_details) {
		const { first_name, seat_number } = traveler

		if (!first_name || !seat_number) {
			return "Missing traveler first name or seat number"
		}

		const isSeatAvailable = await checkAndBookSeat(seat_number, flight_schedule_id, flight_schedule_data)
		if (!isSeatAvailable) {
			return `Seat number ${seat_number} is not available`
		}
	}

	return null
}

async function checkAndBookSeat(seat_number, flight_schedule_id, flight_schedule_data) {

	const row = seat_number.slice(0, -1)
	const seat = seat_number.slice(-1)

	const seat_data = flight_schedule_data?.seating[row][seat]

	console.log("seat_data:", seat_data)

	if (!seat_data && seat_data?.status !== "available") {
		return false
	}

	flight_schedule_data.seating[row][seat].status = "booked"

	const updateParams = {
		TableName: process.env.FLIGHT_SCHEDULE_TABLE,
		Key: { scheduleId: flight_schedule_id },
		UpdateExpression: "set seating = :seating",
		ExpressionAttributeValues: {
			":seating": flight_schedule_data.seating
		},
		ReturnValues: "UPDATED_NEW"
	}

	console.log("updateParams:", updateParams)

	try {
		await updateDynamoTableDataWithParams(updateParams)
	} catch (error) {
		console.error("Error updating seat status:", error)
		throw new Error("Failed to update seat status in the database.")
	}

	return true
}

module.exports = {
	createPNR
}