import requests
import json
from copy import deepcopy

data_map = {}
service_map = {}
stylist_map = {}
branch_map = {}
category_map = {}
service_package_map = {}

excluded_service_list = ["Calligraphy Cut Black ", "Calligraphy Cut Gold", "Calligraphy Cut Silver", "[scalp treatment] cut"]

def get_combined_value(record, key_string):
    """
    Retrieve combined value from a dictionary based on dot-separated key string.
    """
    values = []
    for key in key_string.split("+"):
        values.append(record.get(key, "").strip())
    return " ".join(values)


def clear_name(name):
    branch_name = {
        "Vanderbilt": "Prospect Heights",
        "Myrtle": "Clinton Hill - Myrtle",
        "Fulton": "Clinton Hill - Fulton",
        "Dekalb": "Fort Greene"
    }
    name = (
        name.replace("salon", "")
        .replace("718", "")
        .replace("[", "")
        .replace("]", "")
        .replace("Salon", "")
        .strip()
    )
    return branch_name.get(name, name)


def get_nested_value(data, key_string):
    """
    Retrieve nested value from a dictionary based on dot-separated key string.
    """
    if key_string:
        keys = key_string.split(".")
        value = data
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return None
    return data


# def get_nested_value(data, key):
#     """
#     Helper function to extract nested value from JSON response.
#     """
#     keys = key.split(".")
#     for k in keys:
#         data = data.get(k, {})
#     return data


def sync_get_request(url, query_params=None, key=None, page=0, size=100):
    """
    This function is used for invoking the GET method and waiting for the result.
    Handles pagination to retrieve all records.
    """
    base_url = "https://platform-us.phorest.com/third-party-api-server/api/business/Wj6YQfGEsvSqkLskUdhu3g/"
    param = {
        "url": f"{base_url}{url}",
        "timeout": 30,
        "auth": ("global/<EMAIL>", "z9pha0hAWre6ar1*espu"),
    }

    if query_params is None:
        query_params = {}

    query_params["page"] = page
    query_params["size"] = size
    all_results = []
    try:
        while True:
            param["params"] = query_params
            response = requests.get(**param)

            if response.status_code != 200:
                return "Failed", {}

            response_json = response.json()
            results = get_nested_value(response_json, key)
            if not results:
                break
            if isinstance(results, dict):
                all_results.append(results)
            else:
                all_results.extend(results)
            if len(results) < size:
                break
            query_params["page"] += 1  # Move to the next page
        return "Success", all_results

    except Exception as e:
        return "Failed", str(e)

def process_category(category_list):
    modified_category_dict = {}
    for category in category_list:
        modified_category_dict[category["categoryId"]] = category["name"]
    return modified_category_dict


def process_staff_results(staffs):
    processed_staff = {}
    for staff in staffs:
        if staff["hideFromOnlineBookings"]:
            continue
        staff_data = {
            "name": get_combined_value(staff, "firstName+lastName"),
            "staffCategoryName": staff["staffCategoryName"].strip(),
            "staffCategoryId": staff["staffCategoryId"].strip(),
            "aboutMe": staff.get("onlineProfile", "").strip(),
            "staffId": staff["staffId"].strip(),
        }
        processed_staff[staff["staffId"]] = staff_data
    return processed_staff


def return_staff_dict_price(processed_staff, disqualified_staff_list, service):
    qualified_staff_list = list(
        set(processed_staff.keys()) - set(disqualified_staff_list)
    )
    qualified_staff_dict = {}
    for staff_id in qualified_staff_list:
        copy_processed_staff = deepcopy(processed_staff[staff_id])
        qualified_staff_dict[copy_processed_staff["name"]] = { "name": copy_processed_staff["name"]}
        qualified_staff_dict[copy_processed_staff["name"]]["price"] = service["price"]
        if service.get("staffCategories"):
            prices = [
                item
                for item in service.get("staffCategories")["prices"]
                if item["id"] == copy_processed_staff["staffCategoryId"]
            ]
            if prices:
                qualified_staff_dict[copy_processed_staff["name"]]["price"] = prices[0][
                    "price"
                ]
    return qualified_staff_dict

def return_staff_dict(processed_staff, disqualified_staff_list, service):
    qualified_staff_list = list(
        set(processed_staff.keys()) - set(disqualified_staff_list)
    )
    qualified_staff_dict = {}
    for staff_id in qualified_staff_list:
        qualified_staff_dict[processed_staff[staff_id]["name"]] = {}
    return qualified_staff_dict


def daily_sync_up_salon718():
    status, branches = sync_get_request("branch", None, "_embedded.branches")
    for branch in branches:
        address = "streetAddress1+streetAddress2+city+postalCode"
        _branch = {
            "name": clear_name(branch["name"]),
            "branchId": branch["branchId"],
            "address": get_combined_value(branch, address),
        }
        branch_map[branch["branchId"]] = _branch

        data_map[clear_name(branch["name"])] = {}
        status, services = sync_get_request(
            f"branch/{branch['branchId']}/service", None, "_embedded.services"
        )
        service_map[branch["branchId"]] = services
        status, staffs = sync_get_request(
            f"branch/{branch['branchId']}/staff", None, "_embedded.staffs"
        )
        stylist_map[branch["branchId"]] = staffs
        processed_staff = process_staff_results(staffs)

        status, category = sync_get_request(
            f"branch/{branch['branchId']}/service-category", None, "_embedded.serviceCategories"
        )
        category_map[branch["branchId"]] = process_category(category)
        status, package = sync_get_request(
            f"branch/{branch['branchId']}/package", None, "_embedded.serviceGroups"
        )
        service_package_map[branch["branchId"]] = package

        data_map[clear_name(branch["name"])]["services"] = {}
        for service in services:
            if service["name"] in excluded_service_list:
                continue
            if service["internetEnabled"]:
                data_map[clear_name(branch["name"])]["services"][service["name"]] = {}
                data_map[clear_name(branch["name"])]["services"][service["name"]][
                    "stylists"
                ] = return_staff_dict(processed_staff, service["disqualifiedStaff"], service)

    with open("middleware_app/data/branch_map.json", "w") as f:
        json.dump({"branches": branch_map}, f, indent=4)

    stylist_map_data = {}
    for branch, _ in stylist_map.items():
        stylist_map_data[branch]={}
        for staff in stylist_map[branch]:
            if staff["hideFromOnlineBookings"]:
                continue
            staff_data = {
                "name": get_combined_value(staff, "firstName+lastName"),
                "staffCategoryName": staff["staffCategoryName"].strip(),
                "staffCategoryId": staff["staffCategoryId"].strip(),
                "aboutMe": staff.get("onlineProfile", "").strip(),
                "staffId": staff["staffId"].strip(),
            }
            stylist_map_data[branch][staff_data['name']] = staff_data

    with open("middleware_app/data/stylist_map.json", "w") as f:
        json.dump(stylist_map_data, f, indent=4)


    service_map_data = {}
    for branch, _ in service_map.items():
        processed_staff = process_staff_results(stylist_map[branch])
        service_map_data[branch] = []
        for service in service_map[branch]:
            if service["name"] in excluded_service_list:
                continue
            if not service["internetEnabled"]:
                continue
            service_data = {
                "name": service["name"],
                "serviceId": service["serviceId"],
                "duration": service["duration"],
                "prices": return_staff_dict_price(processed_staff, service["disqualifiedStaff"], service),
                "description": service.get("internetDescription", ""),
                "categoryName": service.get("categoryName", "")
            }
            service_map_data[branch].append(service_data)


    package_map_data = {}
    for branch, _ in service_map.items():
        category_dict = category_map[branch]
        package_map_data[branch] = []
        package_service_check_list = []
        for package in service_package_map[branch]:
            if package.get("availableOnline"):
                package_data = {
                    "packageName": package["serviceGroupName"],
                    "packageId": package["serviceGroupId"],
                    "categoryName": category_dict.get(package["categoryId"]),
                    "description": package.get("serviceGroupDescription", ""),
                    "services": []
                }
                for package_service in package.get("serviceGroupItems"):
                    processed_staff = process_staff_results(stylist_map[branch])
                    package_service = package_service.get("serviceGroupItemOptions")[0]
                    service_id = package_service.get("serviceId")
                    _, service = sync_get_request(
                        f"branch/{branch}/service/{service_id}", None
                    )
                    service_data = {
                        "name": service[0]["name"],
                        "serviceId": service[0]["serviceId"],
                        "duration": service[0]["duration"],
                        "package": "true",
                        "prices": return_staff_dict_price(processed_staff, service[0]["disqualifiedStaff"], service[0]),
                        "description": service[0].get("internetDescription", "")
                    }
                    if service[0]["name"] not in package_service_check_list:
                        branch_name = branch_map[branch]["name"]
                        data_map[branch_name]["services"][service[0]["name"]] = {}
                        data_map[branch_name]["services"][service[0]["name"]][
                            "stylists"
                        ] = return_staff_dict(processed_staff, service[0]["disqualifiedStaff"], service[0])
                        service_map_data[branch].append(service_data)
                        package_service_check_list.append(service[0]["name"])
                    package_data["services"].append(service[0]["name"])
                package_map_data[branch].append(package_data)

    with open("middleware_app/data/possible_values_map.json", "w") as f:
        json.dump({"branches": data_map}, f, indent=4)

    with open("middleware_app/data/service_map.json", "w") as f:
        json.dump(service_map_data, f, indent=4)

    with open("middleware_app/data/package_map.json", "w") as f:
        json.dump(package_map_data, f, indent=4)
