const { DynamoDBClient } = require("@aws-sdk/client-dynamodb")
const { DynamoDBDocumentClient, GetCommand, PutCommand, QueryCommand, UpdateCommand, ScanCommand, DeleteCommand} = require("@aws-sdk/lib-dynamodb")
const client = new DynamoDBClient()
const docClient = DynamoDBDocumentClient.from(client)

/**
 * get the entry from DynamoDB table.
 *
 * @param {string} tableName - The name of the DynamoDB table to check.
 * @param {string} pKey - primaryKey to get.
 * @param {string} pValue - primaryKey value to get.
 * @returns {Promise<object>} - A promise that resolves with the item if it exists else empty.
 */
const retrieveDynamoEntry = async (tableName, pKey, pValue) => {
	console.log("Function retrieveDynamoEntry")
	try {
		const command = new GetCommand({
			TableName: tableName,
			Key: {
				[pKey]: pValue,
			}
		})
		console.log(`command => ${JSON.stringify(command)}`)
		const response = await docClient.send(command)
		console.log(`response => ${JSON.stringify(response.Item)}`)
		return response.Item
	} catch (e) {
		console.log("Error while retrieving Entry from table:", e)
		return {}
	}
}


/**
 * Put  items into a DynamoDB table.
 *
 * @param {string} tableName - The name of the DynamoDB table to retrieve items from.
 * @param {Object} Item - The item to put into the table.
 * @returns {Promise<Object>} - A promise that resolves with the response from the DynamoDB API.
 */
async function putDynamodbTableItem(tableName, Item) {
	const params = {
		TableName: tableName,
		Item: Item,
	}
	console.log("Params for putDynamodbTableItem()",params)
	try{
		const command = new PutCommand(params)
		const response = await docClient.send(command)
		console.log("response",response)
		return response
	}
	catch (e){
		console.log("error", e)
	}
	
}

async function queryDynamodbTableItem(tableName, primaryKey, callTimeStamp48) {
	const params = {
		TableName: tableName,
		KeyConditionExpression:
			"agentId = :agentId and callTimeStamp >= :callTimeStamp48",
		ScanIndexForward: false,
		ExpressionAttributeValues: {
			":agentId": primaryKey,
			":callTimeStamp48": callTimeStamp48
		},
	}
	console.log("Params for QueryCommand()",params)
	const command = new QueryCommand(params)
    
	const response = await docClient.send(command)
	console.log(response)
	return response["Items"]
}

/**
 * @function queryDynamodbTableItem
 * @description Executes a DynamoDB query with dynamic parameters.
 * 
 * @param {Object} params - The query parameters (table name, key conditions, etc.).
 * 
 * @returns {Promise<Array>} - The list of items returned from the query.
 * 
 * @throws {Error} - Throws an error if there is an issue with executing the query.
 */
async function queryDynamodbTableItemGSI(params) {
	try {
		console.log("Params for QueryCommand()", params)
		const command = new QueryCommand(params)
        
		const response = await docClient.send(command)
		console.log("Query response:", response)

		return response.Items || []
	} catch (error) {
		console.error("Error querying DynamoDB:", error)
		throw new Error("Error querying DynamoDB")
	}
}


/**
 * update items from a DynamoDB table.
 *
 * @param {string} params - params for retrieving items from DDB.
 * @returns {Promise<Array>} - A promise that resolves with an array of items.
 */
async function updateDynamoTableDataWithParams(params) {
	console.log("function: updateDynamoTableDataWithParams")
	try{
		const command = new UpdateCommand(params)
		console.log(`command => ${JSON.stringify(command)}`)
		const response = await docClient.send(command)
		return response
	}catch(e){
		console.log("This is the error caught in updateDynamoTableDataWithParams method :- ", e)
		return e
	}
	
	
}

async function queryDynamodbTableItemWithContactId(tableName, contactId) {
	const params = {
		TableName: tableName,
		IndexName: "contactId-index",
		KeyConditionExpression:
			"contactId = :contactId",
		ScanIndexForward: false,
		ExpressionAttributeValues: {
			":contactId": contactId,
		},
	}
	console.log("Params for QueryCommand()",params)
	const command = new QueryCommand(params)
    
	const response = await docClient.send(command)
	console.log(response)
	return response["Items"]
}

async function queryDynamodbTableItemWithParam(params) {
	let items = []
	let lastEvaluatedKey = undefined

	do {
		console.log("Params for QueryCommand()",params)
		const command = new QueryCommand({
			...params,
			ExclusiveStartKey: lastEvaluatedKey
		})

		console.log(`command => ${JSON.stringify(command)}`)

		const { Items, LastEvaluatedKey } = await docClient.send(command)

		console.log(`items => ${JSON.stringify(Items)}`)
        
		items.push(...Items)
		lastEvaluatedKey = LastEvaluatedKey

	} while (lastEvaluatedKey)

	return items
}
/**
 * Get all items from a DynamoDB table.
 *
 * @param {string} tableName - The name of the DynamoDB table to retrieve items from.
 * @returns {Promise<Array<Object>>} - A promise that resolves with an array containing all items from the DynamoDB table.
 */
async function getAllItemsFromDynamoDBTable(tableName) {
	console.log("function: getAllItemsFromDynamoDBTable")
  
	const params = {
		TableName: tableName,
	}
  
	const command = new ScanCommand(params)
	const result = await docClient.send(command)
  
	return result.Items
}


async function deleteDynamodbTableItem(tableName, pKey, pValue) {
	console.log("Function deleteDynamodbTableItem")
	try {
		const command = new DeleteCommand({
			TableName: tableName,
			Key: {
				[pKey]: pValue,
			}
		})
		console.log(`command => ${JSON.stringify(command)}`)
		const response = await docClient.send(command)
		console.log(`response => ${JSON.stringify(response.Item)}`)
		return response.Item
	} catch (e) {
		console.log("Error while retrieving Entry from table:", e)
		return {}
	}
}

/**
 * Queries items from a DynamoDB table with provided parameters.
 *
 * @param {string} tableName - The name of the DynamoDB table.
 * @param {string} indexName - The name of the index to query.
 * @param {string} key - The key to filter the items.
 * @param {string} value - The value of the key to filter the items.
 * @returns {Promise<Array<Object>>} - A promise that resolves with an array of items.
 */
async function queryDynamodbTableItemWithSortKey(tableName, indexName, key, value) {
	const params = {
		TableName: tableName,
		IndexName: indexName,
		KeyConditionExpression: `${key} = :${key}`,
		ExpressionAttributeValues: {
			[`:${key}`]: value
		}
	}

	console.log("Params for QueryCommand()", params)
	const command = new QueryCommand(params)
	const response = await docClient.send(command)
	console.log("Query response", response)
	return response.Items
}
module.exports = {
	putDynamodbTableItem,
	retrieveDynamoEntry,
	queryDynamodbTableItem,
	updateDynamoTableDataWithParams,
	queryDynamodbTableItemWithContactId,
	queryDynamodbTableItemWithParam,
	getAllItemsFromDynamoDBTable,
	deleteDynamodbTableItem,
	queryDynamodbTableItemWithSortKey,
	queryDynamodbTableItemGSI
}
