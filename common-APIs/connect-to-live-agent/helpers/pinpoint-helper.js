const { PinpointClient, PhoneNumberValidateCommand } = require('@aws-sdk/client-pinpoint')
const pinpointClient = new PinpointClient({ region: process.env.REGION })


/**
 * Validates a phone number using Amazon Pinpoint service.
 *
 * This function sends a request to the Amazon Pinpoint service to validate the provided phone number.
 *
 * @async
 * @param {string} phoneNo - The phone number to be validated.
 * @returns {Promise<Object>} A promise that resolves to the response from Amazon Pinpoint service.
 * @throws {Error} If the Amazon Pinpoint service call fails or if the phone number is invalid.
 *
 * @example
 * const phoneNumber = "+1234567890";
 * const response = await validatePhoneNumber(phoneNumber);
 * console.log(response);
 */
const validatePhoneNumber = async (phoneNo) => {
	console.log('pinpoint validatePhoneNumber initiated..........')
	let params = {
		NumberValidateRequest: {
			PhoneNumber: phoneNo
		}
	}
	const pinpointCommand = new PhoneNumberValidateCommand(params)
	console.log(`pinpointCommand => ${JSON.stringify(pinpointCommand)}`)
	const response = await pinpointClient.send(pinpointCommand)
	console.log(`pinpointResponse => ${JSON.stringify(response)}`)
	return response
}



module.exports = {
	validatePhoneNumber
}
