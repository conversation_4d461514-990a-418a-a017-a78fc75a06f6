[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "airline_app"
version = "1.0.0"
description = "Airline API for flight status, offers, and orders"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "fastapi>=0.68.0",
    "uvicorn>=0.15.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=0.19.0",
    "requests>=2.26.0",
    "openai>=1.0.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black>=21.5b2",
    "isort>=5.9.0",
    "flake8>=3.9.0"
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3 