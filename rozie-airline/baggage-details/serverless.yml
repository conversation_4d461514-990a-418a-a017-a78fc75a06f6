service: rozie-mock-airline-baggage-details

provider:
  name: aws
  runtime: nodejs18.x
  endpointType: regional
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:Scan
        - dynamodb:DeleteItem
        - dynamodb:UpdateItem
        - dynamodb:Query
      Resource: "*"
  apiGateway:
    apiKeys:
      - ${self:service}-${self:provider.stage}-Api-Key

functions:
  baggage-details:
    name: ${self:service}-${self:provider.stage}
    handler: index.lambda_handler
    description: function to manage the baggage details
    environment:
      BAGGAGE_TABLE_NAME: rozie-mock-airline-baggage-details-${self:provider.stage}
      PNR_TABLE_NAME: rozie-mock-airline-pnr-details-${self:provider.stage}
      FLIGHT_SCHEDULE_TABLE: rozie-mock-airline-flight-schedule-${self:provider.stage}
    events:
      - http:
          path: /createBaggage
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /getBaggage
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang

resources:
  Resources:
    BaggageDetailsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: pnr_id
            AttributeType: S
          - AttributeName: baggage_id
            AttributeType: S
          - AttributeName: flight_schedule_id
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: baggage_id
        GlobalSecondaryIndexes:
          - IndexName: PNRIndex
            KeySchema:
              - AttributeName: pnr_id
                KeyType: HASH  
            Projection:
              ProjectionType: ALL
          - IndexName: flightShceduleIndex
            KeySchema:
              - AttributeName: flight_schedule_id
                KeyType: HASH  
            Projection:
              ProjectionType: ALL
        BillingMode: PAY_PER_REQUEST
        TableName: rozie-mock-airline-baggage-details-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
