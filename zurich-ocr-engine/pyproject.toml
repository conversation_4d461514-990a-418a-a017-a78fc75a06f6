[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "zurich-ocr-engine"
version = "2.0.0"
description = "Advanced OCR solution with LLM-powered intelligent routing"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    {name = "Zurich OCR Team", email = "<EMAIL>"},
]
keywords = ["ocr", "document-ai", "llm", "fastapi", "google-cloud", "insurance"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Image Processing",
    "Topic :: Office/Business :: Financial :: Insurance",
    "Framework :: FastAPI",
]

dependencies = [
    # Core FastAPI and web framework
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.0.0",
    
    # LLM Integration for Intelligent Routing
    "openai>=1.6.1",
    
    # Google Document AI Integration
    "google-cloud-documentai>=2.20.1",
    "google-cloud-storage>=2.10.0",
    "google-auth>=2.25.2",
    "google-auth-oauthlib>=1.1.0",
    "google-auth-httplib2>=0.1.1",
    
    # OCR and Image Processing
    "pytesseract>=0.3.10",
    "Pillow>=10.1.0",
    "opencv-python>=********",
    "pdf2image>=1.16.3",
    
    # Document Processing - Comprehensive Support
    "PyPDF2>=3.0.1",
    "pdfplumber>=0.10.3",
    "python-docx>=1.1.0",
    "openpyxl>=3.1.2",
    "python-pptx>=0.6.23",
    "pandas>=2.1.4",
    
    # Web Content Processing
    "beautifulsoup4>=4.12.2",
    "lxml>=4.9.3",
    
    # Email Processing
    "email-validator>=2.1.0",
    
    # File Format Detection and Processing
    "python-magic>=0.4.27",
    
    # Archive Processing
    "rarfile>=4.1",
    "py7zr>=0.20.8",
    
    # Additional Format Support
    "xlrd>=2.0.1",  # Legacy Excel support
    "PyYAML>=6.0.1",  # YAML support
    
    # Utilities and Performance
    "requests>=2.31.0",
    "aiofiles>=23.2.1",
    "python-dotenv>=1.0.0",
    
    # Logging and Monitoring
    "structlog>=23.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "httpx>=0.25.2",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
]

aws = [
    "boto3>=1.34.0",
    "botocore>=1.34.0",
]

azure = [
    "azure-ai-formrecognizer>=3.3.0",
    "azure-core>=1.29.5",
]

all = [
    "zurich-ocr-engine[dev,aws,azure]",
]

[project.urls]
Homepage = "https://github.com/zurich/ocr-engine"
Documentation = "https://zurich-ocr-engine.readthedocs.io/"
Repository = "https://github.com/zurich/ocr-engine.git"
"Bug Tracker" = "https://github.com/zurich/ocr-engine/issues"

[project.scripts]
zurich-ocr = "zurich_ocr.main:main"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["zurich_ocr"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pytesseract.*",
    "cv2.*",
    "pdf2image.*",
    "google.cloud.*",
    "openai.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "-ra",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["zurich_ocr"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
