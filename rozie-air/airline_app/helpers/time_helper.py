import time
from datetime import datetime, timedelta
import pytz

from airline_app.common.constants import TIM<PERSON>Z<PERSON><PERSON>

def get_epoch_time():
    try:
        timezone = pytz.timezone(TIMEZONE)
        localized_time = datetime.now(timezone)
        epoch_time = int(localized_time.timestamp())
        return epoch_time
    except Exception as e:
        return f"Error: {e}"

def get_rounded_relative_time(offset_hours):
    epoch_time = get_epoch_time()
    timezone = pytz.timezone(TIMEZONE)
    current_time = datetime.fromtimestamp(epoch_time, timezone)
    relative_time = current_time + timedelta(hours=offset_hours)

    minute = relative_time.minute
    if minute < 15:
        rounded_minute = 0
    elif minute < 45:
        rounded_minute = 30
    else:
        rounded_minute = 0
        relative_time += timedelta(hours=1)

    rounded_time = relative_time.replace(minute=rounded_minute, second=0, microsecond=0)
    return rounded_time
