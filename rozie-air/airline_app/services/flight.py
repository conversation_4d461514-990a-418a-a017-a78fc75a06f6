from airline_app.helpers.dynamo_helper import put_record_into_dynamodb,query_dynamodb_table_item_gsi, get_item_by_primary_key
from airline_app.common.constants import (
    FLIGHT_TABLE,
    PNR_TABLE,
    PNR_TABLE_FLIGHT_INDEX
)
from airline_app.helpers.time_helper import get_rounded_relative_time
from airline_app.helpers.airline_code_helper import get_airline_name


# def get_available_flight_seat(flight_id):
#     flight_details = get_item_by_primary_key(FLIGHT_TABLE, "flight_id", flight_id)
#     if flight_details:
        
#     return None


def create_flight_entry(flight_details):
    put_record_into_dynamodb(FLIGHT_TABLE,flight_details)

def get_available_flight_seat(flight_id):
    flight_details = get_item_by_primary_key(FLIGHT_TABLE, "flight_id", flight_id)
    if flight_details:
        pnr_details = query_dynamodb_table_item_gsi(
            PNR_TABLE, PNR_TABLE_FLIGHT_INDEX, {"flight_id": flight_id}
        )

        occupied_seats = set()
        for pnr_detail in pnr_details:
            occupied_seats.update([passenger["seat"] for passenger in pnr_detail.get("passengers", [])])
        print(occupied_seats)
        available_seats = []
        for seat in flight_details["seats"]:
            if seat["seat_number"] in occupied_seats:
                print("match")
                continue
            available_seats.append(seat)

        return available_seats
    return False

def get_flight_status(flight_id):
    flight_details = get_item_by_primary_key(FLIGHT_TABLE, "flight_id", flight_id)
    if flight_details:
        result = {}
        delay = int(flight_details["delay"])
        rounded_departure_time = get_rounded_relative_time(int(flight_details["departure"])+delay)
        rounded_arrival_time = get_rounded_relative_time(int(flight_details["arrival"])+delay)
        result["flight_id"] = flight_details["flight_id"]
        result["arrival_time"] = rounded_arrival_time.isoformat()
        result["departure_time"] = rounded_departure_time.isoformat()
        duration = rounded_arrival_time - rounded_departure_time
        hours, remainder = divmod(duration.total_seconds(), 3600)
        minutes = remainder // 60
        result["duration"] = f"{int(hours):02}h {int(minutes):02}m"
        result["departure_airport"]= get_airline_name(flight_details["departure_airport"])
        result["arrival_airport"]= get_airline_name(flight_details["arrival_airport"])
        result["departure_airport_code"]= flight_details["departure_airport"]
        result["arrival_airport_code"]= flight_details["arrival_airport"]
        result["arrival_city"] = flight_details["arrival_airport_city"]
        result["departure_city"] = flight_details["departure_airport_city"]
        if delay > 0:
            result["status"] = "DELAYED"
            result["delay_reason"] = flight_details.get("delay_reason", "Operational")
            result["message"] = f"Flight is delayed by {delay} hours"
        else:
            result["status"] = "ON-TIME"
            result["message"] = "Flight is on time"
        if flight_details.get("arrival")<= 0:
            result["status"] = "COMPLETE"
            result["message"] = f"Flight reached destination at {rounded_arrival_time}"
        elif flight_details.get("departure") <= 0:
            result["message"] += f" and departed at {rounded_departure_time} and expected to arrive at {rounded_arrival_time}."
        elif flight_details.get("departure") > 0:
            result["message"] += f" and scheduled to depart at {rounded_departure_time} and arrive at {rounded_arrival_time}."
        return {
            "status": "Success",
            "flight_data": result
        }
    return {
        "status": "Failed",
        "suggested_action": "Let customer know that, no flight details were found for requested Flight ID and to verify Flight ID."
    }

def get_available_meal_services(flight_id, seat_class_name):
    flight_details = get_item_by_primary_key(FLIGHT_TABLE, "flight_id", flight_id)
    result = {}
    if flight_details:
        included_services = flight_details.get("included_services", {})
        services = included_services.get(seat_class_name, [])
        meal_included = False
        if "meal" in services:
            meal_included = True
            result["meal_included"] = meal_included
            result["message"] = f"Meal options are pre-included for the {seat_class_name} please select your preference."
        else:
            result["meal_included"] = meal_included
            result["message"] = f"Meal options are not pre-included for the {seat_class_name}, Still you can chose you preference with additional cost applied."
        final_options = flight_details.get("services", {}).get("meal", {})
        if final_options:
            result["meal_options"] = final_options
        else:
            result["message"] = "Sorry, but the fight do not provide any meal options."
        return result
    return False
