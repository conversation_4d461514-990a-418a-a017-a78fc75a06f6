import json
import requests
import logging
from typing import Dict, Any, List, Optional
from airline_app.core import config
from airline_app.services.base_service import BaseApiService

logger = logging.getLogger(__name__)

class FlightOrderService(BaseApiService):
    def __init__(self):
        super().__init__()

    def create_order(self, flight_offer: Dict[str, Any], travelers: List[Dict[str, Any]], 
                    remarks: Optional[Dict[str, Any]] = None,
                    ticketing_agreement: Optional[Dict[str, Any]] = None,
                    contacts: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Create a flight order using the Amadeus API.
        
        Args:
            flight_offer: The flight offer data from flight offer service
            travelers: List of traveler information including:
                      - id: Traveler ID
                      - dateOfBirth: Date of birth in YYYY-MM-DD format
                      - gender: Gender (MALE/FEMALE)
                      - name: Dictionary with firstName and lastName
                      - contact: Dictionary with emailAddress and phones
            remarks: Optional remarks for the order
            ticketing_agreement: Optional ticketing agreement details
            contacts: Optional list of contacts
        
        Returns:
            Flight order data from Amadeus API
            
        Raises:
            ValueError: If the input data is invalid
            requests.exceptions.RequestException: If the API request fails
        """
        try:
            # Validate input data
            if not flight_offer or not isinstance(flight_offer, dict):
                raise ValueError("Invalid flight offer data")
            
            if not travelers or not isinstance(travelers, list):
                raise ValueError("Invalid travelers data")
                
            for traveler in travelers:
                if not isinstance(traveler, dict):
                    raise ValueError(f"Invalid traveler data format")
                required_fields = ['id', 'dateOfBirth', 'gender', 'name', 'contact']
                missing_fields = [field for field in required_fields if field not in traveler]
                if missing_fields:
                    raise ValueError(f"Missing required fields in traveler {traveler.get('id', 'unknown')}: {', '.join(missing_fields)}")

            endpoint = f"{self.base_url}/v1/booking/flight-orders"
            
            # Prepare the request payload
            payload = {
                "data": {
                    "type": "flight-order",
                    "flightOffers": [flight_offer],
                    "travelers": travelers
                }
            }

            # Add optional fields if provided
            if remarks:
                payload["data"]["remarks"] = remarks
            if ticketing_agreement:
                payload["data"]["ticketingAgreement"] = ticketing_agreement
            if contacts:
                payload["data"]["contacts"] = contacts

            logger.info(f"Making request to Amadeus API: {endpoint}")
            logger.info(f"Request payload: {json.dumps(payload, indent=2)}")
            
            headers = self._get_headers()
            logger.info(f"Request headers: {json.dumps({k: v for k, v in headers.items() if k != 'Authorization'}, indent=2)}")

            response = requests.post(
                endpoint,
                headers=headers,
                json=payload
            )
            
            if response.status_code == 401:
                logger.warning("Received 401 Unauthorized, refreshing token and retrying")
                self._handle_unauthorized()
                return self.create_order(flight_offer, travelers, remarks, ticketing_agreement, contacts)
            
            # Handle specific error cases
            if response.status_code == 400:
                error_data = response.json()
                logger.error(f"Bad request: {error_data}")
                raise ValueError(f"Invalid request data: {error_data.get('errors', [])}")
            
            if response.status_code == 403:
                logger.error("Forbidden: Insufficient permissions")
                raise PermissionError("Insufficient permissions to create flight order")
            
            if response.status_code == 404:
                logger.error("Flight offer not found or no longer available")
                raise ValueError("Flight offer not found or no longer available")
            
            response.raise_for_status()
            data = response.json()
            logger.info(f"Successfully created flight order: {json.dumps(data, indent=2)}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error creating flight order: {str(e)}")
            if hasattr(e.response, 'text'):
                logger.error(f"Response content: {e.response.text}")
            raise

    def get_order(self, order_id: str) -> Dict[str, Any]:
        """
        Retrieve flight order details by order ID.
        
        Args:
            order_id: The ID of the flight order to retrieve
        
        Returns:
            Order data in Amadeus API format
        """
        try:
            endpoint = f"{self.base_url}/v1/booking/flight-orders/{order_id}"
            
            logger.info(f"Making request to Amadeus API: {endpoint}")
            
            headers = self._get_headers()
            logger.info(f"Request headers: {json.dumps({k: v for k, v in headers.items() if k != 'Authorization'}, indent=2)}")

            response = requests.get(
                endpoint,
                headers=headers
            )
            
            if response.status_code == 401:
                logger.warning("Received 401 Unauthorized, refreshing token and retrying")
                self._handle_unauthorized()
                return self.get_order(order_id)
            
            if response.status_code != 200:
                logger.error(f"Amadeus API returned error: {response.status_code}")
                logger.error(f"Response content: {response.text}")
                response.raise_for_status()
            
            data = response.json()
            logger.info(f"Successfully retrieved flight order: {json.dumps(data, indent=2)}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting flight order from Amadeus: {str(e)}")
            if hasattr(e.response, 'text'):
                logger.error(f"Response content: {e.response.text}")
            raise

# Create singleton instance
flight_order_service = FlightOrderService()

def get_flight_order_service():
    """
    Get the singleton flight order service instance.
    """
    return flight_order_service 