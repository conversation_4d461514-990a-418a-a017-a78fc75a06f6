"""
This helper function assist in sending http request.
"""

import requests
import json
from .logging_helper import print_error_log


def sync_get_request(url, query_params=None, username=None, password=None):
    """
    This function used for invoking get method and wait for result.
    """
    param = {"url": url, "timeout": 30}
    if query_params:
        param["params"] = query_params
    if username and password:
        param["auth"] = (username, password)
    try:
        response = requests.get(**param)

        if response.status_code != 200:
            return "Failed", {}

        response = response.json()
        return "Success", response

    except Exception as e:
        print_error_log("", e)
        return "Failed", str(e)


def post_request(url, headers=None, body=None, username=None, password=None):
    """
    This function used for invoking get method and wait for result.
    """
    param = {"url": url, "timeout": 30}
    if headers:
        param["headers"] = headers
    if body:
        param["data"] = json.dumps(body)
    if username and password:
        param["auth"] = (username, password)
    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            if response.json():
                return "Failed", response.json()
            return "Failed", {}

        response = response.json()
        return "Success", response

    except Exception as e:
        print_error_log("", e)
        return "Failed", str(e)
