import json
import requests
from .logging_helper import print_info_log

def create_case_status_message(case_data):
    """Helper function to build the case status message."""
    return (
       f"We have an update regarding your case titled, '{case_data.get('caseTitle')}'. "
        f"The current status is '{case_data.get('caseStatus')}'. "
        f"This case concerns, '{case_data.get('caseDescription')}'. "
    
    )

def case_status_wrapper(event):
    body = json.loads(event["body"])
    customer_id = body["concepts"].get("CustomerId", {}).get("CustomerId", "")
    url = (
        "https://88y2mfkbjg.execute-api.ca-central-1.amazonaws.com/dev/caseManagementWrapper"
    )
    payload = json.dumps({"customer_id": customer_id})
    headers = {
        "x-api-key": "yN7l1i92cP2m16K2FqWIe8tmysaB8QK1TvQxDeKh",
        "Content-Type": "application/json",
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    
    if response.status_code == 200:
        response_data = response.json()
        CaseStatusSuccessMessage = ""
        
        if response_data.get("status") == "Success" and response_data.get("response"):
            case_data = response_data["response"]
            CaseStatusSuccessMessage = create_case_status_message(case_data)
        
        result = {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "api_result_type": "success",
                    "concepts": {
                        "CaseStatusMessage": {"CaseStatusMessage": CaseStatusSuccessMessage}
                    },
                }
            ),
        }
        print_info_log("", "result", result)
        return result
    
    result = {
        "statusCode": 400,
        "body": json.dumps(
            {
                "api_result_type": "failure",
                "concepts": {"CaseStatusMessage": {"CaseStatusMessage": ""}},
            }
        ),
    }
    print_info_log("", "result", result)
    return result

def case_status_wrapper_info():
    
        return {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "api_name": "Case status API",
                    "api_description": "This API get's case status.",
                    "input_concepts": {
                        "CustomerId": "true",
                    },
                    "output_concepts": {"CaseStatusMessage": "true"},
                }
            ),
        }
        