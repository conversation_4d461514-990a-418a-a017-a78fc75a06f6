#!/usr/bin/env python3

import requests
import json

def test_batch_processing():
    url = "http://localhost:8000/api/v1/batch-process"
    
    # Configuration
    config = {
        "ocr_engine": "gemini2.5flash",
        "llm_routing_enabled": False,
        "post_processing": "none",
        "preprocessing": "none",
        "insurance_optimization": False
    }
    
    # Files to process
    files_to_process = [
        "/Users/<USER>/Downloads/Data Docs/139- Redacted  ExampleStatement 2.png",
        "/Users/<USER>/Downloads/Data Docs/139- Redacted ExampleStatement 1.png"
    ]
    
    # Prepare files for upload
    files = []
    for file_path in files_to_process:
        try:
            with open(file_path, 'rb') as f:
                files.append(('files', (file_path.split('/')[-1], f.read(), 'image/png')))
        except FileNotFoundError:
            print(f"File not found: {file_path}")
            return
    
    # Prepare data
    data = {
        'config': json.dumps(config)
    }
    
    print(f"Testing batch processing with {len(files)} files...")
    print(f"Config: {config}")
    
    try:
        response = requests.post(url, files=files, data=data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success')}")
            print(f"Total documents: {result.get('total_documents')}")
            print(f"Processing time: {result.get('processing_time_ms')}ms")
            
            for i, doc_result in enumerate(result.get('results', [])):
                print(f"\nDocument {i+1}: {doc_result.get('filename')}")
                print(f"  Success: {doc_result.get('success')}")
                print(f"  Processor: {doc_result.get('processor_used')}")
                print(f"  Text length: {len(doc_result.get('extracted_text', ''))}")
                if not doc_result.get('success'):
                    print(f"  Error: {doc_result.get('error')}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    test_batch_processing()
