const { ApiResp } = require("../helpers/api-helper.js")
const { retrieveDynamoEntry } = require("../helpers/dynamo-helper.js")

/**
 * @function getPNR
 * @description This function retrieves a PNR entry from a DynamoDB table by PNR ID.
 * 
 * @param {Object} event - The event object containing the pnr_id.
 * @param {string} event.pnr_id - The PNR ID to be retrieved.
 * 
 * @returns {Promise<Object>} - The response object containing the PNR details or an error message.
 * 
 * @throws {Error} - Throws an error if there is an issue with retrieving the PNR entry.
 */
const getPNR = async (pnr_id) => {

	try {
		console.log(`Function getPNR initiated for PNR ID: ${pnr_id}`)
		let pnrDetails = await retrieveDynamoEntry(process.env.PNR_TABLE_NAME, "pnr_id", pnr_id)
		if (!pnrDetails) {
			return ApiResp({
				status: "Error",
				message: "<PERSON><PERSON> not found",
				response:[]
			}, 404)
		}

		const { flight_schedule_id } = pnrDetails
		if (flight_schedule_id) {
			const flightScheduleDetails = await retrieveDynamoEntry(process.env.FLIGHT_SCHEDULE_TABLE, "scheduleId", flight_schedule_id)
			if (flightScheduleDetails) {
				delete flightScheduleDetails.seating
				pnrDetails.flight_schedule_details = flightScheduleDetails
			} else {
				pnrDetails.flight_schedule_details = {}
			}
		}
		
		return ApiResp({
			status: "Success",
			message: "PNR details retrieved successfully",
			response: pnrDetails
		})
	} catch (error) {
		console.log("Error in getPNR:", error)
		return ApiResp({
			status: "Error",
			message: "Error retrieving PNR"
		}, 500)
	}
}

module.exports = {
	getPNR
}
