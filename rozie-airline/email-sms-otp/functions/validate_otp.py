"""
Function: validate_otp

This function is used to validate the OTP provided by caller
"""

import os
import json
import hashlib
from helpers.decorator_helper import exception_handler
from helpers.logging_helper import print_info_log
from helpers.response_helper import format_response
from helpers.dynamo_helper import get_resource_item_by_partition_key

OTP_TABLE = os.environ["OTP_TABLE"]
enable_logs = os.environ.get("ENABLE_LOGS")

def compare_password(code, stored_hashed_code):
    """Compares the customer inputed OTP code and stored hashed code
    """
    hashed_code = hashlib.md5(str(code).encode()).hexdigest()
    return stored_hashed_code == hashed_code

def create_response_object(otp, Id):
    """
    Function fetches all the necessary data from various tables and create a response with
    """
    otp_table_data = get_resource_item_by_partition_key(
        OTP_TABLE, "id", Id
    )
    if otp_table_data == {}:
        response = format_response(200, {"status": "success"}, "OTP is expired")
        return response

    hashed_otp = otp_table_data["hashedCode"]

    if compare_password(otp, hashed_otp):
        response = format_response(200, {"status": "success"}, "OTP is valid")
        return response

    response = format_response(200, {"status": "success"}, "OTP is invalid")
    return response


@exception_handler
def validate_otp_handler(event):
    """
    This function is used to validate an OTP provided by the
    caller

    Args:
        event (dict): The event data.
        context (dict): The context data.
    """
    print_info_log(
        "",
        "validate_otp invoked, Event",
        event
    )
    Id = None
    if "recipient_phone" in event and "email" in event:
        Id = event["recipient_phone"]
        
    elif "recipient_phone" in event:
        Id = event["recipient_phone"]
        
    else:
        Id = event["email"]
        
    otp = event["otp"]
    response = {}
    print_info_log(
        "",
        "validate_otp invoked, Event",
        event
    )

    if len(otp) == 6:
        response = create_response_object(otp, Id)
    else:
        response = format_response(200, {"status": "success"}, "OTP is invalid")

    print_info_log(
        "",
        "validate_otp completed",
        "Otp Send successfully"
    )
    return response
