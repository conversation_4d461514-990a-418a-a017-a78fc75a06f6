import json
from langchain_community.chat_models import ChatLiteLLM
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from pydantic import BaseModel, Field

class MessageResponse(BaseModel):
    extracted_entity: str = Field(
        description="Extracted Entity Value based on the CurrentEntity DataType"
    )


class ExtractAgent:
    def __init__(self, chatId):
        self.chatId = chatId
        self.create_llm_client()

    def create_llm_client(self):
        model = "gpt-4o-mini"
        temperature =  0.0
        max_tokens = 1000
        llm_prompt = self.generate_prompt()
        llm_object = ChatLiteLLM(
            model=model,
            temperature=float(temperature),
            max_tokens=int(max_tokens),
        )
        parser = JsonOutputParser(pydantic_object=MessageResponse)
        prompt = PromptTemplate(
            template=llm_prompt,
            input_variables=["InputText", "CurrentEntity"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )
        self.chain = prompt | llm_object | parser

    def generate_prompt(self):
        """Generate the system prompt for the conversation agent."""

        Prompt = [
            "You are a helpful Entity extraction agent.",
            "You Job is the extract the Entity from InputText based on the configuration provided into CurrentEntity.",
            "You must extract the Entity based on the DataType of the CurrentEntity.",
        ]
        llm_prompt = "\n".join(Prompt)
        llm_prompt = (
            llm_prompt
            + "\nCurrentEntity:\n{CurrentEntity}\n"
            + "\nInputText:\n{InputText}\n"
            + "\nFormat Instructions:\n{format_instructions}"
        )
        return llm_prompt

    def extract(self, message, CurrentEntity):
        try:
            CurrentEntity = json.dumps(CurrentEntity, indent=2)
            result = self.chain.invoke(
                {
                    "InputText": message,
                    "CurrentEntity": CurrentEntity,
                }
            )
            return result.get("extracted_entity", "").replace(" ", "")
        except Exception as e:
            print(f"Exception in convert_response: {e}")
