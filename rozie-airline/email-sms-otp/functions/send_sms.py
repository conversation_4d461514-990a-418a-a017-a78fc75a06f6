import os
from helpers.twilio_helper import send_sms_twilio
from helpers.response_helper import format_response
from helpers.pinpoint_helper import send_sms_via_pinpoint
from helpers.dynamo_helper import (
    get_resource_item_by_partition_key,
    get_resource_item_by_primary_key
)

def send_sms_handler(event):
    """
    Lambda function to send an SMS using either AWS Pinpoint or Twilio, based on the configured service.

    Args:
        event (dict): The event data (expects 'recipient_phone', 'message', and 'service' in body).
        context (dict): The context data (Lambda context).

    Returns:
        dict: Formatted response after attempting to send the SMS.
    """
    try:
        recipient_phone = event["recipient_phone"]
        message = event["message"]
        CONFIG_TABLE = os.environ["CONFIG_TABLE"]
        global_config = get_resource_item_by_partition_key(
        CONFIG_TABLE, "organizationId", "1"
        )
        origination_number = global_config["PinpointSMSOriginationNumber"]
        OTP_SERVICE_PROVIDER = global_config["OTP_SERVICE_PROVIDER"]

        if not recipient_phone or not message:
            return format_response(400, {}, "recipient_phone and message are required.")

        # Check the service to use (either 'twilio' or 'aws').
        if OTP_SERVICE_PROVIDER == 'twilio':
            send_sms_twilio(recipient_phone, message)
        elif OTP_SERVICE_PROVIDER == 'aws':
            application_id = "dd0bd5ac550e4246859dbff799073a68"
            if not application_id or not origination_number:
                return format_response(500, {}, "AWS Pinpoint configuration is missing.")
            
            send_sms_via_pinpoint(application_id, origination_number, recipient_phone, message)
        else:
            return format_response(400, {}, "Invalid SMS service specified. Use 'twilio' or 'aws'.")

        # Success response
        return format_response(200, {}, "SMS sent successfully")

    except Exception as e:
        # Error response
        return format_response(500, {}, f"Error sending SMS: {str(e)}")
