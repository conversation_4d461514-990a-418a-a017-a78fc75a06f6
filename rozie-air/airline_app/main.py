from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from airline_app.routes import (
    health_router,
    pnr_router,
    flight_router,
    customer_router,
    case_router,
    airport_map_router,
    baggage_router,
    email_notification_router,
    authentication
)
from airline_app.common.constants import ALLOWED_ORIGINS


from airline_app.helpers.logging_helper import (
    set_logging_level,
    get_log_level,
)


app = FastAPI()
set_logging_level(get_log_level())
app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS.split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(pnr_router.pnr_router)
app.include_router(health_router.health_router)
app.include_router(flight_router.flight_router)
app.include_router(customer_router.customer_router)
app.include_router(case_router.case_router)
app.include_router(airport_map_router.airport_map_router)
app.include_router(baggage_router.baggage_router)
app.include_router(email_notification_router.email_notification_router)
app.include_router(authentication.authentication_router)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=80, debug=True)
