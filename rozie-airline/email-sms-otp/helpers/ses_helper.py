"""
    ses_helper.py
    module to send email using AWS SES service
"""

import os
import logging

import boto3
from botocore.exceptions import ClientError


ses_client = boto3.client("ses")
print("inside ses_helper");

def send_mail_via_ses(sender, recipients, message):
    """
    This function is used to send email
    """
    try:
        response = ses_client.send_email(
            Destination={
                'ToAddresses': recipients,
            },
            Message={
                'Body': {
                    'Text': {
                        'Charset': 'UTF-8',
                        'Data': message,
                    },
                },
                'Subject': {
                    'Charset': 'UTF-8',
                    'Data': 'OTP message',
                },
            },
            Source= sender,
        )
        logging.info("Email sent! Message ID: %s", response["MessageId"])
        return response
    except ClientError as e:
        logging.info(e.response["Error"]["Message"])
        return None
    
    
