###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off

file_map = {
    
    "clients.baml": "// Learn more about clients at https://docs.boundaryml.com/docs/snippets/clients/overview\n\nclient<llm> CustomGPT4o {\n  provider openai\n  options {\n    model \"gpt-4o\"\n    api_key env.OPENAI_API_KEY\n    temperature 0\n  }\n}\n\nclient<llm> CustomGPT4oMini {\n  provider openai\n  retry_policy Exponential\n  options {\n    model \"gpt-4o-mini\"\n    api_key env.OPENAI_API_KEY\n    temperature 0\n  }\n}\n\nclient<llm> CustomSonnet {\n  provider anthropic\n  options {\n    model \"claude-3-5-sonnet-20241022\"\n    api_key env.ANTHROPIC_API_KEY\n    temperature 0\n  }\n}\n\n\nclient<llm> CustomHaiku {\n  provider anthropic\n  retry_policy Constant\n  options {\n    model \"claude-3-haiku-20240307\"\n    api_key env.ANTHROPIC_API_KEY\n    temperature 0\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/round-robin\nclient<llm> CustomFast {\n  provider round-robin\n  options {\n    // This will alternate between the two clients\n    strategy [CustomGPT4oMini, CustomHaiku]\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/fallback\nclient<llm> OpenaiFallback {\n  provider fallback\n  options {\n    // This will try the clients in order until one succeeds\n    strategy [CustomGPT4oMini, CustomGPT4oMini]\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/retry\nretry_policy Constant {\n  max_retries 3\n  // Strategy is optional\n  strategy {\n    type constant_delay\n    delay_ms 200\n  }\n}\n\nretry_policy Exponential {\n  max_retries 2\n  // Strategy is optional\n  strategy {\n    type exponential_backoff\n    delay_ms 300\n    multiplier 1.5\n    max_delay_ms 10000\n  }\n}",
    "data_completeness_analysis.baml": "// ================================================================================================\n// AI-DRIVEN DATA COMPLETENESS ANALYSIS - BAML IMPLEMENTATION\n// ================================================================================================\n// Purpose: Intelligent assessment of data completeness for insurance claims analysis\n// Enhancement: Replaces hardcoded scoring with AI-driven contextual assessment\n// ================================================================================================\n\n// ================================================================================================\n// DATA COMPLETENESS SCHEMAS\n// ================================================================================================\n\nclass DataCompletenessAssessment {\n  overallCompletenessScore float @description(\"Overall data completeness score (0.0-1.0)\")\n  \n  // Component-specific assessments\n  level01AnalysisCompleteness float @description(\"Completeness of Level 01 analysis (0.0-1.0)\")\n  claimDetailsCompleteness float @description(\"Completeness of basic claim information (0.0-1.0)\")\n  ocrDocumentsCompleteness float @description(\"Completeness of OCR extracted documents (0.0-1.0)\")\n  attachmentsCompleteness float @description(\"Completeness of file attachments (0.0-1.0)\")\n  \n  // Dynamic importance weighting\n  level01AnalysisImportance float @description(\"Importance weight for Level 01 analysis for this specific claim type (0.0-1.0)\")\n  claimDetailsImportance float @description(\"Importance weight for claim details for this specific claim type (0.0-1.0)\")\n  ocrDocumentsImportance float @description(\"Importance weight for OCR documents for this specific claim type (0.0-1.0)\")\n  attachmentsImportance float @description(\"Importance weight for attachments for this specific claim type (0.0-1.0)\")\n  \n  // Critical gaps identification\n  criticalMissingItems string[] @description(\"List of critically missing data items that significantly impact analysis\")\n  minorMissingItems string[] @description(\"List of minor missing data items with minimal impact\")\n  \n  // Quality assessment\n  dataQualityScore float @description(\"Overall quality of available data (0.0-1.0)\")\n  analysisReadiness string @description(\"Assessment of readiness for Level 02 analysis: READY, NEEDS_CLARIFICATION, INSUFFICIENT_DATA\")\n  \n  // Recommendations\n  improvementRecommendations string[] @description(\"Specific recommendations to improve data completeness\")\n  alternativeDataSources string[] @description(\"Alternative sources that could fill data gaps\")\n  \n  // Context-specific insights\n  claimTypeSpecificNeeds string @description(\"Specific data needs based on claim type (AUTO, PROPERTY, LIABILITY, etc.)\")\n  riskFactorsFromGaps string[] @description(\"Risk factors introduced by data gaps\")\n  \n  // Confidence and uncertainty\n  assessmentConfidence float @description(\"Confidence in this completeness assessment (0.0-1.0)\")\n  uncertaintyAreas string[] @description(\"Areas where completeness assessment is uncertain\")\n}\n\nclass DataItem {\n  itemName string @description(\"Name of the data item\")\n  isPresent bool @description(\"Whether this data item is present\")\n  qualityScore float @description(\"Quality score of this data item if present (0.0-1.0)\")\n  importanceForClaimType float @description(\"Importance of this item for the specific claim type (0.0-1.0)\")\n  alternativeSources string[] @description(\"Alternative sources where this data could be obtained\")\n  impactOnAnalysis string @description(\"How absence/presence of this item impacts Level 02 analysis\")\n}\n\n// ================================================================================================\n// AI-DRIVEN DATA COMPLETENESS ANALYSIS FUNCTION\n// ================================================================================================\n\nfunction AssessDataCompleteness(\n  claimId: string,\n  claimType: string,\n  level01Analysis: string,\n  claimDetails: string,\n  ocrTexts: string[],\n  attachmentsList: string[],\n  additionalContext: string\n) -> DataCompletenessAssessment {\n  client \"openai/gpt-4o-mini\"\n  prompt #\"\n    You are an AI expert in insurance data analysis, specializing in assessing data completeness for claims processing.\n    \n    MISSION: Intelligently assess the completeness and quality of available data for Level 02 coverage analysis, providing dynamic importance weighting based on claim type and context.\n    \n    CLAIM INFORMATION:\n    Claim ID: {{ claimId }}\n    Claim Type: {{ claimType }}\n    \n    AVAILABLE DATA SOURCES:\n    \n    LEVEL 01 ANALYSIS:\n    {{ level01Analysis }}\n    \n    BASIC CLAIM DETAILS:\n    {{ claimDetails }}\n    \n    OCR EXTRACTED TEXTS:\n    {% for text in ocrTexts %}\n    - {{ text }}\n    {% endfor %}\n    \n    ATTACHMENTS:\n    {% for attachment in attachmentsList %}\n    - {{ attachment }}\n    {% endfor %}\n    \n    ADDITIONAL CONTEXT:\n    {{ additionalContext }}\n    \n    INTELLIGENT DATA COMPLETENESS ASSESSMENT REQUIREMENTS:\n    \n    1. CONTEXTUAL IMPORTANCE WEIGHTING:\n       - Analyze the specific claim type ({{ claimType }}) to determine data importance\n       - Consider claim complexity, financial exposure, and regulatory requirements\n       - Adjust importance weights based on specific circumstances\n       - Account for industry best practices for this claim type\n    \n    2. QUALITY OVER QUANTITY ASSESSMENT:\n       - Evaluate not just presence but quality of available data\n       - Consider consistency across data sources\n       - Assess reliability and completeness of each data component\n       - Identify conflicting information that needs resolution\n    \n    3. CRITICAL GAP IDENTIFICATION:\n       - Identify data gaps that would prevent accurate coverage determination\n       - Distinguish between critical gaps and nice-to-have information\n       - Consider alternative data sources that could fill gaps\n       - Assess impact of each gap on analysis confidence\n    \n    4. CLAIM-TYPE SPECIFIC ANALYSIS:\n       For AUTO claims, prioritize: vehicle details, police reports, damage assessments\n       For PROPERTY claims, prioritize: property valuations, cause determinations, exclusion applicability\n       For LIABILITY claims, prioritize: incident details, witness information, causation evidence\n       For HEALTH/DISABILITY claims, prioritize: medical records, employment history, benefit calculations\n    \n    5. INTELLIGENT SCORING METHODOLOGY:\n       - Use dynamic weighting based on claim characteristics\n       - Consider data interdependencies (e.g., Level 01 analysis quality affects overall score more heavily)\n       - Factor in data freshness and reliability\n       - Account for regulatory and legal requirements\n    \n    6. ANALYSIS READINESS DETERMINATION:\n       READY: Sufficient high-quality data for confident coverage determination\n       NEEDS_CLARIFICATION: Some gaps exist but analysis can proceed with noted uncertainties\n       INSUFFICIENT_DATA: Critical gaps prevent reliable coverage analysis\n    \n    7. ACTIONABLE RECOMMENDATIONS:\n       - Specific steps to improve data completeness\n       - Alternative data sources to consider\n       - Prioritized list of information to obtain\n       - Timeline considerations for data gathering\n    \n    ASSESSMENT GUIDELINES:\n    \n    - Be context-aware: Different claim types require different data priorities\n    - Consider data quality, not just presence\n    - Provide specific, actionable feedback\n    - Use realistic scoring that reflects actual data utility\n    - Account for diminishing returns (90% completeness may be as good as 100% for analysis purposes)\n    - Consider legal and regulatory data requirements\n    - Factor in cost-benefit analysis of obtaining additional data\n    \n    Provide a comprehensive assessment that enables intelligent decision-making about proceeding with Level 02 analysis or obtaining additional data first.\n    \n    {{ ctx.output_format }}\n  \"#\n} ",
    "document_highlights.baml": "// AI-Driven Document Highlights and Explainability\n// Generates intelligent highlights with color-coding and explanations for claim documents\n// Note: DocumentHighlight class is now defined in individual analysis levels (level01_analysis.baml, level02_coverage_analysis.baml)\n\nclass ColorLegend {\n  color string @description(\"Hex color code\")\n  meaning string @description(\"What this color represents in the context of claim analysis\")\n  examples string[] @description(\"Examples of what gets highlighted in this color\")\n}\n\n// Note: DocumentExplainability now uses DocumentHighlight from individual analysis levels\n// This file provides legacy highlight generation functions - consider using UI metadata from analysis levels instead\n\n// Legacy function - consider using UI metadata from individual analysis levels instead\n// This function is deprecated in favor of integrated explainability in each analysis level\nfunction GenerateDocumentHighlights(\n  document_text: string,\n  document_filename: string,\n  claim_reference: string,\n  email_subject: string,\n  email_body: string,\n  level01_analysis: string,\n  level02_analysis: string,\n  level03_analysis: string,\n  level04_analysis: string,\n  ocr_texts: string[]\n) -> ColorLegend[] {\n  client \"openai/gpt-4o\"\n  prompt #\"\n    You are an expert AI claims analyst tasked with generating explainable highlights for insurance claim documents.\n    \n    CONTEXT:\n    - Claim Reference: {{ claim_reference }}\n    - Document: {{ document_filename }}\n    - Email Subject: {{ email_subject }}\n    - Email Body: {{ email_body }}\n    \n    ANALYSIS LEVELS COMPLETED:\n    Level 1 Analysis: {{ level01_analysis }}\n    Level 2 Analysis: {{ level02_analysis }}\n    Level 3 Analysis: {{ level03_analysis }}\n    Level 4 Analysis: {{ level04_analysis }}\n    \n    OCR TEXTS FROM OTHER DOCUMENTS:\n    {% for ocr in ocr_texts %}\n    - {{ ocr }}\n    {% endfor %}\n    \n    DOCUMENT TO ANALYZE:\n    {{ document_text }}\n    \n    INSTRUCTIONS:\n    1. Analyze the document text and identify key phrases, sentences, or sections that:\n       - Support or contradict the AI analysis decisions made in Levels 1-4\n       - Provide evidence for coverage decisions, fault determination, or quantum calculations\n       - Contain critical dates, amounts, parties, or circumstances\n       - Influence risk assessment or claim validity\n    \n    2. For each highlight, determine:\n       - Exact character positions (start and end) in the document\n       - Appropriate color based on importance and type (use a consistent color scheme)\n       - Which specific claim field or analysis this text supports\n       - Clear explanation of why this text is significant\n       - Contribution score (how much this influenced the AI's decision)\n       - Highlight type (critical/supporting/evidence/context)\n    \n    3. Create a color legend that explains:\n       - What each color represents (e.g., red for critical issues, yellow for dates, green for positive evidence)\n       - Examples of what gets highlighted in each color\n       - Keep it simple but comprehensive\n    \n    4. Focus on explainability - every highlight should help a human understand:\n       - Why the AI made certain decisions\n       - What evidence supports or challenges the analysis\n       - How different pieces of information connect\n    \n    5. Prioritize quality over quantity - highlight the most important elements that truly impact the claim\n    \n    COLOR SCHEME GUIDELINES:\n    - Use hex colors that are visually distinct and accessible\n    - Consider: #FF6B6B (red) for critical issues, #4ECDC4 (teal) for dates/facts, \n      #45B7D1 (blue) for coverage info, #FFA07A (orange) for amounts/values,\n      #98D8C8 (mint) for positive evidence, #F7DC6F (yellow) for attention items\n    \n    {{ ctx.output_format }}\n  \"#\n}\n\n// Function for generating highlights for multiple documents at once\n// Legacy function - consider using UI metadata from individual analysis levels instead\nfunction GenerateMultiDocumentHighlights(\n  documents: string[],\n  filenames: string[],\n  claim_reference: string,\n  email_subject: string,\n  email_body: string,\n  level01_analysis: string,\n  level02_analysis: string,\n  level03_analysis: string,\n  level04_analysis: string\n) -> ColorLegend[] {\n  client \"openai/gpt-4o\"\n  prompt #\"\n    You are analyzing multiple documents for claim {{ claim_reference }}.\n    \n    CONTEXT:\n    - Email Subject: {{ email_subject }}\n    - Email Body: {{ email_body }}\n    \n    ANALYSIS LEVELS:\n    Level 1: {{ level01_analysis }}\n    Level 2: {{ level02_analysis }}\n    Level 3: {{ level03_analysis }}\n    Level 4: {{ level04_analysis }}\n    \n    DOCUMENTS TO ANALYZE:\n    {% for doc, filename in zip(documents, filenames) %}\n    \n    DOCUMENT: {{ filename }}\n    {{ doc }}\n    \n    ---\n    {% endfor %}\n    \n    Generate highlights for each document that show how they collectively support the AI's analysis decisions.\n    Maintain consistent color coding across all documents.\n    \n    {{ ctx.output_format }}\n  \"#\n} ",
    "email_classification.baml": "// ================================================================================================\n// ZURICH EMAIL CLASSIFICATION - BAML IMPLEMENTATION\n// ================================================================================================\n// Purpose: Classify incoming emails to determine if they are claims-related\n// Models: OpenAI GPT-4o (primary), GPT-4o-mini (fallback)\n// Integration: N8N workflow automation\n// ================================================================================================\n\n// ================================================================================================\n// CORE CLASSIFICATION SCHEMAS\n// ================================================================================================\n\nenum ClaimType {\n  LIABILITY @description(\"General liability claims\")\n  PROPERTY @description(\"Property damage claims\") \n  AUTO @description(\"Automotive insurance claims\")\n  MEDICAL_MALPRACTICE @description(\"Medical malpractice claims\")\n  PRODUCT_LIABILITY @description(\"Product liability claims\")\n  ENVIRONMENTAL @description(\"Environmental damage claims\")\n  WORKERS_COMPENSATION @description(\"Workers compensation claims\")\n  PROFESSIONAL_LIABILITY @description(\"Professional liability claims\")\n  CYBER_LIABILITY @description(\"Cyber security and data breach claims\")\n  NOT_CLAIM @description(\"Email is not insurance claim related\")\n}\n\nenum CanadianProvince {\n  ALBERTA @description(\"Alberta, Canada\")\n  BRITISH_COLUMBIA @description(\"British Columbia, Canada\")\n  MANITOBA @description(\"Manitoba, Canada\")\n  NEW_BRUNSWICK @description(\"New Brunswick, Canada\")\n  NEWFOUNDLAND_LABRADOR @description(\"Newfoundland and Labrador, Canada\")\n  NORTHWEST_TERRITORIES @description(\"Northwest Territories, Canada\")\n  NOVA_SCOTIA @description(\"Nova Scotia, Canada\")\n  NUNAVUT @description(\"Nunavut, Canada\")\n  ONTARIO @description(\"Ontario, Canada\")\n  PRINCE_EDWARD_ISLAND @description(\"Prince Edward Island, Canada\")\n  QUEBEC @description(\"Quebec, Canada\")\n  SASKATCHEWAN @description(\"Saskatchewan, Canada\")\n  YUKON @description(\"Yukon, Canada\")\n  UNKNOWN @description(\"Province cannot be determined from email\")\n}\n\nenum UrgencyLevel {\n  CRITICAL @description(\"Requires immediate attention - catastrophic incident\")\n  HIGH @description(\"High priority - significant damages or injuries\")\n  MEDIUM @description(\"Standard priority - routine claim processing\")\n  LOW @description(\"Low priority - minor incidents or inquiries\")\n}\n\nenum AttachmentMismatchType {\n  NO_MISMATCH @description(\"Attachments mentioned and provided, or neither mentioned nor provided\")\n  MENTIONS_BUT_MISSING @description(\"Email mentions attachments/documents but none are provided\")\n  PROVIDED_BUT_NOT_MENTIONED @description(\"Attachments provided but not mentioned in email text\")\n}\n\n// ================================================================================================\n// ATTACHMENT ANALYSIS SCHEMAS  \n// ================================================================================================\n\nclass AttachmentInfo {\n  filename string @description(\"Name of the attachment file\")\n  fileType string @description(\"File extension or MIME type\")\n  isClaimRelated bool @description(\"Whether filename suggests claim-related content\")\n  documentType string @description(\"Inferred document type from filename\")\n}\n\nclass AttachmentAnalysis {\n  hasAttachments bool @description(\"Whether any attachments are provided\")\n  attachmentCount int @description(\"Total number of attachments\")\n  attachments AttachmentInfo[] @description(\"Details of each attachment\")\n  mentionsAttachments bool @description(\"Whether email text references attachments\")\n  attachmentMismatch AttachmentMismatchType @description(\"Mismatch between mentioned and provided attachments\")\n  suspiciousFilenames bool @description(\"Whether any filenames appear suspicious or non-business\")\n}\n\n// ================================================================================================\n// CLAIM INDICATORS SCHEMAS\n// ================================================================================================\n\nclass ClaimIndicators {\n  incidentKeywords string[] @description(\"Keywords suggesting an incident occurred\")\n  damageKeywords string[] @description(\"Keywords suggesting damage or loss\")\n  legalKeywords string[] @description(\"Keywords suggesting legal implications\")\n  medicalKeywords string[] @description(\"Keywords suggesting medical issues\")\n  timeIndicators string[] @description(\"Time-related phrases indicating when incident occurred\")\n  locationIndicators string[] @description(\"Location-related phrases indicating where incident occurred\")\n  partyIndicators string[] @description(\"References to other parties involved\")\n}\n\nclass RiskAssessment {\n  liabilityRisk \"low\" | \"medium\" | \"high\" | \"critical\"\n  financialRisk \"low\" | \"medium\" | \"high\" | \"critical\" \n  reputationalRisk \"low\" | \"medium\" | \"high\" | \"critical\"\n  regulatoryRisk \"low\" | \"medium\" | \"high\" | \"critical\"\n  overallRiskScore float @description(\"Overall risk score from 0.0 to 1.0\")\n}\n\n// ================================================================================================\n// MAIN CLASSIFICATION RESULT SCHEMA\n// ================================================================================================\n\nclass ZurichEmailClassificationResult {\n  // Primary Classification Decision\n  isClaimRelated bool @description(\"PRIMARY DECISION: Whether this email is insurance claim related\")\n  claimType ClaimType @description(\"Type of claim if claim-related\")\n  workflowAction \"PROCEED_TO_ZENDESK\" | \"IGNORE_EMAIL\" | \"REQUEST_ATTACHMENTS\" | \"HUMAN_REVIEW_REQUIRED\"\n  \n  // Confidence and Analysis\n  confidenceScore float @description(\"Confidence level from 0.0 to 1.0\")\n  urgencyLevel UrgencyLevel @description(\"Urgency level for processing priority\")\n  \n  // Canadian Context\n  canadianJurisdiction CanadianProvince @description(\"Relevant Canadian province/territory\")\n  \n  // Attachment Analysis\n  attachmentAnalysis AttachmentAnalysis @description(\"Comprehensive attachment analysis\")\n  \n  // Claim Analysis (if applicable)\n  claimIndicators ClaimIndicators? @description(\"Indicators that suggest this is a claim\")\n  riskAssessment RiskAssessment? @description(\"Risk assessment if claim-related\")\n  \n  // Reasoning and Next Steps  \n  classificationReasoning string @description(\"Detailed explanation of classification decision\")\n  recommendedNextSteps string[] @description(\"Recommended actions based on classification\")\n  flagsForHumanReview string[] @description(\"Specific items requiring human attention\")\n  \n  // Metadata\n  processingNotes string[] @description(\"Technical notes about the classification process\")\n  canadianLegalConsiderations string[] @description(\"Relevant Canadian legal factors identified\")\n}\n\n// ================================================================================================\n// INPUT SCHEMAS FOR N8N INTEGRATION\n// ================================================================================================\n\nclass EmailAttachment {\n  filename string @description(\"Name of the attachment file\")\n  contentType string @description(\"MIME type of the attachment\")\n  size int? @description(\"File size in bytes if available\")\n  // Note: Binary content not processed directly in this classification\n}\n\nclass EmailForClassification {\n  subject string @description(\"Email subject line\")\n  body string @description(\"Email body content (plain text or HTML)\")\n  senderEmail string @description(\"Sender's email address\")\n  senderName string? @description(\"Sender's display name if available\")\n  receivedDate string? @description(\"When email was received (ISO format)\")\n  attachments EmailAttachment[] @description(\"List of email attachments\")\n}\n\n// ================================================================================================\n// MAIN CLASSIFICATION FUNCTION\n// ================================================================================================\n\nfunction ClassifyZurichEmail(\n  emailData: EmailForClassification\n) -> ZurichEmailClassificationResult {\n  client \"openai/gpt-4o\"\n  prompt #\"\n    You are Zurich Insurance's expert email classification system for Canadian claims processing.\n    \n    CRITICAL TASK: Determine if this email is insurance claim-related and requires processing.\n    \n    EMAIL TO ANALYZE:\n    Subject: {{ emailData.subject }}\n    From: {{ emailData.senderEmail }} ({{ emailData.senderName }})\n    Body: {{ emailData.body }}\n    Attachments: {{ emailData.attachments }}\n    \n    CLASSIFICATION CRITERIA:\n    \n    1. CLAIM-RELATED INDICATORS:\n       - Incident descriptions (accidents, damage, injuries, theft, fire, water damage)\n       - Insurance terminology (policy, claim, coverage, deductible, premium)\n       - Legal language (liability, negligence, fault, damages, settlement)\n       - Loss descriptions (property damage, bodily injury, financial loss)\n       - Time/location of incidents\n       - References to other parties, witnesses, police reports\n       - Medical terminology (if injury claims)\n       \n    2. ATTACHMENT ANALYSIS:\n       - Check if email mentions attachments/documents but none provided\n       - Analyze attachment filenames for claim relevance\n       - Flag suspicious or non-business attachments\n       \n    3. CANADIAN CONTEXT:\n       - Identify relevant Canadian province/territory\n       - Consider provincial insurance regulations\n       - Flag Quebec-specific considerations (civil law vs common law)\n       \n    4. URGENCY ASSESSMENT:\n       - CRITICAL: Catastrophic incidents, fatalities, major property damage\n       - HIGH: Significant injuries, substantial property damage, legal threats\n       - MEDIUM: Standard claims, routine incidents\n       - LOW: Minor incidents, information requests\n       \n    5. WORKFLOW DECISIONS:\n       - PROCEED_TO_ZENDESK: Clear claim requiring processing\n       - IGNORE_EMAIL: Not claim-related (spam, marketing, general inquiries)\n       - REQUEST_ATTACHMENTS: Claims-related but mentions missing attachments\n       - HUMAN_REVIEW_REQUIRED: Unclear, complex, or high-risk situations\n    \n    6. RISK FACTORS TO CONSIDER:\n       - Multiple parties involved\n       - Potential litigation\n       - Regulatory compliance issues\n       - High financial exposure\n       - Media attention potential\n       - Previous claim history mentions\n       \n    SPECIAL EXIT POINTS:\n    - If email mentions \"see attached\", \"please find attached\", \"attachment\", \"document\" but NO attachments provided -> workflowAction: \"REQUEST_ATTACHMENTS\"\n    - If suspicious files or potential fraud indicators -> workflowAction: \"HUMAN_REVIEW_REQUIRED\"\n    - If extremely high value or complex legal issues -> workflowAction: \"HUMAN_REVIEW_REQUIRED\"\n    \n    CANADIAN LEGAL CONSIDERATIONS:\n    - Provincial jurisdiction differences\n    - Statute of limitations variations\n    - No-fault insurance provinces (Ontario, Quebec, etc.)\n    - Tort vs no-fault systems\n    - Regulatory requirements by province\n    \n    Provide comprehensive analysis with clear reasoning for your classification decision.\n    Focus on accuracy for Canadian insurance claims processing workflow.\n    \n    {{ ctx.output_format }}\n  \"#\n}\n\n// ================================================================================================\n// FALLBACK FUNCTION WITH GPT-4O-MINI\n// ================================================================================================\n\nfunction ClassifyZurichEmailFallback(\n  emailData: EmailForClassification\n) -> ZurichEmailClassificationResult {\n  client \"openai/gpt-4o-mini\"\n  prompt #\"\n    You are a backup email classifier for Zurich Insurance Canada.\n    \n    Analyze this email and determine if it's an insurance claim:\n    \n    Subject: {{ emailData.subject }}\n    From: {{ emailData.senderEmail }}\n    Body: {{ emailData.body }}\n    Attachments: {{ emailData.attachments }}\n    \n    Key decisions to make:\n    1. Is this claim-related? (look for incidents, damage, injuries, losses)\n    2. What type of claim? (auto, property, liability, etc.)\n    3. What's the urgency? (critical, high, medium, low)\n    4. Are attachments mentioned but missing?\n    5. Which Canadian province applies?\n    \n    Special cases:\n    - If mentions attachments but none provided -> REQUEST_ATTACHMENTS\n    - If unclear or high-risk -> HUMAN_REVIEW_REQUIRED\n    - If clearly not a claim -> IGNORE_EMAIL\n    - If valid claim -> PROCEED_TO_ZENDESK\n    \n    {{ ctx.output_format }}\n  \"#\n}\n\n// ================================================================================================\n// TEST CASES (COMMENTED OUT FOR BAML PLAYGROUND TESTING)\n// ================================================================================================\n\n// // TEST CASE 1: Clear Auto Claim\n// test AutoClaimTest {\n//   functions [ClassifyZurichEmail]\n//   args {\n//     emailData {\n//       subject: \"Car Accident - Need to File Claim - Policy #AC123456\"\n//       body: \"Hi, I was in a car accident yesterday on Highway 401 in Toronto. The other driver ran a red light and hit my vehicle. I have photos and a police report. My car has significant front-end damage. Please let me know what documents you need to process my claim.\"\n//       senderEmail: \"<EMAIL>\"\n//       senderName: \"John Doe\"\n//       attachments: [\n//         {filename: \"accident_photos.zip\", contentType: \"application/zip\", size: 2048000},\n//         {filename: \"police_report.pdf\", contentType: \"application/pdf\", size: 512000}\n//       ]\n//     }\n//   }\n// }\n\n// // TEST CASE 2: Mentions Attachments But None Provided\n// test MissingAttachmentsTest {\n//   functions [ClassifyZurichEmail]\n//   args {\n//     emailData {\n//       subject: \"Property Damage Claim\"\n//       body: \"Dear Zurich, my basement flooded last week due to a burst pipe. I have photos and repair estimates attached. Please see attached documents for damage assessment.\"\n//       senderEmail: \"<EMAIL>\"\n//       senderName: \"Jane Smith\"\n//       attachments: []\n//     }\n//   }\n// }\n\n// // TEST CASE 3: Not Claim Related\n// test NotClaimTest {\n//   functions [ClassifyZurichEmail]\n//   args {\n//     emailData {\n//       subject: \"Marketing Newsletter Subscription\"\n//       body: \"Hi, I would like to subscribe to your newsletter for insurance tips and updates. Please add me to your mailing list.\"\n//       senderEmail: \"<EMAIL>\"\n//       senderName: \"Marketing Team\"\n//       attachments: []\n//     }\n//   }\n// }\n\n// // TEST CASE 4: High-Risk Liability Claim\n// test HighRiskLiabilityTest {\n//   functions [ClassifyZurichEmail]\n//   args {\n//     emailData {\n//       subject: \"URGENT: Serious Injury at Construction Site\"\n//       body: \"This is to notify you of a serious workplace accident at our construction site in Calgary, Alberta. A worker fell from scaffolding and has been hospitalized with critical injuries. Legal counsel has been engaged. This incident may result in significant liability exposure.\"\n//       senderEmail: \"<EMAIL>\"\n//       senderName: \"Legal Department\"\n//       attachments: [\n//         {filename: \"incident_report.pdf\", contentType: \"application/pdf\", size: 1024000},\n//         {filename: \"witness_statements.docx\", contentType: \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\", size: 256000}\n//       ]\n//     }\n//   }\n// }\n\n// // TEST CASE 5: Quebec Civil Law Consideration\n// test QuebecClaimTest {\n//   functions [ClassifyZurichEmail]\n//   args {\n//     emailData {\n//       subject: \"Réclamation - Dommages à la propriété\"\n//       body: \"Bonjour, je souhaite déclarer un sinistre pour des dommages causés par la grêle à ma propriété à Montréal, Québec. Les dommages incluent le toit, les fenêtres et la voiture.\"\n//       senderEmail: \"<EMAIL>\"\n//       senderName: \"Claude Martin\"\n//       attachments: [\n//         {filename: \"photos_dommages.jpg\", contentType: \"image/jpeg\", size: 3072000}\n//       ]\n//     }\n//   }\n// } ",
    "generators.baml": "// This helps use auto generate libraries you can use in the language of\n// your choice. You can have multiple generators if you use multiple languages.\n// Just ensure that the output_dir is different for each generator.\ngenerator target {\n    // Valid values: \"python/pydantic\", \"typescript\", \"ruby/sorbet\", \"rest/openapi\"\n    output_type \"python/pydantic\"\n\n    // Where the generated code will be saved (relative to baml_src/)\n    output_dir \"../\"\n\n    // The version of the BAML package you have installed (e.g. same version as your baml-py or @boundaryml/baml).\n    // The BAML VSCode extension version should also match this version.\n    version \"0.90.2\"\n\n    // Valid values: \"sync\", \"async\"\n    // This controls what `b.FunctionName()` will be (sync or async).\n    default_client_mode sync\n}\n",
    "level01_analysis.baml": "// ================================================================================================\n// ZURICH LEVEL 01 ANALYSIS - BAML IMPLEMENTATION\n// ================================================================================================\n// Purpose: Comprehensive claim analysis with intelligent exit path routing\n// Integration: Follows classification -> Level 01 -> Level 02 workflow\n// Enhancement: Includes Spark NLP preprocessing capabilities for maximum accuracy\n// ================================================================================================\n\n// ================================================================================================\n// CORE ANALYSIS ENUMS\n// ================================================================================================\n\nenum ClaimTypeDetailed {\n  AUTO @description(\"Automotive insurance claims\")\n  PROPERTY @description(\"Property damage claims\")\n  LIABILITY @description(\"General liability claims\") \n  WORKERS_COMPENSATION @description(\"Workers compensation claims\")\n  MEDICAL_MALPRACTICE @description(\"Medical malpractice claims\")\n  PROFESSIONAL_LIABILITY @description(\"Professional liability claims\")\n  PRODUCT_LIABILITY @description(\"Product liability claims\")\n  CYBER_LIABILITY @description(\"Cyber security and data breach claims\")\n  ENVIRONMENTAL @description(\"Environmental damage claims\")\n  GENERAL @description(\"General insurance claims\")\n  UNKNOWN @description(\"Cannot determine claim type from available information\")\n}\n\nenum IncidentType {\n  COLLISION @description(\"Vehicle collision or impact\")\n  WEATHER @description(\"Weather-related damage\")\n  THEFT @description(\"Theft or burglary\")\n  FIRE @description(\"Fire damage\")\n  VANDALISM @description(\"Vandalism or malicious damage\")\n  MEDICAL_EVENT @description(\"Medical incident or malpractice\")\n  SLIP_FALL @description(\"Slip and fall incident\")\n  WORKPLACE_INJURY @description(\"Workplace injury or accident\")\n  WATER_DAMAGE @description(\"Water damage or flooding\")\n  CYBER_ATTACK @description(\"Cyber security incident\")\n  OTHER @description(\"Other type of incident\")\n}\n\nenum ContactRole {\n  CLAIMANT @description(\"Person making the claim\")\n  INSURED @description(\"Insured party on the policy\")\n  OTHER_DRIVER @description(\"Other driver in vehicle incident\")\n  WITNESS @description(\"Witness to the incident\")\n  PASSENGER @description(\"Passenger in vehicle\")\n  ADJUSTER @description(\"Insurance adjuster\")\n  LAWYER @description(\"Legal representative\")\n  POLICE_OFFICER @description(\"Police officer\")\n  MEDICAL_PROFESSIONAL @description(\"Doctor, nurse, or medical professional\")\n  PROPERTY_OWNER @description(\"Owner of damaged property\")\n  CONTRACTOR @description(\"Repair contractor or service provider\")\n  EXPERT_WITNESS @description(\"Expert witness or investigator\")\n}\n\nenum ExitPath {\n  DOCUMENTS_INSUFFICIENT @description(\"Missing critical documents, cannot proceed\")\n  POLICY_LOOKUP_NEEDED @description(\"Policy number found, need to lookup complete policy details\")\n  PROCEED_TO_LEVEL02 @description(\"Sufficient information to proceed to coverage analysis\")\n}\n\nenum PriorityLevel {\n  URGENT @description(\"Requires immediate attention\")\n  HIGH @description(\"High priority processing\")\n  NORMAL @description(\"Standard priority processing\")\n  LOW @description(\"Low priority processing\")\n}\n\n// ================================================================================================\n// CLAIM DETAILS SCHEMAS\n// ================================================================================================\n\nclass ClaimDetails {\n  incidentDate string? @description(\"Date of incident if found (YYYY-MM-DD format)\")\n  incidentTime string? @description(\"Time of incident if available (HH:MM format)\")\n  incidentLocation string? @description(\"Location where incident occurred\")\n  claimantName string? @description(\"Person making the claim\")\n  insuredParty string? @description(\"Insured person/entity on the policy\")\n  claimType ClaimTypeDetailed @description(\"Type of claim identified\")\n  damageDescription string? @description(\"Description of damages/losses\")\n  estimatedAmount string? @description(\"Estimated claim amount if mentioned\")\n  policeReportNumber string? @description(\"Police report number if available\")\n  emergencyServicesInvolved bool @description(\"Were emergency services called\")\n  injuriesReported bool @description(\"Were any injuries reported\")\n  propertyDamage bool @description(\"Is there property damage\")\n  witnessesPresent bool @description(\"Were witnesses present\")\n  vehicleInvolved string? @description(\"Vehicle details if auto claim\")\n  thirdPartyInvolved bool @description(\"Is there a third party involved\")\n  medicalInformation MedicalSummary? @description(\"Comprehensive medical information if injuries reported\")\n}\n\nclass PolicyDetails {\n  policyNumber string? @description(\"Insurance policy number (critical field)\")\n  policyHolder string? @description(\"Name of policy holder\")\n  insuredVehicle string? @description(\"Vehicle details if auto claim\")\n  effectiveDate string? @description(\"Policy effective date\")\n  expiryDate string? @description(\"Policy expiry date\")\n  coverageTypes string[] @description(\"Types of coverage mentioned\")\n  deductibleAmount string? @description(\"Deductible amount if mentioned\")\n  policyLimits string? @description(\"Policy limits if available\")\n  insuranceCompany string? @description(\"Insurance company name\")\n  agentDetails string? @description(\"Agent contact information\")\n  hasCompleteInformation bool @description(\"Whether we have complete policy info for processing\")\n}\n\nclass CauseOfLoss {\n  primaryCause string @description(\"Main cause of the loss/incident\")\n  contributingFactors string[] @description(\"Additional factors that contributed\")\n  incidentType IncidentType @description(\"Type of incident that occurred\")\n  atFaultParties string[] @description(\"Parties potentially at fault\")\n  circumstances string @description(\"Detailed circumstances of the incident\")\n  weatherConditions string? @description(\"Weather conditions at time of incident\")\n  roadConditions string? @description(\"Road conditions if applicable\")\n  trafficViolations string[] @description(\"Any traffic violations mentioned\")\n  negligenceFactors string[] @description(\"Potential negligence factors identified\")\n  causationChain string[] @description(\"Sequence of events leading to the incident\")\n}\n\nclass ContactInfo {\n  name string @description(\"Person's full name\")\n  role ContactRole @description(\"Role of this person in the claim\")\n  phoneNumber string? @description(\"Phone number\")\n  email string? @description(\"Email address\") \n  address string? @description(\"Physical address\")\n  driverLicense string? @description(\"Driver's license number if applicable\")\n  relationship string? @description(\"Relationship to the incident\")\n  insuranceInfo string? @description(\"Their insurance information if available\")\n  employer string? @description(\"Employer if relevant to claim\")\n  notes string? @description(\"Additional notes about this contact\")\n}\n\n// ================================================================================================\n// SPARK NLP ENHANCEMENT SCHEMAS\n// ================================================================================================\n\nclass SparkNlpEntity {\n  text string @description(\"The extracted entity text\")\n  entityType string @description(\"Type of entity (PERSON, ORG, DATE, MONEY, etc.)\")\n  confidence float @description(\"Confidence score for this entity\")\n  context string @description(\"Surrounding context where entity was found\")\n  startPosition int? @description(\"Character position where entity starts\")\n  endPosition int? @description(\"Character position where entity ends\")\n}\n\nclass SparkNlpFinancialEntity {\n  text string @description(\"The financial entity text\")\n  entityType \"MONETARY_AMOUNT\" | \"POLICY_NUMBER\" | \"CLAIM_NUMBER\" | \"PERCENTAGE\" | \"DEDUCTIBLE\" | \"COVERAGE_LIMIT\"\n  normalizedValue string? @description(\"Normalized value if applicable\")\n  confidence float @description(\"Confidence score for this entity\")\n  context string @description(\"Context where financial entity was found\")\n}\n\nclass SparkNlpDateEntity {\n  text string @description(\"The date text as found\")\n  normalizedDate string @description(\"Normalized date in YYYY-MM-DD format\")\n  dateType \"INCIDENT_DATE\" | \"POLICY_DATE\" | \"REPORT_DATE\" | \"EFFECTIVE_DATE\" | \"EXPIRY_DATE\" | \"OTHER\"\n  confidence float @description(\"Confidence score for this date entity\")\n  context string @description(\"Context where date was found\")\n}\n\nclass SparkNlpLocationEntity {\n  text string @description(\"The location text as found\")\n  addressType \"INCIDENT_LOCATION\" | \"MAILING_ADDRESS\" | \"BUSINESS_ADDRESS\" | \"PROPERTY_ADDRESS\" | \"OTHER\"\n  normalizedAddress string? @description(\"Normalized address if possible\")\n  confidence float @description(\"Confidence score for this location\")\n  context string @description(\"Context where location was found\")\n}\n\nclass SparkNlpEnhancedData {\n  enhancedEntities SparkNlpEntity[] @description(\"All entities found via Spark NLP\")\n  financialEntities SparkNlpFinancialEntity[] @description(\"Financial/insurance specific entities\")\n  extractedDates SparkNlpDateEntity[] @description(\"All dates found with context\")\n  locationEntities SparkNlpLocationEntity[] @description(\"Addresses and locations found\")\n  confidenceBoost float @description(\"How much Spark NLP improved extraction confidence (0.0-1.0)\")\n  processingNotes string[] @description(\"Notes about Spark NLP processing\")\n}\n\n// ================================================================================================\n// EXIT PATH ANALYSIS SCHEMAS\n// ================================================================================================\n\nclass ConfidenceBreakdown {\n  claimDetailsConfidence float @description(\"Confidence in claim details extraction (0.0-1.0)\")\n  policyDetailsConfidence float @description(\"Confidence in policy details extraction (0.0-1.0)\")\n  causeOfLossConfidence float @description(\"Confidence in cause of loss analysis (0.0-1.0)\")\n  contactDetailsConfidence float @description(\"Confidence in contact details extraction (0.0-1.0)\")\n  overallConfidence float @description(\"Overall analysis confidence (0.0-1.0)\")\n}\n\nclass NextSteps {\n  documentsNeeded string[] @description(\"Specific documents required to proceed\")\n  informationNeeded string[] @description(\"Specific information to request from claimant\")\n  contactsToReach string[] @description(\"People who need to be contacted\")\n  timelineForResponse string @description(\"How quickly response is needed\")\n  escalationRequired bool @description(\"Whether immediate escalation is needed\")\n  automatedActions string[] @description(\"Actions that can be automated\")\n  manualActions string[] @description(\"Actions requiring human intervention\")\n}\n\nclass AnalysisDetails {\n  extractedInformation string @description(\"Summary of all information extracted\")\n  confidenceBySection ConfidenceBreakdown @description(\"Confidence breakdown by analysis section\")\n  identifiedGaps string[] @description(\"Specific information gaps identified\")\n  assumptionsMade string[] @description(\"Any reasonable assumptions made in analysis\")\n  qualityAssessment string @description(\"Assessment of data quality and completeness\")\n  riskIndicators string[] @description(\"Potential risk factors identified\")\n}\n\nclass ExitAnalysis {\n  exitReason string @description(\"Detailed reason for choosing this exit path\")\n  analysisProvided AnalysisDetails @description(\"All analysis completed so far\")\n  nextStepsRequired NextSteps @description(\"What needs to happen next\")\n  estimatedTimeToResolution string @description(\"Estimated time to resolve missing information\")\n  priorityLevel PriorityLevel @description(\"Priority level for this case\")\n  automationOpportunities string[] @description(\"Opportunities for automated processing\")\n  humanReviewItems string[] @description(\"Items requiring human review\")\n}\n\n// ================================================================================================\n// MAIN LEVEL 01 ANALYSIS RESULT SCHEMA\n// ================================================================================================\n\nclass Level01Analysis {\n  // Core Extractions\n  claimDetails ClaimDetails @description(\"Comprehensive claim information\")\n  policyDetails PolicyDetails @description(\"Policy information extracted\")\n  causeOfLoss CauseOfLoss @description(\"Analysis of what caused the loss\")\n  contactDetails ContactInfo[] @description(\"All relevant contact information\")\n  \n  // Decision Logic Results\n  documentsSufficient bool @description(\"Are submitted documents sufficient for processing\")\n  policyNumberAvailable bool @description(\"Is policy number identified in documents\")\n  policyDetailsComplete bool @description(\"Are complete policy details available\")\n  \n  // Intelligent Exit Path\n  exitPath ExitPath @description(\"Determined next step in workflow\")\n  exitAnalysis ExitAnalysis @description(\"Detailed analysis for chosen exit path\")\n  \n  // Enhanced Analysis (Spark NLP Integration)\n  sparkNlpInsights SparkNlpEnhancedData? @description(\"Enhanced insights from Spark NLP preprocessing\")\n  \n  // Quality Metrics\n  confidenceScore float @description(\"Overall analysis confidence (0.0-1.0)\")\n  processingNotes string[] @description(\"Technical notes about the analysis process\")\n  dataQualityScore float @description(\"Quality of input data (0.0-1.0)\")\n  \n  // Canadian Legal Context\n  canadianJurisdiction string? @description(\"Relevant Canadian province/territory\")\n  legalConsiderations string[] @description(\"Canadian legal factors identified\")\n  regulatoryNotes string[] @description(\"Regulatory considerations\")\n  \n  // UI METADATA AND EXPLAINABILITY (NEW)\n  uiMetadata UIMetadata @description(\"UI metadata for frontend entity mapping and highlighting\")\n  explainabilityInsights ExplainabilityInsight[] @description(\"LIME/SHAP explainability analysis\")\n  \n  // Processing Metadata\n  analysisTimestamp string @description(\"When analysis was performed\")\n  modelVersion string @description(\"Version of analysis model used\")\n  processingTimeMs int @description(\"Time taken for analysis in milliseconds\")\n}\n\n// ================================================================================================\n// INPUT SCHEMAS\n// ================================================================================================\n\nclass ClaimDocumentInput {\n  claimId string @description(\"Unique claim identifier\")\n  emailContent string @description(\"Email body content\")\n  emailSubject string @description(\"Email subject line\")\n  attachmentNames string[] @description(\"Names of all attachments\")\n  attachmentsText string[] @description(\"Extracted text content from all attachments\")\n  preprocessingNotes string[] @description(\"Notes from document preprocessing\")\n}\n\n// ================================================================================================\n// MAIN LEVEL 01 ANALYSIS FUNCTION\n// ================================================================================================\n\nfunction AnalyzeClaimLevel01(\n  claimInput: ClaimDocumentInput\n) -> Level01Analysis {\n  client \"openai/gpt-4o\"\n  prompt #\"\n    You are Zurich Insurance's expert Level 01 claims analyst for Canadian insurance processing.\n    \n    MISSION: Perform comprehensive claim analysis and determine the appropriate next workflow step.\n    \n    CLAIM DATA TO ANALYZE:\n    Claim ID: {{ claimInput.claimId }}\n    Email Subject: {{ claimInput.emailSubject }}\n    Email Content: {{ claimInput.emailContent }}\n    \n    ATTACHMENTS ANALYZED:\n    {% for i in range(claimInput.attachmentNames|length) %}\n    File {{ i+1 }}: {{ claimInput.attachmentNames[i] }}\n    Content: {{ claimInput.attachmentsText[i] }}\n    \n    {% endfor %}\n    \n    COMPREHENSIVE ANALYSIS REQUIREMENTS:\n    \n    1. CLAIM DETAILS EXTRACTION:\n       - Incident date, time, and location (be specific)\n       - Claimant and insured party identification\n       - Claim type classification (auto, property, liability, etc.)\n       - Damage description and estimated amounts\n       - Police report numbers and emergency services involvement\n       - Injury reports and witness information\n       - Vehicle details for auto claims\n       - Third-party involvement assessment\n       \n    1a. MEDICAL INFORMATION EXTRACTION (if injuries reported):\n       - Specific injuries and body parts affected\n       - Injury severity and type (fracture, soft tissue, etc.)\n       - Treatment history (emergency care, physiotherapy, chiropractic, etc.)\n       - Diagnostic results (X-rays, MRI, ultrasound findings)\n       - Medical timeline (first treatment, duration, return to work)\n       - Healthcare providers and facilities involved\n       - Medical costs and future care needs\n       - Impact on daily life and work capacity\n       - Available and needed medical documentation\n    \n    2. POLICY DETAILS EXTRACTION (CRITICAL):\n       - Policy number identification (look for patterns: POL123456, P-123456, *********, etc.)\n       - Policy holder name verification\n       - Coverage types and limits mentioned\n       - Deductible amounts\n       - Policy effective and expiry dates\n       - Insurance company and agent details\n       - Assess completeness of policy information\n    \n    3. CAUSE OF LOSS ANALYSIS:\n       - Primary cause identification\n       - Contributing factors and circumstances\n       - At-fault party determination\n       - Weather and road conditions\n       - Traffic violations or negligence factors\n       - Sequence of events (causation chain)\n    \n    4. CONTACT DETAILS EXTRACTION:\n       - All parties involved with complete contact information\n       - Role classification (claimant, insured, other driver, witness, etc.)\n       - Driver's license numbers\n       - Insurance information for other parties\n       - Professional contacts (lawyers, adjusters, medical professionals)\n       - Relationships to the incident\n    \n    CRITICAL DECISION LOGIC - CHOOSE APPROPRIATE EXIT PATH:\n    \n    EXIT PATH 1 - \"DOCUMENTS_INSUFFICIENT\":\n    Choose this if:\n    - Critical documents are missing (police report, medical records, repair estimates)\n    - Information is too vague or incomplete to make coverage decisions\n    - No policy information is available\n    - Key details like incident date/location are unclear\n    \n    EXIT PATH 2 - \"POLICY_LOOKUP_NEEDED\":\n    Choose this if:\n    - Policy number is clearly identified\n    - Basic claim information is available\n    - But complete policy details (coverage types, limits, deductibles) are missing\n    - Need to lookup policy in system for coverage analysis\n    \n    EXIT PATH 3 - \"PROCEED_TO_LEVEL02\":\n    Choose this if:\n    - Sufficient documents AND complete policy information are available\n    - Claim details are comprehensive enough for coverage analysis\n    - Ready for liability assessment and coverage determination\n    \n    EXIT ANALYSIS REQUIREMENTS:\n    For whichever exit path you choose:\n    - Provide detailed reasoning for the decision\n    - List specific next steps required\n    - Identify automation opportunities\n    - Flag items requiring human review\n    - Estimate timeline for resolution\n    - Set appropriate priority level\n    \n    CANADIAN INSURANCE CONTEXT:\n    - Apply Canadian insurance terminology and regulations\n    - Consider provincial differences (especially Quebec civil law)\n    - Include relevant legal considerations\n    - Factor in comparative negligence rules\n    - Consider no-fault insurance implications where applicable\n    \n    CONFIDENCE SCORING:\n    Rate confidence for each section (0.0-1.0):\n    - How certain are you about claim details?\n    - How confident about policy information?\n    - How clear is the cause of loss?\n    - How complete are contact details?\n    - What's the overall data quality?\n    \n    QUALITY ASSESSMENT:\n    Evaluate:\n    - Completeness of information provided\n    - Quality and clarity of documentation\n    - Consistency across different sources\n    - Potential contradictions or red flags\n    - Missing critical information\n    \n    UI METADATA GENERATION (CRITICAL FOR EXPLAINABLE CLAIM ANALYSIS):\n    - Generate precise entity mappings for every extracted field\n    - Create document highlights with exact coordinates for key information\n    - Map each entity to its source document and position\n    - Assign appropriate colors based on entity type and confidence\n    - Include confidence breakdown for each analysis element\n    - Create visual analysis flow for claim processing decisions\n    \n    EXPLAINABILITY ANALYSIS (LIME/SHAP FOR CLAIM ANALYSIS):\n    - Generate SHAP-style feature importance for decision factors\n    - Identify top factors supporting/opposing each analysis decision\n    - Provide confidence contributors for analysis results\n    - Generate alternative scenarios with probability changes\n    - Create visualization data for interactive claim exploration\n    - Explain methodology used for claim analysis approach\n    \n    UI METADATA REQUIREMENTS:\n    - Every extracted field MUST have source document mapping\n    - Highlight coordinates MUST be provided for key evidence\n    - Color coding MUST reflect entity type and confidence levels\n    - Analysis flow MUST show logical progression of claim processing\n    - Confidence scores MUST be provided for all determinations\n    \n    EXPLAINABILITY REQUIREMENTS:\n    - Feature importance MUST explain analysis reasoning\n    - Alternative scenarios MUST show sensitivity of decisions\n    - Confidence contributors MUST be clearly documented\n    - Visualization data MUST enable interactive claim exploration\n    - Methodology notes MUST explain analysis approach\n    \n    Provide comprehensive analysis regardless of which exit path is chosen.\n    Be thorough but decisive in your routing decision.\n    \n    {{ ctx.output_format }}\n  \"#\n}\n\n// ================================================================================================\n// SPARK NLP PREPROCESSING FUNCTION (FOR FUTURE INTEGRATION)\n// ================================================================================================\n\nfunction EnhanceWithSparkNLP(\n  emailContent: string,\n  attachmentsText: string[]\n) -> SparkNlpEnhancedData {\n  client \"openai/gpt-4o-mini\"\n  prompt #\"\n    You are simulating Spark NLP entity extraction for insurance claim processing.\n    This is a placeholder function until actual Spark NLP integration is implemented.\n    \n    TEXT TO ANALYZE:\n    Email: {{ emailContent }}\n    Attachments: {{ attachmentsText }}\n    \n    Extract and categorize entities as Spark NLP would:\n    - People, organizations, locations\n    - Financial amounts and policy numbers  \n    - Dates and times\n    - Addresses and locations\n    \n    Focus on insurance-relevant entities and provide confidence scores.\n    \n    {{ ctx.output_format }}\n  \"#\n}\n\n// ================================================================================================\n// UI METADATA AND EXPLAINABILITY SCHEMAS (IMPORTED FROM ui_metadata_schemas.baml)\n// ================================================================================================\n\n// ================================================================================================\n// MEDICAL INFORMATION SCHEMAS (NEW)\n// ================================================================================================\n\nenum InjurySeverity {\n  MINOR @description(\"Minor injuries requiring minimal treatment\")\n  MODERATE @description(\"Moderate injuries requiring ongoing treatment\")\n  SEVERE @description(\"Severe injuries with long-term impact\")\n  CATASTROPHIC @description(\"Life-altering or permanent disabilities\")\n}\n\nenum TreatmentType {\n  EMERGENCY_CARE @description(\"Emergency department or urgent care\")\n  PHYSIOTHERAPY @description(\"Physical therapy treatment\")\n  CHIROPRACTIC @description(\"Chiropractic treatment\")\n  MASSAGE_THERAPY @description(\"Massage therapy\")\n  SURGICAL @description(\"Surgical intervention\")\n  MEDICATION @description(\"Prescription medication\")\n  DIAGNOSTIC_IMAGING @description(\"X-rays, MRI, CT scans, ultrasound\")\n  PSYCHOLOGICAL @description(\"Mental health or psychological treatment\")\n  OCCUPATIONAL_THERAPY @description(\"Occupational therapy\")\n  OTHER_MEDICAL @description(\"Other medical treatment\")\n}\n\nclass InjuryDetail {\n  bodyPart string @description(\"Specific body part injured (e.g., right shoulder, lower back)\")\n  injuryType string @description(\"Type of injury (e.g., fracture, soft tissue, tear, strain)\")\n  severity InjurySeverity @description(\"Severity level of the injury\")\n  description string @description(\"Detailed description of the injury\")\n  isPermanent bool @description(\"Whether injury has permanent implications\")\n  causationClear bool @description(\"Whether injury is clearly caused by incident\")\n  priorHistory bool @description(\"Whether there's evidence of prior injury to same area\")\n}\n\nclass TreatmentRecord {\n  treatmentType TreatmentType @description(\"Type of treatment provided\")\n  provider string @description(\"Healthcare provider or facility\")\n  treatmentDate string @description(\"Date of treatment (if available)\")\n  treatmentDetails string @description(\"Specific details of treatment provided\")\n  treatmentDuration string @description(\"Duration or frequency of treatment\")\n  treatmentCost string @description(\"Cost of treatment if mentioned\")\n  ongoing bool @description(\"Whether treatment is ongoing\")\n}\n\nclass DiagnosticResult {\n  diagnosticType string @description(\"Type of diagnostic test (X-ray, MRI, etc.)\")\n  findings string @description(\"Key findings from diagnostic test\")\n  datePerformed string @description(\"Date diagnostic was performed\")\n  facility string @description(\"Where diagnostic was performed\")\n  abnormalFindings bool @description(\"Whether abnormal findings were detected\")\n  supportsCausation bool @description(\"Whether findings support incident causation\")\n}\n\nclass MedicalTimeline {\n  incidentDate string @description(\"Date of incident\")\n  firstTreatmentDate string @description(\"Date of first medical treatment\")\n  emergencyTreatment bool @description(\"Whether emergency treatment was required\")\n  timeOffWork string @description(\"Duration of time off work due to injuries\")\n  returnToWorkDate string @description(\"Date returned to work (if applicable)\")\n  treatmentEndDate string @description(\"Expected or actual end of treatment\")\n  functionalLimitations string[] @description(\"Ongoing functional limitations\")\n}\n\nclass MedicalSummary {\n  injuriesReported bool @description(\"Were any injuries reported\")\n  injuryDetails InjuryDetail[] @description(\"Detailed injury information\")\n  treatmentRecords TreatmentRecord[] @description(\"Treatment history and records\")\n  diagnosticResults DiagnosticResult[] @description(\"Diagnostic test results\")\n  medicalTimeline MedicalTimeline @description(\"Timeline of medical events\")\n  medicalProfessionals ContactInfo[] @description(\"Medical professionals involved\")\n  medicalCosts string @description(\"Total or estimated medical costs\")\n  futureCareneeds string @description(\"Future care requirements\")\n  impactOnDailyLife string @description(\"Impact on claimant's daily activities\")\n  returnToWorkPrognosis string @description(\"Prognosis for return to work\")\n  medicalDocumentsAvailable string[] @description(\"Available medical documents\")\n  medicalDocumentsNeeded string[] @description(\"Medical documents still needed\")\n}\n",
    "level02_coverage_analysis.baml": "// ================================================================================================\n// ZURICH LEVEL 02 COVERAGE ANALYSIS - BAML IMPLEMENTATION  \n// ================================================================================================\n// Purpose: Comprehensive coverage determination with intelligent exit path routing\n// Integration: Follows Level 01 Analysis -> Level 02 Coverage -> Settlement/Denial workflow\n// Enhancement: Includes CanLII legal precedent lookup for Canadian insurance law\n// ================================================================================================\n\n// ================================================================================================\n// CORE COVERAGE DECISION ENUMS\n// ================================================================================================\n\nenum CoverageDecision {\n  NOT_COVERED @description(\"Loss is not covered under policy terms - denial with detailed justification\")\n  COVERED @description(\"Loss is covered under policy terms - proceed to settlement analysis\")\n  INFORMATION_REQUIRED @description(\"Insufficient information to determine coverage - request specific data\")\n}\n\nenum InformationSource {\n  CLAIMANT @description(\"Information must be obtained directly from the claimant\")\n  THIRD_PARTY @description(\"Information must be obtained from external third parties\")\n  INSURANCE_AGENT @description(\"Information can be obtained by insurance agent/adjuster\")\n  EXPERT_ASSESSMENT @description(\"Requires independent expert evaluation\")\n  LEGAL_COUNSEL @description(\"Requires legal counsel interpretation\")\n  REGULATORY_AUTHORITY @description(\"Requires input from regulatory authorities\")\n}\n\nenum CoverageReason {\n  POLICY_TERMS_CLEAR @description(\"Policy terms clearly cover this type of loss\")\n  LEGAL_PRECEDENT @description(\"Canadian legal precedent supports coverage\")\n  REGULATORY_REQUIREMENT @description(\"Provincial regulations mandate coverage\")\n  STANDARD_INTERPRETATION @description(\"Standard industry interpretation supports coverage\")\n  EXCLUSION_APPLIES @description(\"Policy exclusion clearly applies to this loss\")\n  OUTSIDE_POLICY_PERIOD @description(\"Loss occurred outside policy period\")\n  CONDITION_NOT_MET @description(\"Policy condition not satisfied\")\n  INSUFFICIENT_EVIDENCE @description(\"Insufficient evidence to determine coverage\")\n}\n\nenum RiskLevel {\n  LOW @description(\"Low risk coverage decision with clear policy language\")\n  MEDIUM @description(\"Medium risk decision requiring careful documentation\")\n  HIGH @description(\"High risk decision requiring human expert review\")\n  CRITICAL @description(\"Critical risk decision requiring legal counsel review\")\n}\n\n// ================================================================================================\n// CANADIAN LEGAL INTEGRATION SCHEMAS\n// ================================================================================================\n\nclass CanLIILegalPrecedent {\n  caseId string @description(\"CanLII case identifier\")\n  caseName string @description(\"Name of the legal case\")\n  court string @description(\"Court that decided the case\")\n  jurisdiction string @description(\"Canadian jurisdiction (province/federal)\")\n  decisionDate string @description(\"Date of court decision\")\n  relevanceToCase string @description(\"How this precedent applies to current claim\")\n  keyPrinciple string @description(\"Key legal principle established\")\n  supportsCoverage bool @description(\"Whether precedent supports or denies coverage\")\n  confidenceLevel float @description(\"Confidence in precedent applicability (0.0-1.0)\")\n  canliiUrl string @description(\"Direct URL to CanLII case\")\n}\n\nclass CanadianLegalAnalysis {\n  applicableLaw string @description(\"Primary Canadian law governing this claim type\")\n  provincialRegulations string[] @description(\"Relevant provincial insurance regulations\")\n  federalStatutes string[] @description(\"Applicable federal statutes\")\n  legalPrecedents CanLIILegalPrecedent[] @description(\"Relevant CanLII case law\")\n  regulatoryGuidance string[] @description(\"Relevant regulatory guidance documents\")\n  interpretationNotes string @description(\"Legal interpretation considerations\")\n  jurisdictionalCompliance bool @description(\"Whether claim meets jurisdictional requirements\")\n  legalRiskAssessment string @description(\"Assessment of legal risks in coverage decision\")\n}\n\n// ================================================================================================\n// POLICY ANALYSIS SCHEMAS\n// ================================================================================================\n\nclass PolicyCoverageAnalysis {\n  applicableCoverageTypes string[] @description(\"Policy coverage types that apply\")\n  coverageLimits string @description(\"Applicable coverage limits and sub-limits\")\n  deductibleAmount string @description(\"Applicable deductible amount\")\n  policyConditions string[] @description(\"Policy conditions that must be met\")\n  conditionsMet bool @description(\"Whether all policy conditions are satisfied\")\n  coverageInterpretation string @description(\"Interpretation of coverage language\")\n  ambiguousTerms string[] @description(\"Policy terms requiring interpretation\")\n  industryStandards string @description(\"Industry standard interpretation practices\")\n}\n\nclass ExclusionAnalysis {\n  potentialExclusions PolicyExclusion[] @description(\"All exclusions that could potentially apply\")\n  applicableExclusions PolicyExclusion[] @description(\"Exclusions that definitively apply\")\n  exclusionJustification string @description(\"Detailed justification for exclusion application\")\n  canadianExclusionPrecedents CanLIILegalPrecedent[] @description(\"Canadian court decisions on similar exclusions\")\n  exclusionInterpretation string @description(\"Legal interpretation of exclusion language\")\n  exclusionRisk RiskLevel @description(\"Risk level of exclusion application\")\n}\n\nclass PolicyExclusion {\n  exclusionType string @description(\"Type of policy exclusion\")\n  exclusionText string @description(\"Exact text of the exclusion from policy\")\n  applicabilityReason string @description(\"Why this exclusion applies to the claim\")\n  legalBasis string @description(\"Legal basis for exclusion application\")\n  precedentSupport string[] @description(\"Legal precedents supporting exclusion\")\n  interpretationChallenges string[] @description(\"Challenges in interpreting this exclusion\")\n}\n\n// ================================================================================================\n// CAUSE AND COVERAGE MAPPING SCHEMAS\n// ================================================================================================\n\nclass CauseOfLossMapping {\n  primaryCause string @description(\"Primary cause of loss from Level 01\")\n  proximateCause string @description(\"Legal proximate cause determination\")\n  coverageApplicability string @description(\"How cause maps to policy coverage\")\n  causationChain string[] @description(\"Sequence of events leading to loss\")\n  concurrentCauses string[] @description(\"Multiple causes contributing to loss\")\n  causationAnalysis string @description(\"Legal analysis of causation\")\n  canadianCausationLaw string @description(\"Canadian legal principles on causation\")\n}\n\nclass CoverageMapping {\n  causeOfLoss string @description(\"Identified cause of loss\")\n  policySection string @description(\"Relevant policy section providing coverage\")\n  coverageRationale string @description(\"Rationale for coverage determination\")\n  coverageStrength float @description(\"Strength of coverage argument (0.0-1.0)\")\n  counterArguments string[] @description(\"Arguments against coverage\")\n  supportingArguments string[] @description(\"Arguments supporting coverage\")\n  industryPractice string @description(\"Standard industry practice for similar claims\")\n}\n\n// ================================================================================================\n// INFORMATION REQUIREMENTS SCHEMAS\n// ================================================================================================\n\nclass InformationRequest {\n  informationType string @description(\"Type of information needed\")\n  informationSource InformationSource @description(\"Who should provide this information\")\n  informationDetails string @description(\"Specific details of what is needed\")\n  justification string @description(\"Why this information is necessary for coverage decision\")\n  urgencyLevel \"IMMEDIATE\" | \"HIGH\" | \"NORMAL\" | \"LOW\" @description(\"Urgency of obtaining this information\")\n  timelineRequired string @description(\"Timeline for obtaining this information\")\n  alternativeSources string[] @description(\"Alternative sources if primary source unavailable\")\n  impactOnDecision string @description(\"How this information affects coverage decision\")\n}\n\nclass ClaimantInformationNeeded {\n  documentsRequired string[] @description(\"Specific documents needed from claimant\")\n  clarificationsNeeded string[] @description(\"Clarifications needed about the incident\")\n  evidenceRequired string[] @description(\"Additional evidence needed from claimant\")\n  timelineForResponse string @description(\"Timeline for claimant to provide information\")\n  consequencesOfNonCompliance string @description(\"What happens if claimant doesn't provide info\")\n  assistanceAvailable string @description(\"How insurance company can assist claimant\")\n}\n\nclass ThirdPartyInformationNeeded {\n  thirdPartyType string @description(\"Type of third party (witness, expert, authority, etc.)\")\n  thirdPartyContact string @description(\"Contact information if available\")\n  informationNeeded string @description(\"Specific information required from third party\")\n  legalBasisForRequest string @description(\"Legal basis for requesting information\")\n  voluntaryVsMandatory \"VOLUNTARY\" | \"SUBPOENA_REQUIRED\" | \"COURT_ORDER_NEEDED\" @description(\"How information can be obtained\")\n  costImplications string @description(\"Cost implications of obtaining this information\")\n  alternativeApproaches string[] @description(\"Alternative approaches if third party uncooperative\")\n}\n\nclass AgentObtainableInformation {\n  informationType string @description(\"Type of information agent can obtain\")\n  sourceOfInformation string @description(\"Where agent can obtain this information\")\n  procedureRequired string @description(\"Procedure agent must follow\")\n  timelineToObtain string @description(\"Expected timeline for agent to obtain information\")\n  costToCompany string @description(\"Cost implications for insurance company\")\n  reliabilityLevel float @description(\"Reliability of this information source (0.0-1.0)\")\n}\n\n// ================================================================================================\n// COVERAGE DECISION SCHEMAS\n// ================================================================================================\n\nclass CoverageJustification {\n  primaryReason CoverageReason @description(\"Primary reason for coverage decision\")\n  detailedReasoning string @description(\"Comprehensive explanation of coverage decision\")\n  policyBasis string @description(\"Specific policy language supporting decision\")\n  legalBasis string @description(\"Legal basis for coverage decision\")\n  factualBasis string @description(\"Factual basis supporting decision\")\n  industryPractice string @description(\"Industry practice supporting decision\")\n  precedentSupport string[] @description(\"Legal precedents supporting decision\")\n  riskFactors string[] @description(\"Risk factors considered in decision\")\n  alternativeInterpretations string[] @description(\"Alternative interpretations considered\")\n  decisionStrength float @description(\"Strength of coverage decision (0.0-1.0)\")\n}\n\nclass CoverageDecisionSupport {\n  supportingEvidence SupportingEvidence[] @description(\"Evidence supporting the coverage decision\")\n  contradictoryEvidence ContradictoryEvidence[] @description(\"Evidence that contradicts coverage\")\n  evidenceWeighting EvidenceWeighting @description(\"How different evidence was weighted\")\n  expertOpinions ExpertOpinion[] @description(\"Expert opinions obtained\")\n  documentationQuality DocumentationQuality @description(\"Quality of supporting documentation\")\n  evidenceGaps string[] @description(\"Identified gaps in evidence\")\n}\n\nclass SupportingEvidence {\n  evidenceType string @description(\"Type of evidence (document, witness, expert opinion, etc.)\")\n  evidenceDescription string @description(\"Description of the evidence\")\n  evidenceSource string @description(\"Source of the evidence\")\n  evidenceStrength float @description(\"Strength of this evidence (0.0-1.0)\")\n  evidenceReliability float @description(\"Reliability of this evidence (0.0-1.0)\")\n  evidenceImpact string @description(\"Impact of this evidence on coverage decision\")\n}\n\nclass ContradictoryEvidence {\n  evidenceType string @description(\"Type of contradictory evidence\")\n  evidenceDescription string @description(\"Description of contradictory evidence\")\n  contradictionReason string @description(\"Why this evidence contradicts coverage\")\n  responseToContradiction string @description(\"How this contradiction is addressed\")\n  contradictionWeight float @description(\"Weight of this contradictory evidence (0.0-1.0)\")\n  mitigationStrategy string @description(\"Strategy for addressing this contradiction\")\n}\n\nclass EvidenceWeighting {\n  documentaryEvidence float @description(\"Weight given to documentary evidence\")\n  witnessTestimony float @description(\"Weight given to witness testimony\")\n  expertOpinion float @description(\"Weight given to expert opinions\")\n  physicalEvidence float @description(\"Weight given to physical evidence\")\n  circumstantialEvidence float @description(\"Weight given to circumstantial evidence\")\n  weightingRationale string @description(\"Rationale for evidence weighting approach\")\n}\n\nclass ExpertOpinion {\n  expertType string @description(\"Type of expert (legal, technical, medical, etc.)\")\n  expertCredentials string @description(\"Expert's credentials and qualifications\")\n  opinionSummary string @description(\"Summary of expert's opinion\")\n  opinionBasis string @description(\"Basis for expert's opinion\")\n  opinionReliability float @description(\"Reliability of expert opinion (0.0-1.0)\")\n  opinionImpact string @description(\"Impact of opinion on coverage decision\")\n  conflictingOpinions string[] @description(\"Any conflicting expert opinions\")\n}\n\nclass DocumentationQuality {\n  completeness float @description(\"Completeness of documentation (0.0-1.0)\")\n  reliability float @description(\"Reliability of documentation (0.0-1.0)\")\n  consistency float @description(\"Consistency across documents (0.0-1.0)\")\n  timeliness float @description(\"Timeliness of documentation (0.0-1.0)\")\n  authentication float @description(\"Authentication level of documents (0.0-1.0)\")\n  qualityNotes string @description(\"Notes on documentation quality issues\")\n}\n\n// ================================================================================================\n// EXIT PATH ANALYSIS SCHEMAS\n// ================================================================================================\n\nclass Level02ExitAnalysis {\n  exitPath CoverageDecision @description(\"Chosen exit path\")\n  exitReason string @description(\"Detailed reason for choosing this exit path\")\n  coverageAnalysisProvided CoverageAnalysisDetails @description(\"Coverage analysis completed\")\n  nextStepsRequired Level02NextSteps @description(\"What needs to happen next\")\n  riskAssessment Level02RiskAssessment @description(\"Risk assessment for this decision\")\n  humanReviewRequired bool @description(\"Whether human expert review is required\")\n  legalCounselRequired bool @description(\"Whether legal counsel review is required\")\n  timelineForResolution string @description(\"Expected timeline for final resolution\")\n  priorityLevel PriorityLevel @description(\"Priority level for handling this claim\")\n  escalationTriggers string[] @description(\"Factors that would require escalation\")\n}\n\nclass CoverageAnalysisDetails {\n  analysisCompleteness float @description(\"Completeness of coverage analysis (0.0-1.0)\")\n  analysisConfidence float @description(\"Confidence in analysis conclusions (0.0-1.0)\")\n  keyFindings string[] @description(\"Key findings from coverage analysis\")\n  analysisLimitations string[] @description(\"Limitations in the analysis\")\n  assumptionsMade string[] @description(\"Assumptions made during analysis\")\n  uncertaintyAreas string[] @description(\"Areas of uncertainty in analysis\")\n  additionalAnalysisNeeded string[] @description(\"Additional analysis that may be needed\")\n}\n\nclass Level02NextSteps {\n  immediateActions string[] @description(\"Actions that must be taken immediately\")\n  shortTermActions string[] @description(\"Actions needed within 1-7 days\")\n  longTermActions string[] @description(\"Actions needed within 30 days\")\n  documentationRequired DocumentationAction[] @description(\"Documentation that must be created\")\n  communicationRequired CommunicationAction[] @description(\"Communications that must be sent\")\n  investigationRequired InvestigationAction[] @description(\"Investigations that must be conducted\")\n  legalActionRequired LegalAction[] @description(\"Legal actions that may be required\")\n}\n\nclass DocumentationAction {\n  documentType string @description(\"Type of document to create\")\n  documentPurpose string @description(\"Purpose of the document\")\n  documentRecipient string @description(\"Who will receive the document\")\n  documentTimeline string @description(\"When document must be completed\")\n  documentImportance \"CRITICAL\" | \"HIGH\" | \"NORMAL\" | \"LOW\" @description(\"Importance level\")\n}\n\nclass CommunicationAction {\n  communicationType string @description(\"Type of communication (email, letter, phone, meeting)\")\n  communicationRecipient string @description(\"Who to communicate with\")\n  communicationPurpose string @description(\"Purpose of the communication\")\n  communicationTimeline string @description(\"When communication must occur\")\n  communicationMethod string @description(\"Preferred method of communication\")\n  followUpRequired bool @description(\"Whether follow-up is required\")\n}\n\nclass InvestigationAction {\n  investigationType string @description(\"Type of investigation required\")\n  investigationScope string @description(\"Scope of the investigation\")\n  investigationTimeline string @description(\"Timeline for investigation\")\n  investigationResources string @description(\"Resources required for investigation\")\n  investigationCost string @description(\"Estimated cost of investigation\")\n  investigationExpectedOutcome string @description(\"Expected outcome of investigation\")\n}\n\nclass LegalAction {\n  legalActionType string @description(\"Type of legal action\")\n  legalBasis string @description(\"Legal basis for the action\")\n  legalTimeline string @description(\"Timeline for legal action\")\n  legalCost string @description(\"Estimated cost of legal action\")\n  legalRisk string @description(\"Legal risks associated with action\")\n  alternativesToLegalAction string[] @description(\"Alternatives to legal action\")\n}\n\nclass Level02RiskAssessment {\n  coverageRisk RiskLevel @description(\"Risk level of coverage decision\")\n  legalRisk RiskLevel @description(\"Legal risk level\")\n  financialRisk RiskLevel @description(\"Financial risk level\")\n  reputationalRisk RiskLevel @description(\"Reputational risk level\")\n  regulatoryRisk RiskLevel @description(\"Regulatory compliance risk level\")\n  overallRisk RiskLevel @description(\"Overall risk level\")\n  riskMitigationStrategies string[] @description(\"Strategies to mitigate identified risks\")\n  riskMonitoringRequired bool @description(\"Whether ongoing risk monitoring is required\")\n}\n\n// ================================================================================================\n// MAIN LEVEL 02 COVERAGE ANALYSIS RESULT SCHEMA\n// ================================================================================================\n\nclass Level02CoverageAnalysis {\n  // Primary Coverage Decision\n  coverageDecision CoverageDecision @description(\"Final coverage determination\")\n  confidenceScore float @description(\"Overall analysis confidence (0.0-1.0)\")\n  \n  // Core Analysis Components\n  policyAnalysis PolicyCoverageAnalysis @description(\"Detailed policy coverage analysis\")\n  exclusionAnalysis ExclusionAnalysis @description(\"Policy exclusions assessment\")\n  coverageMapping CoverageMapping @description(\"Cause-to-coverage mapping analysis\")\n  causeMapping CauseOfLossMapping @description(\"Legal causation analysis\")\n  \n  // Decision Support and Justification\n  coverageJustification CoverageJustification @description(\"Detailed coverage decision justification\")\n  decisionSupport CoverageDecisionSupport @description(\"Evidence and support for decision\")\n  \n  // Canadian Legal Context\n  canadianLegalAnalysis CanadianLegalAnalysis @description(\"Canadian legal and regulatory analysis\")\n  \n  // Medical Impact Assessment (for injury claims)\n  medicalImpactAssessment MedicalImpactAssessment? @description(\"Medical impact assessment for injury claims\")\n  \n  // Information Requirements (for INFORMATION_REQUIRED exit)\n  claimantInformation ClaimantInformationNeeded? @description(\"Information needed from claimant\")\n  thirdPartyInformation ThirdPartyInformationNeeded[] @description(\"Information needed from third parties\")\n  agentInformation AgentObtainableInformation[] @description(\"Information agent can obtain\")\n  allInformationRequests InformationRequest[] @description(\"All information requests categorized\")\n  \n  // Exit Path Analysis\n  exitAnalysis Level02ExitAnalysis @description(\"Detailed analysis for chosen exit path\")\n  \n  // Quality and Risk Metrics\n  analysisQuality AnalysisQuality @description(\"Quality assessment of the analysis\")\n  riskAssessment Level02RiskAssessment @description(\"Comprehensive risk assessment\")\n  uncertaintyAreas UncertaintyArea[] @description(\"Areas requiring further clarification\")\n  \n  // UI METADATA AND EXPLAINABILITY (NEW)\n  uiMetadata UIMetadata @description(\"UI metadata for frontend entity mapping and highlighting\")\n  explainabilityInsights ExplainabilityInsight[] @description(\"LIME/SHAP explainability analysis for coverage decisions\")\n  \n  // Processing Metadata\n  level01Data Level01Summary @description(\"Summary of Level 01 analysis used\")\n  analysisTimestamp string @description(\"When Level 02 analysis was performed\")\n  processingTimeMs int @description(\"Time taken for analysis in milliseconds\")\n  modelVersion string @description(\"Analysis model version used\")\n  analystId string @description(\"ID of analyst (human or AI) performing analysis\")\n}\n\n// ================================================================================================\n// SUPPORTING SCHEMAS\n// ================================================================================================\n\nclass AnalysisQuality {\n  dataCompleteness float @description(\"Completeness of input data (0.0-1.0)\")\n  dataReliability float @description(\"Reliability of input data (0.0-1.0)\")\n  analysisDepth float @description(\"Depth of analysis performed (0.0-1.0)\")\n  analysisConsistency float @description(\"Consistency of analysis (0.0-1.0)\")\n  expertValidation float @description(\"Level of expert validation (0.0-1.0)\")\n  overallQuality float @description(\"Overall analysis quality score (0.0-1.0)\")\n  qualityNotes string[] @description(\"Notes on quality issues or strengths\")\n}\n\nclass UncertaintyArea {\n  uncertaintyType string @description(\"Type of uncertainty\")\n  uncertaintyDescription string @description(\"Description of the uncertainty\")\n  uncertaintyImpact string @description(\"Impact of uncertainty on coverage decision\")\n  resolutionApproach string @description(\"Approach to resolve uncertainty\")\n  resolutionTimeline string @description(\"Timeline for resolving uncertainty\")\n  uncertaintyRisk RiskLevel @description(\"Risk level associated with this uncertainty\")\n}\n\nclass Level01Summary {\n  claimId string @description(\"Claim ID from Level 01\")\n  claimType string @description(\"Type of claim from Level 01\")\n  policyNumber string @description(\"Policy number identified in Level 01\")\n  incidentDate string @description(\"Incident date from Level 01\")\n  primaryCause string @description(\"Primary cause of loss from Level 01\")\n  level01Confidence float @description(\"Confidence score from Level 01\")\n  level01ExitPath string @description(\"Exit path from Level 01 analysis\")\n  keyFindings string[] @description(\"Key findings from Level 01 that affect coverage\")\n}\n\n// ================================================================================================\n// INPUT SCHEMA FOR LEVEL 02 ANALYSIS\n// ================================================================================================\n\nclass Level02AnalysisInput {\n  claimId string @description(\"Unique claim identifier\")\n  level01Analysis Level01Summary @description(\"Complete Level 01 analysis results\")\n  policyDocuments string[] @description(\"Policy document texts (if available)\")\n  additionalEvidence string[] @description(\"Additional evidence texts\")\n  humanInputs string[] @description(\"Manual clarifications or inputs\")\n  processingNotes string[] @description(\"Processing metadata and notes\")\n  urgencyLevel \"IMMEDIATE\" | \"HIGH\" | \"NORMAL\" | \"LOW\" @description(\"Processing urgency\")\n  specialInstructions string[] @description(\"Special handling instructions\")\n}\n\n// ================================================================================================\n// MAIN LEVEL 02 ANALYSIS FUNCTION\n// ================================================================================================\n\nfunction AnalyzeCoverageLevel02(\n  claimId: string,\n  claimType: string,\n  policyNumber: string,\n  incidentDate: string,\n  primaryCause: string,\n  level01Confidence: float,\n  level01ExitPath: string,\n  keyFindings: string[],\n  policyDocuments: string,\n  additionalEvidence: string,\n  canadianJurisdiction: string\n) -> Level02CoverageAnalysis {\n  client CustomGPT4o\n  prompt #\"\nsystem: You are Zurich Insurance's expert Level 02 Coverage Analyst specializing in Canadian insurance law and policy interpretation.\n\nMISSION: Determine coverage for the claim using comprehensive policy analysis, Canadian legal precedents, and detailed justification.\n\n⚠️ ACCURACY REQUIREMENTS:\n\n1. POLICY EXCLUSIONS:\n   - Only cite exclusions that are explicitly provided in the policy documents\n   - If no policy document is provided, state \"Policy document not available for exclusion review\"\n   - Mark any exclusion analysis as \"REQUIRES_POLICY_REVIEW\" if no policy text provided\n\n2. LEGAL PRECEDENTS:\n   - Focus on well-established Canadian legal principles\n   - If you don't have access to specific precedents, state \"Legal research required - specific precedent database not available\"\n   - Use general legal principles rather than specific case citations unless you're certain of accuracy\n\n3. POLICY LANGUAGE:\n   - Base analysis on policy language explicitly provided in the input\n   - If policy terms are missing, clearly indicate \"Policy language not available\"\n\n4. FACTUAL CLAIMS:\n   - Base analysis on facts provided in the Level 01 analysis and evidence\n   - Clearly distinguish between \"facts provided\" and \"assumptions made\"\n\n5. CONFIDENCE SCORING:\n   - Adjust confidence scores based on completeness of available information\n   - Factor in missing information when calculating confidence\n\nLEVEL 01 ANALYSIS RESULTS:\nClaim ID: {{ claimId }}\nClaim Type: {{ claimType }}\nPolicy Number: {{ policyNumber }}\nPrimary Cause: {{ primaryCause }}\nIncident Date: {{ incidentDate }}\nLevel 01 Confidence: {{ level01Confidence }}\nKey Level 01 Findings: {{ keyFindings }}\n\nADDITIONAL EVIDENCE:\n{{ additionalEvidence }}\n\nPOLICY DOCUMENTS:\n{{ policyDocuments }}\n\nCANADIAN JURISDICTION: {{ canadianJurisdiction }}\n\n⚠️ MEDICAL ANALYSIS REQUIREMENTS (if injuries reported):\n\nIf Level 01 indicates injuries, perform comprehensive medical impact assessment:\n\n1. INJURY SEVERITY EVALUATION:\n   - Assess injury severity based on Level 01 medical information\n   - Consider treatment complexity and duration\n   - Evaluate functional impairment and disability potential\n\n2. MEDICAL CAUSATION ANALYSIS:\n   - Assess clarity of medical causation to incident\n   - Identify any pre-existing conditions that may complicate claims\n   - Evaluate quality of medical documentation\n\n3. COVERAGE IMPLICATIONS:\n   - Consider impact on policy limits adequacy\n   - Assess need for expert medical opinions\n   - Factor medical complexity into coverage confidence\n   - Evaluate return-to-work likelihood and financial impact\n\n4. FUTURE CARE ASSESSMENT:\n   - Estimate future care costs based on injury severity\n   - Consider long-term disability implications\n   - Assess permanent impairment potential\n\nBase medical analysis on injury details, treatment records, diagnostic results, and medical timeline from Level 01 analysis.\n\n🎯 CRITICAL: UI METADATA & EXPLAINABILITY REQUIREMENTS\n\nFor EVERY coverage decision and policy analysis, provide complete UI metadata:\n\n1. ENTITY MAPPING FOR COVERAGE ANALYSIS:\n   - Field name (coverage_decision, policy_limits, exclusions_applied, etc.)\n   - Extracted/analyzed values with source attribution\n   - Original policy language or document text\n   - Source document name and location\n   - Decision reasoning with confidence scores\n   - Appropriate UI highlight colors based on decision type\n\n2. COVERAGE DECISION EXPLAINABILITY (LIME/SHAP STYLE):\n   - Feature importance analysis: What factors most influenced coverage decision?\n   - Policy language impact: How did specific policy terms affect the decision?\n   - Legal precedent influence: Weight of legal factors in decision\n   - Medical factors: If injury claim, how medical info influenced coverage\n   - Risk assessment factors: What increased/decreased confidence\n   - Alternative scenarios: What would change the coverage decision\n\n3. DOCUMENT HIGHLIGHTS FOR POLICY ANALYSIS:\n   - Create highlighting regions for policy terms used\n   - Map exclusions to their exact policy language\n   - Link coverage provisions to decision reasoning\n   - Highlight legal precedents or regulatory factors\n\n4. CONFIDENCE BREAKDOWN:\n   - Per-field confidence with detailed reasoning\n   - Policy completeness impact on confidence\n   - Legal clarity impact on confidence\n   - Medical information quality impact (if applicable)\n   - Overall decision confidence factors\n\n5. VISUALIZATION DATA:\n   - Coverage flow charts showing decision logic\n   - Feature importance charts for coverage factors\n   - Policy provision impact analysis\n   - Risk assessment visualizations\n   - Medical severity impact charts (if applicable)\n\nUI COLOR SCHEME FOR LEVEL 02:\nCoverage Decision Colors:\n- COVERED: #4CAF50 (green)\n- NOT_COVERED: #F44336 (red)\n- INFORMATION_REQUIRED: #FF9800 (orange)\n- POLICY_LANGUAGE: #2196F3 (blue)\n- EXCLUSIONS: #E91E63 (pink)\n- LEGAL_PRECEDENT: #9C27B0 (purple)\n- MEDICAL_FACTORS: #795548 (brown)\n\nEXPLAINABILITY DEPTH FOR COVERAGE DECISIONS:\n\nFor each coverage determination, analyze:\n- Which policy provisions were most important?\n- How did legal precedents influence the decision?\n- What medical factors affected coverage (if applicable)?\n- How does evidence quality impact confidence?\n- What additional information would improve confidence?\n- What alternative interpretations were considered?\n\nALTERNATIVE SCENARIO ANALYSIS:\n- Different policy interpretations\n- Impact of additional evidence\n- Regulatory changes that could affect decision\n- Medical prognosis variations (if applicable)\n- Legal precedent changes\n\n{{ ctx.output_format }}\n\"#\n}\n\n// ================================================================================================\n// CANLII LEGAL RESEARCH FUNCTION (FUTURE INTEGRATION)\n// ================================================================================================\n\nfunction ResearchCanadianLegalPrecedents(\n  claimType: string,\n  causeOfLoss: string,\n  policyLanguage: string,\n  jurisdiction: string\n) -> CanadianLegalAnalysis {\n  client \"openai/gpt-4o-mini\"\n  prompt #\"\n    You are simulating CanLII legal research for insurance coverage analysis.\n    This function will be enhanced with actual CanLII API integration.\n    \n    RESEARCH PARAMETERS:\n    Claim Type: {{ claimType }}\n    Cause of Loss: {{ causeOfLoss }}\n    Policy Language: {{ policyLanguage }}\n    Jurisdiction: {{ jurisdiction }}\n    \n    Research Canadian legal precedents relevant to this insurance coverage question.\n    Focus on coverage interpretation, exclusion application, and policy construction principles.\n    \n    Provide realistic legal analysis based on Canadian insurance law principles.\n    \n    {{ ctx.output_format }}\n  \"#\n}\n\n// ================================================================================================\n// MEDICAL IMPACT ANALYSIS FOR COVERAGE (NEW)\n// ================================================================================================\n\nenum MedicalComplexity {\n  LOW @description(\"Minor injuries, straightforward treatment\")\n  MODERATE @description(\"Moderate injuries requiring ongoing care\")\n  HIGH @description(\"Severe injuries with complex treatment needs\")\n  CATASTROPHIC @description(\"Life-altering injuries requiring long-term care\")\n}\n\nclass MedicalImpactAssessment {\n  injurySeverityLevel MedicalComplexity @description(\"Overall injury severity level\")\n  treatmentComplexity MedicalComplexity @description(\"Complexity of required treatment\")\n  functionalImpairment string @description(\"Level of functional impairment\")\n  returnToWorkLikelihood \"full_return\" | \"modified_duties\" | \"partial_return\" | \"unlikely\" @description(\"Likelihood of return to work\")\n  futureCareCosts string @description(\"Estimated future care costs\")\n  permanentDisability bool @description(\"Whether permanent disability is likely\")\n  medicalCausationClear bool @description(\"Whether medical causation is clear\")\n  preExistingConditions string[] @description(\"Any identified pre-existing conditions\")\n  medicalDocumentationQuality \"excellent\" | \"good\" | \"adequate\" | \"poor\" @description(\"Quality of medical documentation\")\n  expertMedicalOpinionNeeded bool @description(\"Whether expert medical opinion is required\")\n}\n",
    "level03_fault_analysis.baml": "// ================================================================================================\n// ZURICH LEVEL 03 FAULT DETERMINATION - AI-ENHANCED RULE APPLICATION\n// ================================================================================================\n// Purpose: AI extracts liability factors from comprehensive data sources, then applies Canadian law\n// Hybrid Approach: AI Analysis → Structured Extraction → Rule-Based Canadian Fault Determination\n// Data Sources: Level 1, Level 2, Email Content, SparkNLP insights, OCR text\n// ================================================================================================\n\n// ================================================================================================\n// LEVEL 03 FAULT ANALYSIS INPUT\n// ================================================================================================\n\nclass Level03AnalysisInput {\n  claimReference string @description(\"Unique claim identifier\")\n  province string @description(\"Canadian province where incident occurred\")\n  \n  // Comprehensive data sources\n  level01Analysis string @description(\"Complete Level 1 analysis results as JSON string\")\n  level02Coverage string @description(\"Level 2 coverage analysis as JSON string\")\n  emailContent string[] @description(\"Original email content and communications\")\n  sparkNlpInsights string @description(\"SparkNLP extracted entities and insights as JSON\")\n  ocrTexts string[] @description(\"OCR extracted text from documents\")\n  attachmentDetails string[] @description(\"Document names and descriptions\")\n}\n\n// ================================================================================================\n// FAULT ANALYSIS OUTPUT SCHEMAS\n// ================================================================================================\n\nclass LiabilityFactorExtraction {\n  accidentClassification AccidentClassification @description(\"Precise accident type and circumstances\")\n  negligenceFactors NegligenceAnalysis @description(\"Detailed negligence factor analysis\")\n  contributoryFactors ContributoryAnalysis @description(\"Contributory negligence assessment\")\n  evidenceQuality EvidenceQuality @description(\"Quality and reliability of evidence\")\n  circumstanceDetails CircumstanceDetails @description(\"Detailed incident circumstances\")\n  structuredForRules StructuredCircumstances @description(\"Data organized for Canadian rule application\")\n  injuryImpactAnalysis InjuryImpactAnalysis? @description(\"Analysis of injury severity impact on liability assessment\")\n  faultGuidance FaultGuidance @description(\"AI guidance for fault percentage distribution\")\n  \n  // UI METADATA AND EXPLAINABILITY (NEW)\n  uiMetadata UIMetadata @description(\"UI metadata for frontend entity mapping and highlighting\")\n  explainabilityInsights ExplainabilityInsight[] @description(\"LIME/SHAP explainability analysis for fault determination\")\n}\n\nclass AccidentClassification {\n  primaryType \"auto_collision\" | \"slip_fall\" | \"occupiers_liability\" | \"general_liability\" | \"product_liability\" @description(\"Primary accident category\")\n  specificSubtype string @description(\"Specific subtype (e.g., wet_floor, rear_end_standard, etc.)\")\n  incidentComplexity \"simple\" | \"moderate\" | \"complex\" | \"multi_party\" @description(\"Complexity level for fault determination\")\n  applicableLaw string @description(\"Primary Canadian law/regulation that applies\")\n  certaintyLevel float @description(\"Confidence in accident classification (0.0-1.0)\")\n}\n\nclass NegligenceAnalysis {\n  propertyOwnerFactors string[] @description(\"Property owner negligence factors identified\")\n  vehicleOperatorFactors string[] @description(\"Vehicle operator negligence factors\") \n  thirdPartyFactors string[] @description(\"Third party negligence factors\")\n  institutionalFactors string[] @description(\"Institutional/corporate negligence\")\n  maintenanceFailures string[] @description(\"Specific maintenance failures identified\")\n  warningDeficiencies string[] @description(\"Missing or inadequate warnings\")\n  dutyOfCareBreaches string[] @description(\"Clear breaches of duty of care\")\n  statutoryViolations string[] @description(\"Violations of specific laws/regulations\")\n}\n\nclass ContributoryAnalysis {\n  claimantFactors string[] @description(\"Claimant contributory factors\")\n  awarenessLevel \"fully_aware\" | \"should_have_known\" | \"unaware\" | \"distracted\" @description(\"Hazard awareness level\")\n  avoidabilityFactor \"easily_avoidable\" | \"avoidable_with_care\" | \"difficult_to_avoid\" | \"unavoidable\" @description(\"Whether incident was avoidable\")\n  behaviorFactors string[] @description(\"Claimant behavior contributing to incident\")\n  mitigatingCircumstances string[] @description(\"Circumstances reducing claimant fault\")\n  aggravatingCircumstances string[] @description(\"Circumstances increasing claimant fault\")\n  contributionPercentage float @description(\"Estimated contribution percentage (0.0-100.0)\")\n}\n\nclass EvidenceQuality {\n  witnessStatements string[] @description(\"Quality of witness statements\")\n  documentaryEvidence string[] @description(\"Document and photo evidence quality\")\n  physicalEvidence string[] @description(\"Physical/forensic evidence available\")\n  expertOpinions string[] @description(\"Expert opinions and assessments\")\n  overallReliability float @description(\"Overall evidence reliability (0.0-1.0)\")\n  evidenceGaps string[] @description(\"Missing evidence that would be helpful\")\n}\n\nclass CircumstanceDetails {\n  environmentalFactors EnvironmentalFactors @description(\"Environmental conditions\")\n  locationDetails string[] @description(\"Location-specific factors\")\n  timingFactors string[] @description(\"Time-related factors affecting incident\")\n  humanBehaviorFactors string[] @description(\"Human behavior factors\")\n  equipmentConditions string[] @description(\"Equipment/mechanical factors if applicable\")\n}\n\nclass EnvironmentalFactors {\n  weatherConditions string @description(\"Weather at time of incident\")\n  lightingConditions string @description(\"Lighting conditions\")\n  surfaceConditions string @description(\"Surface/ground conditions\")\n  visibilityFactors string[] @description(\"Factors affecting visibility\")\n  crowdingLevel \"empty\" | \"light\" | \"moderate\" | \"heavy\" | \"overcrowded\" @description(\"Area crowding\")\n}\n\nclass StructuredCircumstances {\n  // For Auto accidents\n  trafficViolations string[] @description(\"Specific traffic violations\")\n  rightOfWayFactors string[] @description(\"Right-of-way considerations\")\n  vehicleConditions string[] @description(\"Vehicle condition factors\")\n  roadConditions string[] @description(\"Road and traffic conditions\")\n  \n  // For Premises liability\n  visitorStatus \"invitee\" | \"licensee\" | \"trespasser\" | \"employee\" @description(\"Legal visitor status\")\n  locationType \"commercial\" | \"residential\" | \"industrial\" | \"public\" | \"recreational\" @description(\"Property type\")\n  hazardType string @description(\"Specific hazard classification\")\n  warningsPosted bool @description(\"Whether adequate warnings were posted\")\n  mitigationEfforts string[] @description(\"Property owner mitigation efforts\")\n  \n  // For General liability\n  dutyOfCareLevel \"low\" | \"standard\" | \"high\" | \"statutory\" @description(\"Applicable duty of care standard\")\n  breachFactors string[] @description(\"Specific duty breaches\")\n  causationChain string[] @description(\"Causal sequence of events\")\n  \n  // Common factors\n  contributingFactors string[] @description(\"All contributing factors\")\n  mitigatingFactors string[] @description(\"All mitigating factors\")\n  atFaultParties string[] @description(\"Identified at-fault parties\")\n}\n\nclass FaultGuidance {\n  suggestedPrimaryFault float @description(\"Suggested primary party fault percentage\")\n  suggestedSecondaryFault float @description(\"Suggested secondary party fault percentage\")  \n  faultRationale string @description(\"Reasoning for fault distribution\")\n  uncertaintyAreas string[] @description(\"Areas requiring human review\")\n  comparisonCases string[] @description(\"Similar cases for reference\")\n  recommendedRuleSet string @description(\"Which Canadian rule set to apply\")\n  confidenceInGuidance float @description(\"Confidence in fault guidance (0.0-1.0)\")\n}\n\nclass InjuryImpactAnalysis {\n  injurySeverityFactor \"minor\" | \"moderate\" | \"severe\" | \"catastrophic\" @description(\"Overall injury severity affecting liability\")\n  medicalCausationCertainty float @description(\"Certainty that injuries caused by incident (0.0-1.0)\")\n  treatmentComplexity \"simple\" | \"moderate\" | \"complex\" | \"ongoing_care\" @description(\"Complexity of required treatment\")\n  functionalImpact \"minimal\" | \"moderate\" | \"significant\" | \"life_altering\" @description(\"Impact on claimant's daily function\")\n  workImpact \"no_time_off\" | \"short_absence\" | \"extended_absence\" | \"permanent_disability\" @description(\"Impact on ability to work\")\n  recoveryPrognosis \"full_recovery\" | \"partial_recovery\" | \"ongoing_limitations\" | \"permanent_impairment\" @description(\"Expected recovery outcome\")\n  preExistingFactors string[] @description(\"Pre-existing conditions affecting injury assessment\")\n  medicalDocumentationQuality float @description(\"Quality of medical evidence (0.0-1.0)\")\n  liabilityAggravationFactor float @description(\"How injury severity affects liability assessment (0.5-2.0)\")\n}\n\n// ================================================================================================\n// LEVEL 03 FAULT DETERMINATION FUNCTION\n// ================================================================================================\n\nfunction ExtractLevel03FaultFactors(input: Level03AnalysisInput) -> LiabilityFactorExtraction {\n  client \"openai/gpt-4o\"\n  prompt #\"\n    You are a Canadian liability insurance expert analyzing comprehensive claim data to extract precise fault determination factors.\n    \n    Your task: Analyze ALL available data sources and extract structured liability factors for Canadian rule-based fault determination.\n    \n    Data Sources Available:\n    - Claim Reference: {{ input.claimReference }}\n    - Province: {{ input.province }}\n    - Level 1 Analysis: {{ input.level01Analysis }}\n    - Level 2 Coverage: {{ input.level02Coverage }}\n    - Email Content: {{ input.emailContent }}\n    - SparkNLP Insights: {{ input.sparkNlpInsights }}\n    - OCR Text: {{ input.ocrTexts }}\n    - Attachments: {{ input.attachmentDetails }}\n    \n    CRITICAL REQUIREMENTS:\n    1. COMPREHENSIVE ANALYSIS: Consider ALL data sources thoroughly\n    2. CANADIAN LAW FOCUS: Apply knowledge of provincial regulations and common law\n    3. PRECISE EXTRACTION: Extract specific, factual details that impact liability\n    4. EVIDENCE-BASED: Only include factors supported by available evidence\n    5. RULE-READY OUTPUT: Structure data for Canadian fault determination rules\n    \n    KEY ANALYSIS AREAS:\n    \n    A) ACCIDENT CLASSIFICATION:\n    - Determine precise accident type from all evidence\n    - Identify applicable Canadian law/regulation\n    - Assess incident complexity for fault determination\n    \n    B) NEGLIGENCE ANALYSIS:\n    - Extract specific negligence factors for each party\n    - Identify duty of care breaches\n    - Assess maintenance failures, warning deficiencies\n    - Document statutory/regulatory violations\n    \n    C) CONTRIBUTORY FACTORS:\n    - Assess claimant awareness and avoidability\n    - Identify behavior and experience factors\n    - Determine mitigating vs aggravating circumstances\n    \n    D) EVIDENCE QUALITY:\n    - Evaluate witness, documentary, physical evidence\n    - Assess overall reliability for fault determination\n    - Identify evidence gaps\n    \n    E) ENVIRONMENTAL CIRCUMSTANCES:\n    - Extract weather, lighting, surface conditions\n    - Identify visibility and crowding factors\n    - Assess timing and location factors\n    \n    F) STRUCTURED FOR CANADIAN RULES:\n    - Organize data for immediate application to provincial fault rules\n    - Determine visitor status for premises liability\n    - Identify traffic violations for auto accidents\n    - Structure duty of care factors for general liability\n    \n    G) INJURY IMPACT ANALYSIS (if injuries reported):\n    - Assess injury severity and impact on liability assessment\n    - Evaluate medical causation certainty\n    - Consider how injury severity affects fault allocation\n    - Assess impact of treatment complexity and recovery prognosis\n    - Factor pre-existing conditions into liability analysis\n    - Evaluate quality of medical documentation\n    \n    H) FAULT GUIDANCE:\n    - Provide preliminary fault percentage recommendations\n    - Explain reasoning based on Canadian legal principles\n    - Identify areas requiring expert human review\n    - Reference similar cases where applicable\n    - Factor injury severity into fault percentage recommendations\n    \n    SPECIAL ATTENTION TO:\n    - SparkNLP entity extraction for precise location/person details\n    - Email communications revealing negligence or liability factors\n    - OCR text from incident reports, photos, official documents\n    - Level 2 risk assessment insights and coverage implications\n    - Canadian jurisdictional requirements and provincial variations\n    \n    FAULT FACTOR PRIORITIES (highest to lowest weight):\n    1. Statutory/regulatory violations \n    2. Clear duty of care breaches \n    3. Industry standard violations\n    4. Maintenance and warning failures\n    5. Contributory negligence factors\n    6. Environmental and circumstantial factors\n    \n    CANADIAN PROVINCIAL CONSIDERATIONS:\n    - Ontario: Regulation 668/90, Occupiers' Liability Act\n    - BC: ICBC rules, Motor Vehicle Act, Occupiers Liability Act\n    - Alberta: Traffic Safety Act, Occupiers' Liability Act\n    - Quebec: Civil Code provisions, no-fault considerations\n    - Other provinces: Apply relevant motor vehicle acts and common law\n    \n    I) UI METADATA GENERATION (CRITICAL FOR EXPLAINABLE FAULT DETERMINATION):\n    - Generate precise entity mappings for every fault factor extracted\n    - Create document highlights with exact coordinates for fault evidence\n    - Map each fault factor to its source document and position\n    - Assign appropriate colors based on fault factor type and severity\n    - Include confidence breakdown for each fault determination element\n    - Create visual decision flow for fault determination process\n    \n    J) EXPLAINABILITY ANALYSIS (LIME/SHAP FOR FAULT DETERMINATION):\n    - Generate SHAP-style feature importance for fault factors\n    - Identify top factors increasing/decreasing fault percentage\n    - Provide confidence contributors for fault determination\n    - Generate alternative fault scenarios with probability changes\n    - Create visualization data for interactive fault exploration\n    - Include Canadian legal factors affecting fault determination\n    - Explain methodology used for fault percentage calculation\n    \n    UI METADATA REQUIREMENTS:\n    - Every fault factor MUST have source document mapping\n    - Highlight coordinates MUST be provided for key evidence\n    - Color coding MUST reflect fault factor type and severity\n    - Decision flow MUST show logical progression of fault analysis\n    - Confidence scores MUST be provided for all determinations\n    \n    EXPLAINABILITY REQUIREMENTS:\n    - Feature importance MUST explain fault percentage reasoning\n    - Alternative scenarios MUST show sensitivity of fault determination\n    - Canadian legal factors MUST be integrated into explainability\n    - Visualization data MUST enable interactive fault exploration\n    - Methodology notes MUST explain fault determination approach\n    \n    Return comprehensive extraction ready for Canadian fault determination rule application with complete UI metadata and explainability.\n    \n    {{ ctx.output_format }}\n  \"#\n} ",
    "level04_quantum_calculation.baml": "// ================================================================================================\n// ZURICH LEVEL 04 QUANTUM CALCULATION - AI-ENHANCED DAMAGE ASSESSMENT (RESILIENT VERSION)\n// ================================================================================================\n// Purpose: AI extracts comprehensive damage details from all data sources for accurate quantum calculation\n// Hybrid Approach: AI Analysis → Structured Extraction → Rule-Based Canadian Quantum Calculation\n// Data Sources: Level 1, Level 2, Level 3, Email Content, SparkNLP insights, OCR text\n// RESILIENT: All fields optional with defaults to handle incomplete data gracefully\n// ================================================================================================\n\n// ================================================================================================\n// ONTARIO QUANTUM CALCULATION BENCHMARKS\n// ================================================================================================\n\nclass OntarioQuantumBenchmarks {\n  painAndSufferingBenchmarks OntarioPainSufferingGuide @description(\"Ontario pain and suffering benchmarks\")\n  medicalCostGuidelines OntarioMedicalGuidelines @description(\"Ontario medical cost assessment guidelines\")\n  incomeLossStandards OntarioIncomeLossStandards @description(\"Ontario income loss calculation standards\")\n  statutoryThresholds OntarioStatutoryThresholds @description(\"Ontario statutory thresholds and deductibles\")\n}\n\nclass OntarioPainSufferingGuide {\n  minorInjuryRange string @description(\"Minor soft tissue: $5,000 - $15,000\")\n  moderateInjuryRange string @description(\"Moderate injuries with some impairment: $15,000 - $45,000\")\n  severeInjuryRange string @description(\"Severe injuries with significant impairment: $45,000 - $150,000\")\n  catastrophicInjuryRange string @description(\"Catastrophic injuries: $150,000 - $420,000 (Andrews cap)\")\n  slipFallBenchmarks string @description(\"Typical slip-fall settlements: $10,000 - $50,000 for moderate cases\")\n  andrewsCap2025 float @description(\"2025 Andrews cap: $420,000\")\n  deductible2025 float @description(\"2025 pain and suffering deductible: $45,239\")\n  conservativeApproach string @description(\"Apply conservative estimates for human review and settlement\")\n}\n\nclass OntarioMedicalGuidelines {\n  emergencyCareTypical string @description(\"Emergency care: $500 - $2,000 typical range\")\n  physiotherapySessionCost float @description(\"Physiotherapy: $80 - $150 per session\")\n  chiropracticSessionCost float @description(\"Chiropractic: $60 - $120 per session\")\n  diagnosticImagingCosts string @description(\"X-ray: $100-200, MRI: $800-1200, Ultrasound: $200-400\")\n  maximumTreatmentDuration string @description(\"Moderate injuries: 6-12 months treatment typical\")\n  documentationRequirement string @description(\"All costs must be supported by receipts or medical invoices\")\n  evidenceBasedLimits string @description(\"CRITICAL: Only include costs with documentary evidence - do not project speculative future costs\")\n  moderateInjuryMedicalCap float @description(\"Moderate slip-fall injuries: $2,000-$5,000 typical total medical costs\")\n  ageAdjustedRecovery string @description(\"Claimants over 60: Apply conservative recovery assumptions and reduced future treatment projections\")\n}\n\nclass OntarioIncomeLossStandards {\n  averageWeeklyEarnings string @description(\"Use actual pre-accident earnings or provincial average\")\n  maximumBenefitPeriod string @description(\"Income replacement benefits: maximum periods apply\")\n  returnToWorkAssumption string @description(\"Assume return to work unless permanent disability proven\")\n  ageFactors string @description(\"Consider age for future income loss projections\")\n  documentationRequired string @description(\"Employment records and medical work capacity assessments required\")\n  ageSpecificGuidelines string @description(\"CRITICAL: Claimants over 60 - apply reduced future income projections considering proximity to retirement\")\n  evidenceBasedCalculations string @description(\"Base ALL income loss on documented employment records and actual wage statements - no speculative projections\")\n  conservativeApproach string @description(\"For moderate injuries, assume return to full capacity within 6-12 months unless medical evidence proves otherwise\")\n}\n\nclass OntarioStatutoryThresholds {\n  motorVehicleThreshold string @description(\"Motor vehicle: permanent serious impairment threshold\")\n  nonEconomicLossDeductible float @description(\"Non-economic loss deductible: $45,239 (2025)\")\n  andrewsCapAmount float @description(\"Andrews cap: $420,000 (2025)\")\n  catastrophicThreshold string @description(\"Catastrophic impairment threshold definitions\")\n  faultReductionRules string @description(\"Comparative negligence reduction applies to all damages\")\n}\n\n// ================================================================================================\n// LEVEL 04 QUANTUM ANALYSIS INPUT\n// ================================================================================================\n\nclass Level04AnalysisInput {\n  claimReference string @description(\"Unique claim identifier\")\n  province string @description(\"Canadian province for quantum calculation rules\")\n  \n  // Comprehensive data sources\n  level01Analysis string @description(\"Complete Level 1 analysis results as JSON string\")\n  level02Coverage string @description(\"Level 2 coverage analysis as JSON string\")\n  level03Fault string @description(\"Level 3 fault determination as JSON string\")\n  emailContent string[] @description(\"Original email content and communications\")\n  sparkNlpInsights string @description(\"SparkNLP extracted entities and insights as JSON\")\n  ocrTexts string[] @description(\"OCR extracted text from documents\")\n  attachmentDetails string[] @description(\"Document names and descriptions\")\n}\n\n// ================================================================================================\n// QUANTUM CALCULATION OUTPUT SCHEMAS\n// ================================================================================================\n\nclass QuantumDamageExtraction {\n  medicalDamages MedicalDamageAssessment @description(\"Comprehensive medical damage assessment\")\n  incomeLossDamages IncomeLossAssessment @description(\"Income loss and earning capacity assessment\")\n  careAndAssistanceCosts CareAssistanceAssessment @description(\"Care and assistance requirements\")\n  specialDamages SpecialDamagesBreakdown @description(\"Out-of-pocket and special damages\")\n  generalDamages GeneralDamagesAssessment @description(\"Pain and suffering assessment\")\n  futureCareCosts FutureCareAssessment @description(\"Future care and treatment costs\")\n  faultImpactAnalysis FaultImpactOnQuantum @description(\"How fault allocation affects quantum calculation\")\n  quantumGuidance QuantumCalculationGuidance @description(\"AI guidance for quantum calculation\")\n  ontarioBenchmarks OntarioQuantumBenchmarks @description(\"Ontario-specific benchmarks applied\")\n  validationChecks QuantumValidationChecks @description(\"Validation against overvaluation\")\n  \n  // UI METADATA AND EXPLAINABILITY (NEW)\n  uiMetadata UIMetadata @description(\"UI metadata for frontend entity mapping and highlighting\")\n  explainabilityInsights ExplainabilityInsight[] @description(\"LIME/SHAP explainability analysis for quantum calculations\")\n}\n\nclass MedicalDamageAssessment {\n  emergencyCareCosts float @description(\"Emergency department and initial treatment costs\")\n  ongoingTreatmentCosts float @description(\"Physiotherapy, chiropractic, and ongoing care costs\")\n  diagnosticCosts float @description(\"X-rays, MRI, ultrasound, and diagnostic test costs\")\n  medicationCosts float @description(\"Prescription and over-the-counter medication costs\")\n  medicalEquipmentCosts float @description(\"Medical aids, braces, mobility equipment costs\")\n  treatmentSessions int @description(\"Number of documented treatment sessions\")\n  treatmentDuration string @description(\"Duration of treatment period (e.g., '8 weeks', '6 months')\")\n  medicalProviders string[] @description(\"Healthcare providers and facilities involved\")\n  medicalComplexity \"simple\" | \"moderate\" | \"complex\" | \"severe\" | \"unknown\" @description(\"Complexity of medical treatment required\")\n  documentedMedicalCosts float @description(\"Total documented medical expenses\")\n  estimatedFutureMedical float @description(\"Estimated future medical costs\")\n  medicalCostCertainty float @description(\"Confidence in medical cost assessment (0.0-1.0)\")\n  currentTreatmentStatus \"ongoing\" | \"completed\" | \"planned\" | \"unknown\" @description(\"Current treatment status\")\n  recoveryPrognosis \"excellent\" | \"good\" | \"fair\" | \"poor\" | \"unknown\" @description(\"Recovery prognosis\")\n}\n\nclass IncomeLossAssessment {\n  preAccidentIncome float @description(\"Pre-accident annual income\")\n  employmentStatus \"employed\" | \"self_employed\" | \"unemployed\" | \"student\" | \"retired\" | \"unknown\" @description(\"Employment status at time of accident\")\n  jobTitle string @description(\"Job title or occupation\")\n  timeOffWork string @description(\"Period unable to work (e.g., '8 weeks', '3 months')\")\n  workDaysLost int @description(\"Number of work days lost\")\n  wageReplacementAmount float @description(\"Income replacement benefits received\")\n  returnToWorkStatus \"full_capacity\" | \"reduced_capacity\" | \"modified_duties\" | \"unable_to_return\" @description(\"Return to work status\")\n  futureEarningImpact \"no_impact\" | \"temporary_reduction\" | \"permanent_reduction\" | \"total_disability\" @description(\"Impact on future earning capacity\")\n  documentedIncomeLoss float @description(\"Documented income loss amount\")\n  projectedFutureIncomeLoss float @description(\"Projected future income loss\")\n  incomeLossCertainty float @description(\"Confidence in income loss assessment (0.0-1.0)\")\n  returnToWorkCapacity float @description(\"Return to work capacity (0.0-1.0)\")\n  ageFactorAdjustment \"none\" | \"minor\" | \"significant\" | \"unknown\" @description(\"Age factor adjustment for future income\")\n}\n\nclass CareAssistanceAssessment {\n  familyCareProvided bool @description(\"Whether family provided care assistance\")\n  familyCareHours float @description(\"Hours of family care assistance provided\")\n  familyCareValue float @description(\"Economic value of family care assistance\")\n  professionalCareRequired bool @description(\"Whether professional care assistance required\")\n  professionalCareHours float @description(\"Hours of professional care required\")\n  professionalCareCosts float @description(\"Cost of professional care assistance\")\n  housekeepingAssistance bool @description(\"Whether housekeeping assistance required\")\n  housekeepingCosts float @description(\"Cost of housekeeping assistance\")\n  personalCareNeeds string[] @description(\"Specific personal care needs identified\")\n  careAssistanceDuration string @description(\"Duration care assistance was/will be needed\")\n  totalCareAssistanceCosts float @description(\"Total documented care assistance costs\")\n}\n\nclass SpecialDamagesBreakdown {\n  transportationCosts float @description(\"Medical transportation and travel costs\")\n  accommodationCosts float @description(\"Accommodation costs for medical treatment\")\n  prescriptionCosts float @description(\"Prescription medication costs\")\n  medicalSuppliesCosts float @description(\"Medical supplies and equipment costs\")\n  parkingFees float @description(\"Hospital and medical facility parking fees\")\n  lostBenefits float @description(\"Lost employment benefits and perks\")\n  otherOutOfPocketCosts float @description(\"Other documented out-of-pocket expenses\")\n  outOfPocketReceipts string[] @description(\"Types of receipts and documentation available\")\n  specialDamagesTotal float @description(\"Total special damages amount\")\n  specialDamagesCertainty float @description(\"Confidence in special damages assessment (0.0-1.0)\")\n}\n\nclass GeneralDamagesAssessment {\n  painLevel \"minimal\" | \"mild\" | \"moderate\" | \"severe\" | \"extreme\" @description(\"Level of pain experienced\")\n  sufferingLevel \"minimal\" | \"mild\" | \"moderate\" | \"severe\" | \"extreme\" @description(\"Level of suffering experienced\")\n  functionalImpairment \"none\" | \"minimal\" | \"moderate\" | \"significant\" | \"severe\" | \"unknown\" @description(\"Functional impairment level\")\n  lifestyleImpact \"none\" | \"minimal\" | \"moderate\" | \"significant\" | \"severe\" | \"unknown\" @description(\"Impact on lifestyle and activities\")\n  psychologicalImpact \"none\" | \"minimal\" | \"moderate\" | \"significant\" | \"severe\" | \"unknown\" @description(\"Psychological impact and distress\")\n  relationshipImpact \"none\" | \"minimal\" | \"moderate\" | \"significant\" | \"severe\" | \"unknown\" @description(\"Impact on personal relationships\")\n  ageAtTimeOfAccident int @description(\"Claimant's age at time of accident\")\n  genderConsiderations string @description(\"Gender-specific considerations for quantum assessment\")\n  permanentDisabilityFactor float @description(\"Permanent disability factor (0.0-1.0)\")\n  comparableAwards string[] @description(\"References to comparable awards or precedents\")\n  generalDamagesRange string @description(\"Estimated general damages range\")\n  recommendedGeneralDamages float @description(\"Recommended general damages amount\")\n  ontarioBenchmarkCategory \"minor\" | \"moderate\" | \"severe\" | \"catastrophic\" | \"unknown\" @description(\"Ontario benchmark category for this case\")\n  benchmarkJustification string @description(\"Justification for chosen benchmark category\")\n  conservativeEstimate bool @description(\"Whether this is a conservative estimate for settlement purposes\")\n}\n\nclass FutureCareAssessment {\n  futureMedicalTreatment bool @description(\"Whether future medical treatment is required\")\n  futureTreatmentCosts float @description(\"Estimated future medical treatment costs\")\n  futureTherapyCosts float @description(\"Estimated future therapy and rehabilitation costs\")\n  futureCarePeriod string @description(\"Period over which future care will be required\")\n  assistiveDevices string[] @description(\"Future assistive devices or equipment needed\")\n  assistiveDeviceCosts float @description(\"Estimated costs of assistive devices\")\n  homeModifications string[] @description(\"Required home modifications for accessibility\")\n  homeModificationCosts float @description(\"Estimated costs of home modifications\")\n  attendantCareRequired bool @description(\"Whether future attendant care is required\")\n  attendantCareCosts float @description(\"Estimated future attendant care costs\")\n  totalFutureCareCosts float @description(\"Total estimated future care costs\")\n  futureCareUncertainty float @description(\"Uncertainty level in future care assessment (0.0-1.0)\")\n}\n\nclass FaultImpactOnQuantum {\n  claimantFaultPercentage float @description(\"Claimant's fault percentage from Level 3\")\n  faultReductionApplicable bool @description(\"Whether fault reduction applies to damages\")\n  specialDamagesReduction float @description(\"Reduction applied to special damages\")\n  generalDamagesReduction float @description(\"Reduction applied to general damages\")\n  futureCareDamagesReduction float @description(\"Reduction applied to future care damages\")\n  thresholdConsiderations string[] @description(\"Provincial threshold considerations\")\n  faultImpactOnSettlement string @description(\"How fault affects overall settlement strategy\")\n  netRecoverableAmount float @description(\"Net recoverable amount after fault reduction\")\n}\n\nclass QuantumCalculationGuidance {\n  totalSpecialDamages float @description(\"Total calculated special damages\")\n  totalGeneralDamages float @description(\"Total calculated general damages\")\n  totalFutureCareDamages float @description(\"Total future care damages\")\n  grossDamagesTotal float @description(\"Total gross damages before fault reduction\")\n  netDamagesTotal float @description(\"Total net damages after fault reduction\")\n  quantumConfidence float @description(\"Confidence in quantum calculation (0.0-1.0)\")\n  quantumRationale string @description(\"Reasoning for quantum calculation approach\")\n  provincialFactors string[] @description(\"Provincial factors affecting quantum calculation\")\n  uncertaintyAreas string[] @description(\"Areas requiring expert assessment or clarification\")\n  recommendedExpertReports string[] @description(\"Recommended expert reports for quantum support\")\n  settlementRange string @description(\"Recommended settlement range\")\n  litigationRisk \"low\" | \"moderate\" | \"high\" | \"very_high\" | \"unknown\" @description(\"Risk assessment for litigation\")\n  settlementStrategy \"negotiate\" | \"mediate\" | \"litigate\" | \"settle_fast\" | \"unknown\" @description(\"Recommended settlement approach\")\n}\n\nclass QuantumValidationChecks {\n  painSufferingValidation string @description(\"Validation of pain and suffering against Ontario benchmarks\")\n  totalDamagesValidation string @description(\"Validation of total damages against typical case ranges\")\n  medicalCostValidation string @description(\"Validation of medical costs against documented evidence\")\n  settlementRangeValidation string @description(\"Validation of settlement range against provincial standards\")\n  overvaluationRisk \"low\" | \"moderate\" | \"high\" | \"critical\" @description(\"Risk of overvaluation assessment\")\n  recommendedAdjustments string[] @description(\"Recommended adjustments to prevent overvaluation\")\n  comparisonToActualClaims string @description(\"How this compares to similar actual claims in Ontario\")\n}\n\n// ================================================================================================\n// LEVEL 04 QUANTUM CALCULATION FUNCTION\n// ================================================================================================\n\nfunction ExtractLevel04QuantumDetails(input: Level04AnalysisInput) -> QuantumDamageExtraction {\n  client \"openai/gpt-4o\"\n  prompt #\"\n    You are a Canadian personal injury quantum assessment expert analyzing comprehensive claim data to extract precise damage details for quantum calculation.\n    \n    ⚠️ CRITICAL ACCURACY REQUIREMENTS - PREVENT OVERVALUATION:\n    \n    This system was previously overvaluing pain and suffering by 10.6x actual claims. You must apply conservative, evidence-based assessment using Ontario benchmarks to prevent overvaluation that could lead to unrealistic settlement expectations.\n    \n    Your task: Analyze ALL available data sources and extract structured damage information for Canadian quantum calculation using CONSERVATIVE, REALISTIC estimates.\n    \n    Data Sources Available:\n    - Claim Reference: {{ input.claimReference }}\n    - Province: {{ input.province }}\n    - Level 1 Analysis: {{ input.level01Analysis }}\n    - Level 2 Coverage: {{ input.level02Coverage }}\n    - Level 3 Fault: {{ input.level03Fault }}\n    - Email Content: {{ input.emailContent }}\n    - SparkNLP Insights: {{ input.sparkNlpInsights }}\n    - OCR Text: {{ input.ocrTexts }}\n    - Attachments: {{ input.attachmentDetails }}\n    \n    CRITICAL REQUIREMENTS:\n    1. CONSERVATIVE APPROACH: Apply Ontario benchmarks to prevent overvaluation\n    2. EVIDENCE-BASED ONLY: Only include damages supported by documentation\n    3. REALISTIC CALCULATIONS: Use actual case precedents, not theoretical maximums\n    4. HUMAN REVIEW READY: Provide conservative estimates for settlement discussions\n    5. VALIDATION REQUIRED: Include validation checks against overvaluation\n    \n    KEY EXTRACTION AREAS:\n    \n    A) MEDICAL DAMAGES ASSESSMENT:\n    - Extract specific medical costs from all sources (receipts, invoices, treatment records)\n    - Identify emergency care, ongoing treatment, diagnostic costs\n    - Count documented treatment sessions and calculate costs\n    - Assess medical complexity and future treatment needs\n    - Factor in documented vs. estimated medical expenses\n    \n    B) INCOME LOSS ANALYSIS:\n    - Extract pre-accident income information\n    - Calculate time off work and lost wages\n    - Assess impact on earning capacity\n    - Consider return-to-work status and limitations\n    - Project future income loss if permanent impairment\n    \n    C) CARE AND ASSISTANCE COSTS:\n    - Identify family care assistance provided\n    - Calculate economic value of care assistance\n    - Assess professional care requirements\n    - Factor in housekeeping and personal care needs\n    \n    D) SPECIAL DAMAGES BREAKDOWN:\n    - Extract all out-of-pocket expenses\n    - Include transportation, accommodation, prescription costs\n    - Account for lost benefits and other financial losses\n    - Assess quality and completeness of documentation\n    \n    E) GENERAL DAMAGES ASSESSMENT (CRITICAL - APPLY CONSERVATIVE ONTARIO BENCHMARKS):\n    - MINOR INJURIES (soft tissue, sprains): $5,000 - $15,000 maximum\n    - MODERATE INJURIES (partial tears, limited surgery): $15,000 - $45,000 maximum  \n    - SEVERE INJURIES (significant surgery, permanent impairment): $45,000 - $150,000\n    - CATASTROPHIC INJURIES (life-altering): $150,000 - $420,000 (Andrews cap)\n    - SLIP-FALL CASES: Typically $10,000 - $50,000 for moderate injuries\n    - USE LOWER END of ranges unless strong evidence supports higher amounts\n    - COMPARE to actual documented settlement amounts in similar cases\n    - APPLY CONSERVATIVE estimates for settlement and human review purposes\n    \n    F) FUTURE CARE NEEDS:\n    - Assess ongoing medical treatment requirements\n    - Estimate future therapy and rehabilitation costs\n    - Consider assistive devices and home modifications\n    - Project attendant care needs if applicable\n    \n    G) FAULT IMPACT ANALYSIS:\n    - Apply Level 3 fault determination to quantum calculation\n    - Calculate reduction factors for different damage categories\n    - Consider provincial threshold and no-fault implications\n    - Assess net recoverable amounts\n    \n    H) QUANTUM CALCULATION GUIDANCE WITH VALIDATION:\n    - Provide total damage calculations by category\n    - Calculate gross and net damage totals (MUST BE REALISTIC)\n    - VALIDATE against Ontario benchmarks and actual case precedents\n    - IDENTIFY overvaluation risks and recommend adjustments\n    - COMPARE to documented actual claims of similar severity\n    - Recommend conservative settlement strategy for human review\n    - Flag areas requiring expert medical/economic reports\n    - Consider litigation risk (overvaluation increases litigation risk)\n    \n    SPECIAL ATTENTION TO:\n    - Medical records and treatment documentation\n    - Employment records and income verification\n    - Receipts and invoices for out-of-pocket expenses\n    - Expert medical opinions and prognosis\n    - Functional capacity evaluations\n    - Return-to-work assessments\n    \n    CANADIAN PROVINCIAL CONSIDERATIONS:\n    - Ontario: Statutory Accident Benefits, threshold provisions\n    - BC: No-fault benefits, Part 7 tort claims\n    - Alberta: Section B benefits, minor injury regulation\n    - Quebec: SAAQ no-fault system considerations\n    - Other provinces: Apply relevant provincial personal injury law\n    \n    QUANTUM CALCULATION PRINCIPLES:\n    - Special damages: Actual financial losses (receipts-based ONLY)\n    - General damages: Pain and suffering (Ontario precedent-based, CONSERVATIVE)\n    - Future care: Reasonable and necessary care costs (medical evidence required)\n    - Loss of earning capacity: Evidence-based projections (conservative assumptions)\n    - Mitigation: Duty to minimize damages\n    \n    ⚠️ VALIDATION REQUIREMENTS:\n    - COMPARE all damage amounts to actual Ontario case settlements\n    - JUSTIFY any amounts above typical ranges with strong evidence\n    - APPLY conservative estimates suitable for settlement discussions\n    - IDENTIFY areas where amounts may be inflated\n    - RECOMMEND expert reports for amounts requiring validation\n    \n    🎯 SETTLEMENT FOCUS:\n    This analysis is for HUMAN REVIEW and SETTLEMENT PURPOSES. Provide conservative, defensible estimates that:\n    - Can be supported in settlement negotiations\n    - Reflect realistic recovery expectations\n    - Account for litigation costs and risks\n    - Are appropriate for reserve adequacy\n    \n    I) UI METADATA GENERATION (CRITICAL FOR EXPLAINABLE QUANTUM CALCULATION):\n    - Generate precise entity mappings for every damage amount extracted\n    - Create document highlights with exact coordinates for financial evidence\n    - Map each damage component to its source document and position\n    - Assign appropriate colors based on damage type and amount ranges\n    - Include confidence breakdown for each quantum calculation element\n    - Create visual calculation flow for quantum determination process\n    \n    J) EXPLAINABILITY ANALYSIS (LIME/SHAP FOR QUANTUM CALCULATION):\n    - Generate SHAP-style feature importance for damage factors\n    - Identify top factors increasing/decreasing quantum amounts\n    - Provide confidence contributors for quantum calculations\n    - Generate alternative quantum scenarios with amount changes\n    - Create visualization data for interactive quantum exploration\n    - Include Ontario benchmark factors affecting quantum calculation\n    - Explain methodology used for damage amount calculations\n    \n    UI METADATA REQUIREMENTS:\n    - Every damage amount MUST have source document mapping\n    - Highlight coordinates MUST be provided for financial evidence\n    - Color coding MUST reflect damage type and confidence levels\n    - Calculation flow MUST show logical progression of quantum analysis\n    - Confidence scores MUST be provided for all calculations\n    \n    EXPLAINABILITY REQUIREMENTS:\n    - Feature importance MUST explain quantum calculation reasoning\n    - Alternative scenarios MUST show sensitivity of damage calculations\n    - Ontario benchmark factors MUST be integrated into explainability\n    - Visualization data MUST enable interactive quantum exploration\n    - Methodology notes MUST explain quantum calculation approach\n    - Overvaluation prevention measures MUST be clearly documented\n    \n    Extract comprehensive quantum details ready for Canadian damage calculation and settlement negotiation with complete UI metadata and explainability.\n    \n    {{ ctx.output_format }}\n  \"#\n}\n",
    "priority_risk_analysis.baml": "// ================================================================================================\n// AI-DRIVEN PRIORITY AND RISK ASSESSMENT - BAML IMPLEMENTATION\n// ================================================================================================\n// Purpose: Intelligent priority scoring and risk assessment for insurance claims\n// Enhancement: Replaces rule-based logic with AI-driven contextual assessment\n// ================================================================================================\n\n// ================================================================================================\n// PRIORITY AND RISK SCHEMAS\n// ================================================================================================\n\nenum RiskCategory {\n  FINANCIAL @description(\"Financial exposure risk\")\n  LEGAL @description(\"Legal liability or compliance risk\")\n  REGULATORY @description(\"Regulatory or statutory risk\")\n  REPUTATIONAL @description(\"Reputational damage risk\")\n  OPERATIONAL @description(\"Operational or processing risk\")\n  FRAUD @description(\"Potential fraud or misrepresentation risk\")\n  COVERAGE @description(\"Coverage interpretation or dispute risk\")\n}\n\nclass RiskFactor {\n  category RiskCategory @description(\"Category of risk\")\n  severity \"LOW\" | \"MEDIUM\" | \"HIGH\" | \"CRITICAL\" @description(\"Severity level of this risk factor\")\n  description string @description(\"Detailed description of the risk factor\")\n  likelihood float @description(\"Likelihood of this risk materializing (0.0-1.0)\")\n  potentialImpact string @description(\"Potential impact if risk materializes\")\n  mitigationStrategy string @description(\"Recommended mitigation strategy\")\n  timeframeToResolve string @description(\"Expected timeframe to resolve or mitigate\")\n  requiresSpecialistReview bool @description(\"Whether this risk requires specialist review\")\n}\n\nclass PriorityRiskAssessment {\n  overallPriorityLevel PriorityLevel @description(\"Overall priority level for this claim\")\n  overallRiskScore float @description(\"Overall risk score (0.0-1.0, higher = more risk)\")\n  \n  // Priority factors\n  priorityDrivers string[] @description(\"Key factors driving the priority level\")\n  timelineSensitivity string @description(\"How time-sensitive this claim is\")\n  stakeholderImpact string @description(\"Impact on key stakeholders if delayed\")\n  \n  // Risk analysis\n  identifiedRisks RiskFactor[] @description(\"All identified risk factors\")\n  highestRiskCategory RiskCategory @description(\"Category with highest risk exposure\")\n  riskMitigationPriority string[] @description(\"Prioritized list of risks to address first\")\n  \n  // Human review requirements\n  requiresHumanReview bool @description(\"Whether human expert review is required\")\n  requiresLegalCounsel bool @description(\"Whether legal counsel review is required\")\n  requiresSpecialistReview bool @description(\"Whether specialist review is required\")\n  reviewReason string @description(\"Primary reason for human/legal/specialist review\")\n  \n  // Timeline and urgency\n  recommendedProcessingTimeline string @description(\"Recommended processing timeline\")\n  escalationTriggers string[] @description(\"Conditions that would trigger escalation\")\n  urgencyJustification string @description(\"Justification for the assigned priority level\")\n  \n  // Financial considerations\n  estimatedFinancialExposure string @description(\"Estimated financial exposure range\")\n  costOfDelay string @description(\"Estimated cost of processing delays\")\n  \n  // Quality and confidence\n  assessmentConfidence float @description(\"Confidence in this priority/risk assessment (0.0-1.0)\")\n  uncertaintyFactors string[] @description(\"Factors that create uncertainty in assessment\")\n  \n  // Recommendations\n  immediateActions string[] @description(\"Actions that should be taken immediately\")\n  processOptimizations string[] @description(\"Opportunities to optimize processing\")\n  preventiveRecommendations string[] @description(\"Recommendations to prevent similar issues\")\n}\n\n// ================================================================================================\n// AI-DRIVEN PRIORITY AND RISK ASSESSMENT FUNCTION\n// ================================================================================================\n\nfunction AssessPriorityAndRisk(\n  claimId: string,\n  claimType: string,\n  coverageDecision: string,\n  coverageConfidence: float,\n  level01Analysis: string,\n  claimDetails: string,\n  policyInformation: string,\n  dataCompleteness: float,\n  additionalContext: string\n) -> PriorityRiskAssessment {\n  client \"openai/gpt-4o\"\n  prompt #\"\n    You are an AI expert in insurance risk management and claims prioritization, with deep knowledge of Canadian insurance regulations and industry best practices.\n    \n    MISSION: Intelligently assess priority level and risk factors for this insurance claim, considering financial exposure, legal complexity, regulatory requirements, and operational efficiency.\n    \n    CLAIM INFORMATION:\n    Claim ID: {{ claimId }}\n    Claim Type: {{ claimType }}\n    Coverage Decision: {{ coverageDecision }}\n    Coverage Confidence: {{ coverageConfidence }}\n    Data Completeness: {{ dataCompleteness }}\n    \n    DETAILED ANALYSIS DATA:\n    \n    LEVEL 01 ANALYSIS:\n    {{ level01Analysis }}\n    \n    CLAIM DETAILS:\n    {{ claimDetails }}\n    \n    POLICY INFORMATION:\n    {{ policyInformation }}\n    \n    ADDITIONAL CONTEXT:\n    {{ additionalContext }}\n    \n    INTELLIGENT PRIORITY AND RISK ASSESSMENT REQUIREMENTS:\n    \n    1. PRIORITY LEVEL DETERMINATION:\n       \n       CRITICAL Priority if:\n       - High financial exposure (>$500K potential liability)\n       - Imminent legal deadlines or regulatory requirements\n       - Potential fraud indicators\n       - Media attention or reputational risk\n       - Time-sensitive coverage disputes\n       - Catastrophic claims requiring immediate response\n       \n       HIGH Priority if:\n       - Significant financial exposure ($100K-$500K)\n       - Legal complexity requiring specialist attention\n       - Coverage disputes with precedent implications\n       - Third-party liability with injury\n       - Regulatory reporting requirements\n       - Customer VIP status or business relationship impact\n       \n       MEDIUM Priority if:\n       - Moderate financial exposure ($25K-$100K)\n       - Standard liability or property claims\n       - Clear coverage with minor complications\n       - Routine regulatory compliance needs\n       - Standard processing timelines adequate\n       \n       LOW Priority if:\n       - Low financial exposure (<$25K)\n       - Straightforward coverage decisions\n       - No legal or regulatory complications\n       - Routine administrative processing\n       - No time-sensitive factors\n    \n    2. COMPREHENSIVE RISK ANALYSIS:\n       \n       FINANCIAL RISKS:\n       - Assess total potential financial exposure\n       - Consider policy limits, deductibles, and coverage gaps\n       - Evaluate cost of delays vs. expedited processing\n       - Factor in legal costs and settlement ranges\n       \n       LEGAL RISKS:\n       - Identify potential litigation exposure\n       - Assess regulatory compliance requirements\n       - Consider precedent-setting implications\n       - Evaluate limitation periods and deadlines\n       \n       REGULATORY RISKS:\n       - Provincial insurance regulatory requirements\n       - Federal compliance obligations\n       - Industry reporting standards\n       - Consumer protection considerations\n       \n       REPUTATIONAL RISKS:\n       - Public visibility of claim\n       - Social media or news coverage potential\n       - Customer relationship implications\n       - Industry reputation considerations\n       \n       OPERATIONAL RISKS:\n       - Processing complexity and resource requirements\n       - Data quality and completeness issues\n       - Specialist expertise availability\n       - Technology or system limitations\n       \n       FRAUD RISKS:\n       - Unusual claim patterns or characteristics\n       - Inconsistent information or documentation\n       - Timing or circumstance red flags\n       - Historical fraud indicators\n    \n    3. HUMAN REVIEW REQUIREMENTS:\n       \n       Require HUMAN EXPERT REVIEW if:\n       - High financial exposure or complex liability\n       - Unusual or precedent-setting circumstances\n       - Data quality concerns affecting decision confidence\n       - Coverage interpretation requiring judgment\n       - Customer relationship management needs\n       \n       Require LEGAL COUNSEL REVIEW if:\n       - Potential litigation exposure\n       - Complex regulatory compliance issues\n       - Coverage disputes with legal implications\n       - Third-party claims with significant liability\n       - Precedent-setting coverage decisions\n       \n       Require SPECIALIST REVIEW if:\n       - Technical expertise needed (engineering, medical, etc.)\n       - Industry-specific knowledge required\n       - Complex valuation or assessment needs\n       - Regulatory specialist input required\n    \n    4. TIMELINE AND URGENCY ASSESSMENT:\n       \n       Consider:\n       - Statutory deadlines and limitation periods\n       - Customer service commitments\n       - Regulatory reporting requirements\n       - Business impact of delays\n       - Resource availability and capacity\n       - Complexity of required analysis\n    \n    5. CONTEXT-SPECIFIC FACTORS:\n       \n       For {{ claimType }} claims, particularly consider:\n       - Industry-specific risks and regulations\n       - Typical processing timelines and complexities\n       - Common dispute areas and precedents\n       - Stakeholder expectations and requirements\n       \n       For {{ coverageDecision }} decisions with {{ coverageConfidence }} confidence:\n       - Higher confidence may reduce priority\n       - Lower confidence may increase human review needs\n       - INFORMATION_REQUIRED decisions need expedited data gathering\n       - NOT_COVERED decisions may require enhanced documentation\n    \n    6. CANADIAN INSURANCE CONTEXT:\n       \n       Factor in:\n       - Provincial insurance regulations and requirements\n       - Canadian court precedents and legal framework\n       - Industry best practices and standards\n       - Consumer protection obligations\n       - Regulatory oversight and reporting requirements\n    \n    ASSESSMENT METHODOLOGY:\n    \n    - Use contextual intelligence rather than rigid rules\n    - Consider interdependencies between factors\n    - Provide specific, actionable recommendations\n    - Balance efficiency with thoroughness\n    - Account for unique circumstances and exceptions\n    - Consider both immediate and long-term implications\n    \n    Be comprehensive, practical, and focused on enabling optimal claim processing decisions that balance efficiency, accuracy, and risk management.\n    \n    {{ ctx.output_format }}\n  \"#\n} ",
    "resume.baml": "// Defining a data model.\nclass Resume {\n  name string\n  email string\n  experience string[]\n  skills string[]\n}\n\n// Create a function to extract the resume from a string.\nfunction ExtractResume(resume: string) -> Resume {\n  // Specify a client as provider/model-name\n  // you can use custom LLM params with a custom client name from clients.baml like \"client CustomHaiku\"\n  client \"openai/gpt-4o\" // Set OPENAI_API_KEY to use this client.\n  prompt #\"\n    Extract from this content:\n    {{ resume }}\n\n    {{ ctx.output_format }}\n  \"#\n}\n\n\n\n// Test the function with a sample resume. Open the VSCode playground to run this.\ntest vaibhav_resume {\n  functions [ExtractResume]\n  args {\n    resume #\"\n      Vaibhav Gupta\n      <EMAIL>\n\n      Experience:\n      - Founder at BoundaryML\n      - CV Engineer at Google\n      - CV Engineer at Microsoft\n\n      Skills:\n      - Rust\n      - C++\n    \"#\n  }\n}\n",
    "ui_metadata_schemas.baml": "// ================================================================================================\n// SHARED UI METADATA AND EXPLAINABILITY SCHEMAS\n// ================================================================================================\n// Purpose: Shared schemas for UI metadata and explainability across all analysis levels\n// Used by: Level01, Level02, Level03, Level04 analysis schemas\n// ================================================================================================\n\n// ================================================================================================\n// CORE UI METADATA SCHEMAS\n// ================================================================================================\n\nclass UIMetadata {\n  entityMappings EntityMapping[] @description(\"UI metadata for entity-to-source mapping\")\n  explainabilityData ExplainabilityInsight[] @description(\"LIME/SHAP analysis for model decisions\")\n  documentHighlights DocumentHighlight[] @description(\"Precise highlighting coordinates\")\n  confidenceBreakdown FieldConfidenceBreakdown @description(\"Per-field confidence analysis\")\n  colorScheme ColorSchemeMapping @description(\"UI color scheme for different entity types\")\n  analysisFlow AnalysisFlow @description(\"Visual flow of analysis logic\")\n}\n\nclass EntityMapping {\n  fieldName string @description(\"Entity field name\")\n  extractedValue string @description(\"The extracted/normalized value\")\n  originalText string @description(\"Original text from document\")\n  sourceDocument string @description(\"Source document filename\")\n  pageNumber int @description(\"Page number where found\")\n  lineNumber int? @description(\"Line number if available\")\n  startPosition int? @description(\"Character start position\")\n  endPosition int? @description(\"Character end position\")\n  boundingBox float[]? @description(\"OCR bounding box [x1, y1, x2, y2]\")\n  extractionMethod string @description(\"How entity was extracted\")\n  confidence float @description(\"Extraction confidence (0.0-1.0)\")\n  highlightColor string @description(\"UI highlight color for this entity\")\n  entityType string @description(\"Type of entity\")\n  relevanceScore float @description(\"Relevance score for analysis (0.0-1.0)\")\n}\n\nclass DocumentHighlight {\n  documentId string @description(\"Document identifier\")\n  documentName string @description(\"Document filename\")\n  pageNumber int @description(\"Page number\")\n  highlights HighlightRegion[] @description(\"Highlight regions on this page\")\n}\n\nclass HighlightRegion {\n  fieldName string @description(\"Associated entity field\")\n  text string @description(\"Highlighted text\")\n  startPos int @description(\"Character start position\")\n  endPos int @description(\"Character end position\")\n  boundingBox float[]? @description(\"Visual bounding box\")\n  highlightColor string @description(\"UI color for highlight\")\n  confidence float @description(\"Confidence in this extraction\")\n  entityType string @description(\"Type of entity being highlighted\")\n  analysisRelevance float @description(\"Relevance to analysis (0.0-1.0)\")\n}\n\nclass FieldConfidenceBreakdown {\n  overallConfidence float @description(\"Overall analysis confidence\")\n  fieldConfidences FieldConfidence[] @description(\"Per-field confidence scores\")\n  uncertaintyFactors string[] @description(\"Factors contributing to uncertainty\")\n  confidenceDistribution ConfidenceDistribution @description(\"Confidence distribution analysis\")\n  analysisCertainty float @description(\"Certainty in analysis results\")\n}\n\nclass FieldConfidence {\n  fieldName string @description(\"Field name\")\n  confidence float @description(\"Confidence score\")\n  contributingFactors string[] @description(\"What contributed to this confidence\")\n  uncertaintyReasons string[] @description(\"Reasons for any uncertainty\")\n  sourceQuality float @description(\"Quality of source information (0.0-1.0)\")\n  validationStrength float @description(\"Strength of validation (0.0-1.0)\")\n}\n\nclass ConfidenceDistribution {\n  highConfidenceFields string[] @description(\"Fields with >90% confidence\")\n  mediumConfidenceFields string[] @description(\"Fields with 70-90% confidence\")\n  lowConfidenceFields string[] @description(\"Fields with <70% confidence\")\n  averageConfidence float @description(\"Average confidence across all fields\")\n}\n\nclass ColorSchemeMapping {\n  entityTypeColors EntityTypeColor[] @description(\"Color mapping for different entity types\")\n  confidenceColorScheme ConfidenceColorScheme @description(\"Color scheme based on confidence levels\")\n  analysisColorScheme AnalysisColorScheme @description(\"Color scheme for analysis-specific elements\")\n}\n\nclass EntityTypeColor {\n  entityType string @description(\"Entity type\")\n  primaryColor string @description(\"Primary highlight color\")\n  secondaryColor string @description(\"Secondary/border color\")\n  textColor string @description(\"Text color for good contrast\")\n  weight float @description(\"Weight/importance of this entity type (0.0-1.0)\")\n}\n\nclass ConfidenceColorScheme {\n  highConfidenceColor string @description(\"Color for high confidence (>90%)\")\n  mediumConfidenceColor string @description(\"Color for medium confidence (70-90%)\")\n  lowConfidenceColor string @description(\"Color for low confidence (<70%)\")\n}\n\nclass AnalysisColorScheme {\n  criticalColor string @description(\"Color for critical elements\")\n  highPriorityColor string @description(\"Color for high priority elements\")\n  mediumPriorityColor string @description(\"Color for medium priority elements\")\n  lowPriorityColor string @description(\"Color for low priority elements\")\n}\n\nclass AnalysisFlow {\n  flowSteps AnalysisFlowStep[] @description(\"Steps in analysis logic\")\n  decisionPoints DecisionPoint[] @description(\"Key decision points in analysis\")\n  dataFlow DataFlowStep[] @description(\"Flow of data through analysis\")\n  alternativeScenarios AlternativeScenario[] @description(\"Alternative scenarios considered\")\n}\n\nclass AnalysisFlowStep {\n  stepNumber int @description(\"Step number in analysis process\")\n  stepName string @description(\"Name of this analysis step\")\n  stepDescription string @description(\"Description of what happens in this step\")\n  inputData string[] @description(\"Input data considered in this step\")\n  outcome string @description(\"Outcome of this step\")\n  nextSteps string[] @description(\"Possible next steps\")\n  confidence float @description(\"Confidence in this step's outcome\")\n}\n\nclass DecisionPoint {\n  decisionName string @description(\"Name of the decision\")\n  decisionDescription string @description(\"Description of the decision made\")\n  inputFactors string[] @description(\"Factors that influenced this decision\")\n  outcome string @description(\"Outcome of the decision\")\n  confidence float @description(\"Confidence in this decision\")\n  alternativeOptions string[] @description(\"Alternative options considered\")\n}\n\nclass DataFlowStep {\n  stepName string @description(\"Name of data flow step\")\n  sourceData string[] @description(\"Source data for this step\")\n  processing string @description(\"Processing performed on data\")\n  outputData string[] @description(\"Output data from this step\")\n  dataQuality float @description(\"Quality of data after processing (0.0-1.0)\")\n}\n\n// ================================================================================================\n// EXPLAINABILITY SCHEMAS\n// ================================================================================================\n\nclass ExplainabilityInsight {\n  analysisType \"LIME\" | \"SHAP\" | \"INTEGRATED_GRADIENTS\" | \"ATTENTION\" @description(\"Type of explainability analysis\")\n  decisionContext string @description(\"What decision this explains\")\n  featureImportances FeatureImportance[] @description(\"Feature importance scores\")\n  topPositiveFactors ExplanationFactor[] @description(\"Top factors supporting decision\")\n  topNegativeFactors ExplanationFactor[] @description(\"Top factors against decision\")\n  confidenceContributors ConfidenceContributor[] @description(\"What contributed to confidence\")\n  alternativeScenarios AlternativeScenario[] @description(\"Alternative scenarios and their impact\")\n  visualizationData VisualizationData @description(\"Data for UI visualization\")\n  methodologyNotes string[] @description(\"Notes about the explainability methodology used\")\n  contextualFactors ContextualFactors @description(\"Contextual factors affecting analysis\")\n}\n\nclass FeatureImportance {\n  featureName string @description(\"Name of the feature/field\")\n  importance float @description(\"Importance score (-1.0 to 1.0)\")\n  direction \"POSITIVE\" | \"NEGATIVE\" | \"NEUTRAL\" @description(\"Impact direction\")\n  explanation string @description(\"Human-readable explanation\")\n  sourceText string? @description(\"Source text that contributed\")\n  confidence float @description(\"Confidence in this importance score\")\n  relatedFields string[] @description(\"Other fields this feature influences\")\n  analysisContext string @description(\"Context for this feature's importance\")\n}\n\nclass ExplanationFactor {\n  factor string @description(\"The factor/feature\")\n  impact float @description(\"Impact score\")\n  explanation string @description(\"Why this factor matters\")\n  sourceLocation string? @description(\"Where this factor was found\")\n  evidenceStrength \"STRONG\" | \"MODERATE\" | \"WEAK\" @description(\"Strength of evidence\")\n  supportingEvidence string[] @description(\"Supporting evidence for this factor\")\n}\n\nclass ConfidenceContributor {\n  contributor string @description(\"What contributed to confidence\")\n  contribution float @description(\"How much it contributed (0.0-1.0)\")\n  reason string @description(\"Why this contributed to confidence\")\n  sourceType \"DOCUMENT_QUALITY\" | \"TEXT_CLARITY\" | \"PATTERN_MATCH\" | \"CROSS_VALIDATION\" | \"EXPERT_VALIDATION\" @description(\"Type of contributor\")\n}\n\nclass AlternativeScenario {\n  scenario string @description(\"Alternative scenario description\")\n  probabilityChange float @description(\"How decision probability would change\")\n  requiredChanges string[] @description(\"What would need to change\")\n  likelihood \"HIGH\" | \"MEDIUM\" | \"LOW\" @description(\"Likelihood of this scenario\")\n  implications string[] @description(\"Implications of this scenario\")\n}\n\nclass VisualizationData {\n  chartType \"BAR\" | \"WATERFALL\" | \"HEATMAP\" | \"SANKEY\" | \"FEATURE_IMPORTANCE\" | \"FLOW_DIAGRAM\" @description(\"Recommended chart type\")\n  chartData ChartDataPoint[] @description(\"Data points for visualization\")\n  colorScheme string[] @description(\"Color scheme for visualization\")\n  annotations ChartAnnotation[] @description(\"Chart annotations\")\n  interactiveElements InteractiveElement[] @description(\"Interactive elements for the visualization\")\n}\n\nclass ChartDataPoint {\n  label string @description(\"Data point label\")\n  value float @description(\"Data point value\")\n  color string? @description(\"Specific color for this point\")\n  metadata string? @description(\"Additional metadata\")\n  tooltip string? @description(\"Tooltip text for this point\")\n  analysisImpact float @description(\"Impact on overall analysis\")\n}\n\nclass ChartAnnotation {\n  text string @description(\"Annotation text\")\n  position string @description(\"Where to place annotation\")\n  style string @description(\"Annotation style\")\n  importance \"HIGH\" | \"MEDIUM\" | \"LOW\" @description(\"Importance of this annotation\")\n  analysisRelevance float @description(\"Relevance to analysis\")\n}\n\nclass InteractiveElement {\n  elementType \"HOVER_DETAIL\" | \"CLICK_DRILL_DOWN\" | \"FILTER\" | \"ZOOM\" | \"COMPARE\" @description(\"Type of interactive element\")\n  targetField string @description(\"Field this element targets\")\n  action string @description(\"What action it performs\")\n  description string @description(\"Description of the interaction\")\n  context string @description(\"Context for this interaction\")\n}\n\nclass ContextualFactors {\n  analysisLevel string @description(\"Analysis level (L1, L2, L3, L4)\")\n  analysisContext string @description(\"Context of the analysis\")\n  domainSpecificFactors string[] @description(\"Domain-specific factors affecting analysis\")\n  regulatoryFactors string[] @description(\"Regulatory factors considered\")\n  businessRules string[] @description(\"Business rules applied\")\n  qualityFactors string[] @description(\"Factors affecting data quality\")\n}",
}

def get_baml_files():
    return file_map