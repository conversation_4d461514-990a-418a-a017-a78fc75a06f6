const { DynamoDBClient } = require('@aws-sdk/client-dynamodb')
const { DynamoDBDocumentClient, PutCommand } = require('@aws-sdk/lib-dynamodb')
const dynamoDbClient = DynamoDBDocumentClient.from(new DynamoDBClient())

/**
 * Put  items into a DynamoDB table.
 *
 * @param {string} tableName - The name of the DynamoDB table to retrieve items from.
 * @param {Object} Item - The item to put into the table.
 * @returns {Promise<Object>} - A promise that resolves with the response from the DynamoDB API.
 */
async function putDynamodbTableItem(tableName, Item) {
	const params = {
		TableName: tableName,
		Item: Item,
	}
	console.log('Params for putDynamodbTableItem()', params)
	const command = new PutCommand(params)

	const response = await dynamoDbClient.send(command)
	console.log(response)
	return response
}

module.exports = {
    putDynamodbTableItem
}