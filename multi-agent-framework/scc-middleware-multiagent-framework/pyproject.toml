[tool.poetry]
authors = []
description = "FastAPI service for Elsa Conversation Insights."
name = "elsa-conversation-insights-service"
readme = "README.md"
version = "0.1.0"

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
fastapi = "^0.109.2"
uvicorn = {extras = ["standard"], version = "^0.27.0.post1"}
boto3 = "^1.34.49"
requests= "^2.31.0"
twilio= "^9.2.3"
litellm="1.46.8"
langchain="0.3.0"
langchain-community="0.3.0"
langchain-core="0.3.2"
scikit-learn="1.5.2"
xgboost="2.1.1"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
jupyter = "^1.0.0"

[tool.poetry.group.codespell]
optional = true

[tool.poetry.group.codespell.dependencies]
codespell = "^2.2.6"

[tool.poetry.group.lint]
optional = true

[tool.poetry.group.lint.dependencies]
ruff = "^0.1.15"
pre-commit = "^3.6.0"

[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
pytest = "^7.3.0"
pytest-cov = "^4.0.0"

[tool.poetry.group.typing]
optional = true

[tool.poetry.group.typing.dependencies]
mypy = "^1.8.0"

[tool.codespell]
check-filenames = true
check-hidden = true
ignore-words-list = "astroid,gallary,momento,narl,ot,rouge"
# Feel free to un-skip examples, and experimental, you will just need to
# work through many typos (--write-changes and --interactive will help)
skip = "*.csv,*.html,*.json,*.jsonl,*.pdf,*.txt,*.ipynb"

[tool.coverage.run]
omit = [
  "tests/*"
]

[tool.mypy]
disallow_untyped_defs = true
# Remove venv skip when integrated with pre-commit
exclude = [".venv", "examples"]
follow_imports = "skip"
ignore_missing_imports = true
python_version = "3.11"
strict_optional = false

[tool.ruff]
target-version = "py311"

[tool.ruff.flake8-annotations]
mypy-init-return = true

[tool.ruff.pydocstyle]
convention = "google"

[build-system]
build-backend = "poetry.core.masonry.api"
requires = ["poetry-core"]