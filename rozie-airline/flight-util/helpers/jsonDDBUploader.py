
import boto3 # type: ignore
import json
import os
import time

# Initialize the DynamoDB client
client = boto3.resource('dynamodb', region_name='ca-central-1')
table = client.Table('rozie-mock-airline-airport-table-dev')

# Load the JSON data
with open("../data/airline_code_items.json", 'r') as airLineCodeItems:
    airLineCodeItemsEntries = json.load(airLineCodeItems)

# Total number of items
total_items = len(airLineCodeItemsEntries)

# Upload each item and show progress
for index, item in enumerate(airLineCodeItemsEntries, start=1):
    response = table.put_item(Item=item)
    
    # Display progress
    print(f"Uploading item {index}/{total_items}... ({index / total_items * 100:.2f}% complete)")
    
    # Optional: Sleep for a short time to slow down and better show progress
    # time.sleep(0.1)  # You can uncomment this if you want to simulate slower uploads

print("All items uploaded successfully!")
