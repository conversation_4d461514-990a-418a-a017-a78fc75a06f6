import random
import string
from datetime import datetime
from zoneinfo import ZoneInfo

from airline_app.helpers.dynamo_helper import  get_item_by_primary_key, query_items_by_gsi
from airline_app.common.constants import (
    PNR_TABLE,
    PNR_TABLE_FLIGHT_INDEX,
    BAGGAGE_TABLE,
    BAGGAGE_ID_BY_FLIGHT_INDEX
)
from airline_app.helpers.logging_helper import print_error_log,print_info_log
   
def get_baggage_details_by_pnr(pnr_id):
    try:
        pnr_details = get_item_by_primary_key(PNR_TABLE, "pnr_id", pnr_id)
        if not pnr_details:
            print_info_log("INFO", "get_baggage_details_by_pnr", f"Invalid pnr_id: {pnr_id}")
            return {
                "status": "Error",
                "message": f"No details pnr_id: {pnr_id}, please check the PNR",
                "response":[]
            }
        baggages = pnr_details.get("baggages", [])
        if not baggages:
            print_info_log("INFO", "get_baggage_details_by_pnr", f"No baggages found with pnr_id: {pnr_id}")
            return {
                "status": "Error",
                "message": f"No baggage details found with pnr_id : {pnr_id}",
                "response":[]
            }
        print_info_log("INFO", "get_baggage_details_by_pnr", f"pnr details retrieved for pnr_id:{pnr_id}")
        baggage_details = []
        for baggage in baggages:
            item = get_item_by_primary_key(BAGGAGE_TABLE, "baggage_tag", baggage)
            if item:
                baggage_details.append(item)
        if baggage:
            return {
                "status": "Success",
                "message": f"Successfully retrieved baggage details for pnr_id : {pnr_id}",
                "response": baggage_details
            }
        return {
            "status": "Error",
            "message": f"No baggage details found with pnr_id : {pnr_id}",
        }
    except Exception as e:
        print_error_log("get_baggage_details_by_pnr", f"Error retrieving pnr details: {str(e)}")
        return {
            "status": "Error",
            "message": "Failed to retrieve pnr details"
        }

def get_baggage_details_by_baggage_id(baggage_id):
    try:
        if not baggage_id:
            print_info_log("INFO", "get_baggage_details_by_baggage_id", f"No baggages id provided")
            return {
                "status": "Error",
                "message": f"Must Provide baggage tag id",
                "response":[]
            }
        item = get_item_by_primary_key(BAGGAGE_TABLE, "baggage_tag", baggage_id)
        return {
            "status": "Success",
            "message": f"Successfully retrieved baggage details for baggage_tag : {baggage_id}",
            "response": item
        }
    except Exception as e:
        print_error_log("get_baggage_details_by_pnr", f"Error retrieving baggage details: {str(e)}")
        return {
            "status": "Error",
            "message": "Failed to retrieve baggage details"
        }
