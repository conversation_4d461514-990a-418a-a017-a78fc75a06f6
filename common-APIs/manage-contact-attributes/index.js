const { ApiResp } = require('./helpers/api-helper.js')
const {
  putDynamodbTableItem,
  retrieveDynamoEntry
} = require('./helpers/dynamodb-helper.js')
const {setContactAttributes} = require('./helpers/connect-helper.js')
const {CONNECT_INSTANCE_ID, TABLE_NAME} = process.env

const CustomError = class extends Error {
  constructor(name, message) {
    super(message)
    this.name = name
  }
}

const handler = async (event) => {
  console.log(JSON.stringify(event))
  let result = {}
  try {
    if(event.httpMethod == 'POST'){
      const body = JSON.parse(event.body)
      console.log("event body", body)
      if (!body.toPhoneNumber) {
        throw new CustomError('MissingParameters', 'MissingParameters')
      }
      await putDynamodbTableItem(TABLE_NAME, body)
      console.log("contact attributes stored successfully")
    }else if(event.Details.ContactData?.InitialContactId){
      result = await retrieveDynamoEntry(TABLE_NAME, 'toPhoneNumber', event.Details.Parameters?.PhoneNumber)
      console.log("result", result) 
      if(result){
        let response= await setContactAttributes(event.Details.ContactData?.InitialContactId, CONNECT_INSTANCE_ID, result?.attributes)
        return response
      }
    }
    return ApiResp({ success: true, data: result })
  } catch (err) {
    console.error(err)
    return ApiResp({ success: true, data: { error: err?.name ?? 'generic_error' } })
  }
}

module.exports = {
  handler
}