from fastapi import APIRouter, HTTPException, Query
from typing import Dict, Any
from services.flight_status_service import get_flight_status_service

router = APIRouter()

@router.get("/flight-status")
async def get_flight_status(
    carrier_code: str = Query(..., description="Airline/carrier code (e.g., BA for British Airways)"),
    flight_number: str = Query(..., description="Flight number without carrier code (e.g., 876)"),
    scheduled_departure_date: str = Query(..., description="Scheduled departure date (YYYY-MM-DD)"),
    use_real_data: bool = Query(True, description="If True, try to get real data from Amadeus API. If False, generate synthetic data.")
) -> Dict[str, Any]:
    """
    Get the current status of a flight.
    """
    try:
        service = get_flight_status_service()
        response = service.get_flight_status(
            carrier_code=carrier_code,
            flight_number=flight_number,
            scheduled_departure_date=scheduled_departure_date,
            use_real_data=use_real_data
        )
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 