import asyncio
import json
import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import io

from google.cloud import documentai
from google.api_core import exceptions as google_exceptions
from google.oauth2 import service_account

from .config import settings
from .intelligent_router import ProcessingStrategy, DocumentAnalysis

logger = logging.getLogger(__name__)

@dataclass
class ProcessingResult:
    """Result from Google Document AI processing"""
    success: bool
    text: str
    confidence: float
    processor_used: str
    processing_time: float
    page_count: int
    entities: List[Dict[str, Any]]
    tables: List[Dict[str, Any]]
    form_fields: List[Dict[str, Any]]
    error_message: Optional[str] = None
    raw_response: Optional[Any] = None

class GoogleDocumentAIHub:
    """    
    Provides comprehensive integration with all Google Document AI processors
    with intelligent routing, parallel processing, and error handling.
    """
    
    def __init__(self):
        self.client = None
        self.processors = {}
        self.credentials = None
        self._initialize_client()
        self._setup_processors()
        
    def _initialize_client(self):
        """Initialize Google Document AI client"""
        try:
            if settings.GOOGLE_CREDENTIALS_JSON:
                # Parse credentials from JSON string
                credentials_info = json.loads(settings.GOOGLE_CREDENTIALS_JSON)
                self.credentials = service_account.Credentials.from_service_account_info(credentials_info)
                self.client = documentai.DocumentProcessorServiceClient(credentials=self.credentials)
                logger.info("Google Document AI client initialized successfully")
            else:
                logger.warning("Google Document AI credentials not configured")
        except Exception as e:
            logger.error(f"Failed to initialize Google Document AI client: {str(e)}")
    
    def _setup_processors(self):
        """Setup processor configurations"""
        if not self.client or not settings.GOOGLE_PROJECT_ID:
            return

        # Simplified 4-processor mapping (anti-hallucination)
        processor_type_mapping = {
            "OCR_PROCESSOR": "ocr",
            "FORM_PARSER_PROCESSOR": "form_parser",
            "INVOICE_PROCESSOR": "invoice",
            "LAYOUT_PARSER_PROCESSOR": "layout_parser"
        }

        self.processors = {}
        for processor_type, processor_key in processor_type_mapping.items():
            if processor_key in settings.GOOGLE_PROCESSORS:
                processor_id = settings.GOOGLE_PROCESSORS[processor_key]
                self.processors[processor_type] = {
                    "type": processor_type,
                    "id": processor_id,
                    "name": f"projects/{settings.GOOGLE_PROJECT_ID}/locations/{settings.GOOGLE_LOCATION}/processors/{processor_id}",
                    "capabilities": settings.PROCESSOR_CAPABILITIES.get(processor_type, {}),
                    "max_pages": settings.PROCESSOR_CAPABILITIES.get(processor_type, {}).get("max_pages", 15)
                }
        
        logger.info(f"Configured {len(self.processors)} Google Document AI processors")
    
    async def process_document(
        self, 
        document_data: bytes, 
        strategy: ProcessingStrategy,
        filename: str = "document"
    ) -> ProcessingResult:
        """
        Process document using optimal Google Document AI strategy
        
        Args:
            document_data: Raw document bytes
            strategy: Processing strategy from intelligent router
            filename: Original filename
            
        Returns:
            ProcessingResult: Comprehensive processing result
        """
        
        if not self.client:
            return self._create_error_result("Google Document AI client not initialized")
        
        start_time = time.time()
        
        try:
            # Primary processing
            primary_result = await self._process_with_processor(
                document_data, 
                strategy.primary_processor,
                filename
            )
            
            # Check if primary processing was successful
            if primary_result.success and primary_result.confidence >= strategy.confidence_threshold:
                logger.info(f"Primary processing successful with {strategy.primary_processor}")
                return primary_result
            
            # Enhancement processing if configured
            if strategy.enhancement_processors and strategy.parallel_processing:
                enhanced_result = await self._parallel_enhancement_processing(
                    document_data, 
                    strategy.enhancement_processors,
                    primary_result,
                    filename
                )
                if enhanced_result.success:
                    return enhanced_result
            
            # Fallback processing
            if strategy.fallback_strategy:
                for fallback_processor in strategy.fallback_strategy:
                    fallback_result = await self._process_with_processor(
                        document_data,
                        fallback_processor,
                        filename
                    )
                    if fallback_result.success:
                        logger.info(f"Fallback processing successful with {fallback_processor}")
                        return fallback_result
            
            # If all else fails, return the best result we got
            return primary_result if primary_result.success else self._create_error_result("All processing attempts failed")
            
        except Exception as e:
            logger.error(f"Error in document processing: {str(e)}")
            return self._create_error_result(f"Processing error: {str(e)}")
    
    async def _process_with_processor(
        self, 
        document_data: bytes, 
        processor_type: str,
        filename: str
    ) -> ProcessingResult:
        """
        Process document with specific Google Document AI processor
        
        Args:
            document_data: Raw document bytes
            processor_type: Type of processor to use
            filename: Original filename
            
        Returns:
            ProcessingResult: Processing result
        """
        
        start_time = time.time()
        
        try:
            # Get processor configuration
            processor_config = self.processors.get(processor_type)

            if not processor_config:
                return self._create_error_result(f"Processor {processor_type} not configured")
            
            # Determine MIME type
            mime_type = self._detect_mime_type(document_data, filename)
            
            # Create processing request
            request = documentai.ProcessRequest(
                name=processor_config["name"],
                raw_document=documentai.RawDocument(
                    content=document_data,
                    mime_type=mime_type
                ),
                # Enable advanced features
                process_options=documentai.ProcessOptions(
                    ocr_config=documentai.OcrConfig(
                        enable_native_pdf_parsing=True,
                        enable_image_quality_scores=True,
                        enable_symbol=True,
                        premium_features=documentai.OcrConfig.PremiumFeatures(
                            enable_selection_mark_detection=True,
                            enable_math_ocr=True
                        )
                    )
                )
            )
            
            # Process document
            logger.info(f"Processing with {processor_type} for {filename}")
            result = self.client.process_document(request=request)
            
            # Extract comprehensive information
            processing_result = self._extract_comprehensive_result(
                result.document, 
                processor_type,
                time.time() - start_time
            )
            
            logger.info(f"Processing completed: {processor_type}, confidence: {processing_result.confidence:.3f}")
            
            return processing_result
            
        except google_exceptions.GoogleAPIError as e:
            logger.error(f"Google API error with {processor_type}: {str(e)}")
            return self._create_error_result(f"Google API error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error with {processor_type}: {str(e)}")
            return self._create_error_result(f"Processing error: {str(e)}")
    
    async def _parallel_enhancement_processing(
        self,
        document_data: bytes,
        enhancement_processors: List[str],
        primary_result: ProcessingResult,
        filename: str
    ) -> ProcessingResult:
        """
        Run enhancement processors in parallel and merge results
        
        Args:
            document_data: Raw document bytes
            enhancement_processors: List of enhancement processors
            primary_result: Primary processing result
            filename: Original filename
            
        Returns:
            ProcessingResult: Enhanced processing result
        """
        
        try:
            # Run enhancement processors in parallel
            tasks = [
                self._process_with_processor(document_data, processor, filename)
                for processor in enhancement_processors
            ]
            
            enhancement_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Merge results
            merged_result = self._merge_processing_results(primary_result, enhancement_results)
            
            return merged_result
            
        except Exception as e:
            logger.error(f"Error in parallel enhancement processing: {str(e)}")
            return primary_result
    
    def _extract_comprehensive_result(
        self, 
        document: documentai.Document, 
        processor_type: str,
        processing_time: float
    ) -> ProcessingResult:
        """
        Extract comprehensive information from Google Document AI response
        
        Args:
            document: Google Document AI document object
            processor_type: Type of processor used
            processing_time: Time taken for processing
            
        Returns:
            ProcessingResult: Comprehensive processing result
        """
        
        # Extract text
        text = document.text if document.text else ""
        
        # Calculate confidence score
        confidence = self._calculate_confidence(document)
        
        # Extract entities
        entities = self._extract_entities(document)
        
        # Extract tables
        tables = self._extract_tables(document)
        
        # Extract form fields
        form_fields = self._extract_form_fields(document)
        
        # Get page count
        page_count = len(document.pages) if document.pages else 1
        
        return ProcessingResult(
            success=True,
            text=text,
            confidence=confidence,
            processor_used=processor_type,
            processing_time=processing_time,
            page_count=page_count,
            entities=entities,
            tables=tables,
            form_fields=form_fields,
            raw_response=document
        )
    
    def _calculate_confidence(self, document: documentai.Document) -> float:
        """Calculate overall confidence score from document"""
        
        if not document.pages:
            return 0.5
        
        total_confidence = 0.0
        total_elements = 0
        
        for page in document.pages:
            # Text confidence
            if page.tokens:
                for token in page.tokens:
                    if hasattr(token, 'text_anchor') and hasattr(token, 'confidence'):
                        total_confidence += token.confidence
                        total_elements += 1
            
            # Form field confidence
            if page.form_fields:
                for field in page.form_fields:
                    if hasattr(field, 'confidence'):
                        total_confidence += field.confidence
                        total_elements += 1
        
        if total_elements == 0:
            return 0.8  # Default confidence for successful processing
        
        return min(1.0, total_confidence / total_elements)
    
    def _extract_entities(self, document: documentai.Document) -> List[Dict[str, Any]]:
        """Extract entities from document"""
        entities = []
        
        if document.entities:
            for entity in document.entities:
                entities.append({
                    "type": entity.type_,
                    "mention_text": entity.mention_text,
                    "confidence": getattr(entity, 'confidence', 0.0),
                    "normalized_value": getattr(entity.normalized_value, 'text', '') if entity.normalized_value else ''
                })
        
        return entities

    def _extract_tables(self, document: documentai.Document) -> List[Dict[str, Any]]:
        """Extract tables from document"""
        tables = []

        if not document.pages:
            return tables

        for page in document.pages:
            if page.tables:
                for table in page.tables:
                    table_data = {
                        "rows": [],
                        "header_rows": [],
                        "confidence": getattr(table, 'confidence', 0.0)
                    }

                    # Extract table rows
                    if table.body_rows:
                        for row in table.body_rows:
                            row_data = []
                            if row.cells:
                                for cell in row.cells:
                                    cell_text = self._get_text_from_layout(cell.layout, document.text) if cell.layout else ""
                                    row_data.append(cell_text.strip())
                            table_data["rows"].append(row_data)

                    # Extract header rows
                    if table.header_rows:
                        for row in table.header_rows:
                            row_data = []
                            if row.cells:
                                for cell in row.cells:
                                    cell_text = self._get_text_from_layout(cell.layout, document.text) if cell.layout else ""
                                    row_data.append(cell_text.strip())
                            table_data["header_rows"].append(row_data)

                    tables.append(table_data)

        return tables

    def _extract_form_fields(self, document: documentai.Document) -> List[Dict[str, Any]]:
        """Extract form fields from document"""
        form_fields = []

        if not document.pages:
            return form_fields

        for page in document.pages:
            if page.form_fields:
                for field in page.form_fields:
                    field_name = self._get_text_from_layout(field.field_name.layout, document.text) if field.field_name and field.field_name.layout else ""
                    field_value = self._get_text_from_layout(field.field_value.layout, document.text) if field.field_value and field.field_value.layout else ""

                    form_fields.append({
                        "name": field_name.strip(),
                        "value": field_value.strip(),
                        "confidence": getattr(field, 'confidence', 0.0)
                    })

        return form_fields

    def _get_text_from_layout(self, layout, document_text: str) -> str:
        """Extract text from layout object"""
        if not layout or not layout.text_anchor:
            return ""

        text_segments = []
        for segment in layout.text_anchor.text_segments:
            start_index = int(segment.start_index) if segment.start_index else 0
            end_index = int(segment.end_index) if segment.end_index else len(document_text)
            text_segments.append(document_text[start_index:end_index])

        return "".join(text_segments)

    def _merge_processing_results(
        self,
        primary_result: ProcessingResult,
        enhancement_results: List[ProcessingResult]
    ) -> ProcessingResult:
        """
        Merge primary and enhancement processing results

        Args:
            primary_result: Primary processing result
            enhancement_results: List of enhancement results

        Returns:
            ProcessingResult: Merged result
        """

        # Start with primary result
        merged_text = primary_result.text
        merged_entities = primary_result.entities.copy()
        merged_tables = primary_result.tables.copy()
        merged_form_fields = primary_result.form_fields.copy()

        # Merge enhancement results
        for result in enhancement_results:
            if isinstance(result, ProcessingResult) and result.success:
                # Merge entities (avoid duplicates)
                for entity in result.entities:
                    if entity not in merged_entities:
                        merged_entities.append(entity)

                # Merge tables (avoid duplicates)
                for table in result.tables:
                    if table not in merged_tables:
                        merged_tables.append(table)

                # Merge form fields (avoid duplicates)
                for field in result.form_fields:
                    if field not in merged_form_fields:
                        merged_form_fields.append(field)

        # Calculate merged confidence
        merged_confidence = primary_result.confidence
        valid_results = [r for r in enhancement_results if isinstance(r, ProcessingResult) and r.success]
        if valid_results:
            all_confidences = [primary_result.confidence] + [r.confidence for r in valid_results]
            merged_confidence = sum(all_confidences) / len(all_confidences)

        return ProcessingResult(
            success=True,
            text=merged_text,
            confidence=merged_confidence,
            processor_used=f"{primary_result.processor_used}+enhancements",
            processing_time=primary_result.processing_time,
            page_count=primary_result.page_count,
            entities=merged_entities,
            tables=merged_tables,
            form_fields=merged_form_fields,
            raw_response=primary_result.raw_response
        )

    def _detect_mime_type(self, document_data: bytes, filename: str) -> str:
        """Detect MIME type for document"""

        # Try to detect from file extension first
        extension = filename.split('.')[-1].lower() if '.' in filename else ""

        extension_mapping = {
            'pdf': 'application/pdf',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'tiff': 'image/tiff',
            'tif': 'image/tiff',
            'webp': 'image/webp'
        }

        if extension in extension_mapping:
            return extension_mapping[extension]

        # Fallback to magic number detection
        try:
            import magic
            return magic.from_buffer(document_data, mime=True)
        except:
            # Final fallback
            if document_data.startswith(b'%PDF'):
                return 'application/pdf'
            elif document_data.startswith(b'\x89PNG'):
                return 'image/png'
            elif document_data.startswith(b'\xff\xd8\xff'):
                return 'image/jpeg'
            else:
                return 'application/octet-stream'

    def _create_error_result(self, error_message: str) -> ProcessingResult:
        """Create error processing result"""
        return ProcessingResult(
            success=False,
            text="",
            confidence=0.0,
            processor_used="none",
            processing_time=0.0,
            page_count=0,
            entities=[],
            tables=[],
            form_fields=[],
            error_message=error_message
        )

    async def batch_process_documents(
        self,
        documents: List[Tuple[bytes, str, ProcessingStrategy]],
        max_concurrent: int = 5
    ) -> List[ProcessingResult]:
        """
        Process multiple documents in batch with concurrency control

        Args:
            documents: List of (document_data, filename, strategy) tuples
            max_concurrent: Maximum concurrent processing

        Returns:
            List of ProcessingResult objects
        """

        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_single(doc_data, filename, strategy):
            async with semaphore:
                return await self.process_document(doc_data, strategy, filename)

        tasks = [
            process_single(doc_data, filename, strategy)
            for doc_data, filename, strategy in documents
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Convert exceptions to error results
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append(self._create_error_result(f"Batch processing error: {str(result)}"))
            else:
                processed_results.append(result)

        return processed_results

    def get_processor_status(self) -> Dict[str, Any]:
        """Get status of all configured processors"""

        status = {
            "client_initialized": self.client is not None,
            "credentials_configured": self.credentials is not None,
            "project_id": settings.GOOGLE_PROJECT_ID,
            "location": settings.GOOGLE_LOCATION,
            "processors": {}
        }

        for proc_name, config in self.processors.items():
            status["processors"][proc_name] = {
                "type": config["type"],
                "configured": True,
                "max_pages": config["max_pages"],
                "capabilities": config["capabilities"]
            }

        return status

# Global Google Document AI hub instance
google_ai_hub = GoogleDocumentAIHub()
