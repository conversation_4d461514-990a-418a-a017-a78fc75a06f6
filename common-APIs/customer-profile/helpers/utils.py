"""Contains utility functions"""
import os
from helpers.dynamo_helper import (scan_table_with_filter, query_table_with_gsi)
def get_next_number_in_sequence(table_name, key_name):
    """
    function to get next customer id
    Args:
        table_name: table name
        key_name: key name

    Returns:
         next sequence number provided primary key
    """
    if os.environ.get(key_name):
        next_Index = int(os.environ.get(key_name)) + 1
    else:
        items= scan_table_with_filter(table_name)
        if items:
            last_Index= max(int(item[key_name]) for item in items)
            next_Index = last_Index + 1 
        else:
            next_Index = 1000000
    os.environ[key_name] = str(next_Index)

    return str(next_Index)

def check_if_item_already_present(table_name, index_name, key_name, key_value):
    item= query_table_with_gsi(table_name, index_name, key_name, key_value)
    if item:
        return {"isAvailable": True, "data": item}
    else:
        return {"isAvailable": False, "data": None}