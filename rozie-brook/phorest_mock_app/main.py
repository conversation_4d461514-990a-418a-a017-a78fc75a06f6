from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from datetime import datetime, timedelta
import random
import json
import os

app = FastAPI()

# Load JSON data
JSON_FILE_PATH = "phorest_mock_app/data/mock_api_dump.json"
if not os.path.exists(JSON_FILE_PATH):
    raise FileNotFoundError(f"JSON file '{JSON_FILE_PATH}' not found. Please ensure the file exists.")

with open(JSON_FILE_PATH, "r") as file:
    data = json.load(file)

# Request Models
class LocationRequest(BaseModel):
    business_id: str
    chat_id: str

class StaffRequest(LocationRequest):
    Salons_Branch_Name: str

class ServiceRequest(StaffRequest):
    Stylist_Name: str

class PriceRequest(StaffRequest):
    Salon_Service: list[str]
    
class SlotRequest(BaseModel):
    business_id: str
    chat_id: str
    Salons_Branch_Name: str
    Salon_Service: list[str]
    Stylist_Name: str


# API Endpoints
@app.post("/brook_phorest/get_location_details")
def get_location_details(request: LocationRequest):
    """
    Retrieves the list of salon branch names and addresses.
    """
    try:
        response = [
            {"name": branch, "address": branch_data.get("address", "N/A")}
            for branch, branch_data in data.items()
        ]
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving location details: {str(e)}")

@app.post("/brook_phorest/get_staff_details")
def get_staff_details(request: StaffRequest):
    """
    Retrieves the list of staff details for a specific branch.
    """
    try:
        branch_data = data.get(request.Salons_Branch_Name)
        if not branch_data:
            raise HTTPException(status_code=404, detail=f"Branch '{request.Salons_Branch_Name}' not found.")

        # Debug log for branch data
        print(f"Branch data for '{request.Salons_Branch_Name}': {branch_data}")

        # Ensure branch_data is a dictionary
        if not isinstance(branch_data, dict):
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected data structure for branch '{request.Salons_Branch_Name}'. Expected dictionary."
            )

        # Extract staff details
        response = [
            {
                "name": staff["staff_details"]["name"],
                "staffCategoryName": staff["staff_details"]["staffCategoryName"],
                "staffCategoryId": staff["staff_details"]["staffCategoryId"],
                "aboutMe": staff["staff_details"]["aboutMe"],
                "staffId": staff["staff_details"]["staffId"],
            }
            for staff in branch_data.values() if "staff_details" in staff
        ]
        return response
    except KeyError as e:
        raise HTTPException(status_code=500, detail=f"Missing key in data: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving staff details: {str(e)}")

@app.post("/brook_phorest/get_services_and_packages")
def get_services_and_packages(request: ServiceRequest):
    """
    Retrieves the list of services and packages for a specific stylist in a branch.
    """
    try:
        branch_data = data.get(request.Salons_Branch_Name)
        if not branch_data:
            raise HTTPException(status_code=404, detail=f"Branch '{request.Salons_Branch_Name}' not found.")

        stylist_data = branch_data.get(request.Stylist_Name)
        if not stylist_data:
            raise HTTPException(status_code=404, detail=f"Stylist '{request.Stylist_Name}' not found.")

        response = stylist_data.get("services", [])
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving services and packages: {str(e)}")

@app.post("/brook_phorest/get_salon_prices")
def get_salon_prices(request: PriceRequest):
    """
    Retrieves the prices for specific services in a branch.
    """
    try:
        branch_data = data.get(request.Salons_Branch_Name)
        if not branch_data:
            raise HTTPException(status_code=404, detail=f"Branch '{request.Salons_Branch_Name}' not found.")

        # Validate branch_data is a dictionary
        if not isinstance(branch_data, dict):
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected data structure for branch '{request.Salons_Branch_Name}'. Expected dictionary."
            )

        response = {}
        for service in request.Salon_Service:
            # Collect prices for the service
            service_prices = [
                {"name": stylist["staff_details"]["name"], "price": service_data["price"]}
                for stylist in branch_data.values()
                if isinstance(stylist, dict)  # Ensure stylist is a dictionary
                for service_data in stylist.get("services", [])
                if service_data.get("service_name") == service
            ]
            response[service] = service_prices if service_prices else "Service not found."
        return response
    except KeyError as e:
        raise HTTPException(status_code=500, detail=f"Missing key in data: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving salon prices: {str(e)}")

@app.post("/brook_phorest/get_available_slots")
def get_available_slots(request: SlotRequest):
    """
    Dynamically generates available slots for a specific stylist and services in a branch
    by fetching service durations from the JSON data.
    """
    try:
        # Fetch branch data
        branch_data = data.get(request.Salons_Branch_Name)
        if not branch_data:
            raise HTTPException(status_code=404, detail=f"Branch '{request.Salons_Branch_Name}' not found.")

        # Fetch stylist data
        stylist_data = branch_data.get(request.Stylist_Name)
        if not stylist_data:
            raise HTTPException(status_code=404, detail=f"Stylist '{request.Stylist_Name}' not found in branch '{request.Salons_Branch_Name}'.")

        # Map services to their durations
        services = stylist_data.get("services", [])
        service_durations = {
            service["service_name"]: service["duration"]
            for service in services
            if "service_name" in service and "duration" in service
        }

        # Validate requested services
        missing_services = [s for s in request.Salon_Service if s not in service_durations]
        if missing_services:
            raise HTTPException(status_code=400, detail=f"Requested services not found: {missing_services}")

        # Generate random slots for the next 28 days
        def generate_random_slots(service_list, stylist_name, days=28):
            slots = []
            start_date = datetime.utcnow()
            for _ in range(10):  # Generate 10 slots
                random_day = random.randint(0, days - 1)  # Random day within the range
                random_hour = random.randint(9, 17)  # Random hour between 9 AM to 5 PM
                random_minute = random.choice([0, 15, 30, 45])  # Random quarter-hour increment
                appointment_start_time = start_date + timedelta(days=random_day, hours=random_hour, minutes=random_minute)

                # Create schedule for the services
                schedule = []
                service_start_time = appointment_start_time
                for service_name in service_list:
                    service_duration = service_durations.get(service_name, 30)  # Default to 30 if duration not found
                    service_end_time = service_start_time + timedelta(minutes=service_duration)
                    schedule.append({
                        "service_name": service_name,
                        "schedule_start_time": service_start_time.strftime("%Y-%m-%dT%H:%M:%S"),
                        "schedule_end_time": service_end_time.strftime("%Y-%m-%dT%H:%M:%S"),
                    })
                    service_start_time = service_end_time

                slots.append({
                    "appointment_start_time": appointment_start_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    "schedule": schedule,
                    "staff_member": stylist_name,
                })

            return slots

        # Generate the random slots
        slots = generate_random_slots(request.Salon_Service, request.Stylist_Name)

        # Mock response
        response = {
            "message": "Slots available for the requested stylist for the next 28 days",
            "slots": slots,
        }
        return response
    except KeyError as e:
        raise HTTPException(status_code=500, detail=f"Missing key in data: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving available slots: {str(e)}")


@app.post("/brook_phorest/make_booking")
def make_booking(request: dict):
    """
    Makes a booking for a specific service at a salon branch.
    """
    try:
        # Extract request details
        date_of_appointment = request.get("Date_of_Appointment")
        service_description = request.get("Service_Description")
        appointment_time = request.get("Appointment_Time")

        # Validate required fields
        if not all([date_of_appointment, service_description, appointment_time]):
            raise HTTPException(status_code=400, detail="Missing required fields in the request.")

        # Create a professional success message
        success_message = (
            f"Your appointment has been successfully scheduled!\n"
            f"Appointment Details:\n"
            f"- **Date**: {date_of_appointment}\n"
            f"- **Time**: {appointment_time}\n"
            f"- **Service**: {service_description}\n"
            f"We look forward to seeing you!"
        )

        return {"message": success_message}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error making booking: {str(e)}")


@app.post("/brook_phorest/get_valid_stylist_options")
def get_valid_stylist_options(request: dict):
    """
    Retrieves valid stylist options for a specific service at a branch.
    """
    try:
        # Extract request details
        business_id = request.get("business_id")
        chat_id = request.get("chat_id")
        branch_name = request.get("Salons_Branch_Name")
        salon_service = request.get("Salon_Service")
        stylist_name = request.get("Stylist_Name")

        # Validate required fields
        if not all([business_id, chat_id, branch_name, salon_service, stylist_name]):
            raise HTTPException(status_code=400, detail="Missing required fields in the request.")

        # Mock response for valid stylist options
        # Here we assume that the stylist name provided in the request is valid
        # In a real-world application, you would validate this against your database or logic
        response = {
            "possible_valid_options": [stylist_name]
        }
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving stylist options: {str(e)}")

@app.get("/readiness")
def readiness():
    return {"status": "healthy"}