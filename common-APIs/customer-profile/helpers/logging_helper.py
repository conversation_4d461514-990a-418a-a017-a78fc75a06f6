"""
This module contains helper functions used for the logging information.

Functions:
- print_log
- log_exception
- set_logging_level
- get_log_level

"""

import logging
import os

enable_logs = os.environ.get("ENABLE_LOG", "False")

def print_info_log(initial_contact_id, key, value):
    """
    This function is used for logging the info.

    Args:
        initial_contact_id (string): contact id of the connect call.
        key (string): key to log.
        value (any): value to log.
        enable_logs (string): flag to decide logging of info
    """
    if enable_logs.lower() == "true":
        logging.info("%s ==> %s: %s", initial_contact_id, key, value)
        # print(key,value)
        # print()


def print_error_log(initial_contact_id, exception):
    """
    Log an exception message with the ERROR level.

    Args:
        exception: The exception object or message to be logged.
    """
    logging.exception(
        "%s ==> ERROR: Lambda exception %s",
        initial_contact_id,
        str(exception),
        exc_info=True,
    )


def log_exception(exception):
    """
    Log an exception message with the ERROR level.

    Args:
        exception: The exception object or message to be logged.
    """
    logging.exception("ERROR: Lambda exception %s", str(exception))


def set_logging_level(log_level):
    """
    Set the logging level for the root logger.

    Args:
        log_level: The logging level to be set (e.g., logging.DEBUG, logging.INFO).
    """
    logging.getLogger().setLevel(log_level)


def get_log_level():
    """
    This function retrieves the logging level from the environment variable LOG_LEVEL
    and returns it. If the environment variable is not set or the value is not a valid
    logging level,the function returns "ERROR"
    """
    log_level = os.environ.get("LOG_LEVEL", "").upper()
    if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
        log_level = "ERROR"
    return log_level
