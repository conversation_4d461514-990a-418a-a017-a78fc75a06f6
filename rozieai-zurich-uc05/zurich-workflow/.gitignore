# =============================================================================
# ZURICH WORKFLOW PROJECT GITIGNORE
# =============================================================================

# Environment files - NEVER commit these!
.env
.env.local
.env.*.local
.env.development
.env.production
.env.staging

# =============================================================================
# PYTHON
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Virtual environments
venv/
env/
.venv/
.env/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# =============================================================================
# BAML (Boundary ML)
# =============================================================================

# Generated BAML client files (regenerated on build)
baml_models/baml_client/
baml_models/.baml/

# BAML logs
*.baml.log

# =============================================================================
# NODE.JS / DASHBOARD
# =============================================================================

# Dependencies
zurich-dashboard/node_modules/
frontend/**/node_modules/

# Logs
zurich-dashboard/npm-debug.log*
zurich-dashboard/yarn-debug.log*
zurich-dashboard/yarn-error.log*
frontend/**/npm-debug.log*
frontend/**/yarn-debug.log*
frontend/**/yarn-error.log*

# Runtime data
zurich-dashboard/pids
zurich-dashboard/*.pid
zurich-dashboard/*.seed
zurich-dashboard/*.pid.lock

# Coverage directory used by tools like istanbul
zurich-dashboard/coverage/
frontend/**/coverage/

# nyc test coverage
zurich-dashboard/.nyc_output
frontend/**/.nyc_output

# Build directories
zurich-dashboard/dist/
zurich-dashboard/build/
frontend/**/dist/
frontend/**/build/
frontend/**/.cache/

# =============================================================================
# DOCKER
# =============================================================================

# Docker build context files
.dockerignore

# =============================================================================
# AWS / SERVERLESS
# =============================================================================

# Serverless directories
.serverless/
.serverless_plugins/

# AWS exports
aws-exports.js
amplify-build-config.json
amplify-gradle-config.json
amplifytools.xcconfig

# CloudFormation templates (generated)
.aws-sam/

# =============================================================================
# LOGS
# =============================================================================

# Application logs
*.log
logs/
.log

# System logs
*.out
*.err

# =============================================================================
# TEMPORARY FILES
# =============================================================================

# Temporary directories
tmp/
temp/
.tmp/
.temp/

# Backup files
*.bak
*.backup
*.old

# =============================================================================
# IDE / EDITOR FILES
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.sublime-project
*.sublime-workspace

# =============================================================================
# OPERATING SYSTEM FILES
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# DEVELOPMENT / TESTING FILES
# =============================================================================

# Local development files
local_test.py
test_*.py
debug.py
scratch.py
playground.py
experiment.py

# Test data
test_data/
sample_data/
mock_data/

# Development databases
*.db
*.sqlite
*.sqlite3

# =============================================================================
# SECURITY / SECRETS
# =============================================================================

# API keys and secrets (should be in .env but extra safety)
**/secrets.json
**/credentials.json
**/*_key.json
**/*_secret.json
**/api_keys.txt

# SSL certificates
*.pem
*.key
*.crt
*.cert

# =============================================================================
# DEPLOYMENT ARTIFACTS
# =============================================================================

# Deployment packages
*.zip
*.tar.gz
*.tgz

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# =============================================================================
# DOCUMENTATION BUILD
# =============================================================================

# Sphinx documentation
docs/_build/

# MkDocs documentation
/site

# =============================================================================
# MISC
# =============================================================================

# Thumbnails
*.thumb

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Compiled source
*.com
*.class
*.dll
*.exe
*.o
*.so
