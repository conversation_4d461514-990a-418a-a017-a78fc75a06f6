import time
from middleware_app.helpers.request_helper import post_request


def rozie_studio_navigator_request(
    chat_id, customer_details, channel_id, channel_name, customer_message=None
):
    """
    This function calls Rozie Studio API to get the response from LLM
    """
    user_id = {"id": chat_id, "id_type": "channel_user_id", "id_resource": "webchat"}

    base_request_body = {
        "version": "1.0",
        "user_info": {"user_id": user_id},
        "channel": {
            "channel_id": channel_id,
            "channel_name": "webchat",
            "ui_info": {
                "should_consolidate_buttons": True,
            },
            "reply_info": {"reply_mode": "sync"},
        },
        "incoming_events": [
            {
                "event_id": "event_0",
                "event_creator": {"event_creator_type": "user", "user_id": user_id},
                "event_template": {
                    "event_type": "text",
                    "text": customer_message,
                },
            },
        ],
        "metadata": {
            "customer_profile": {
                "name": "rozie",
                "email": "<EMAIL>",
                "location": "New York",
                "gender": "Male",
            },
            "channel": {"time_zone": "America/New_York"},
        },
    }
    if customer_details:
        if customer_details.get("user_name"):
            base_request_body["metadata"]["customer_profile"]["name"] = (
                customer_details.get("user_name")
            )
        if customer_details.get("phone_number"):
            base_request_body["metadata"]["customer_profile"]["mobile"] = (
                customer_details.get("phone_number")
            )

    return base_request_body


def rozie_studio_navigator_make_call(params, lang="en_us"):
    """
    This function calls Rozie Studio API to make a call
    """
    url = "https://api-manager-sandbox.rozie.ai/event-adapter/v1/adapters-event"
    headers = {
        "Content-Type": "application/json",
        "api-key": "5b5fadd08ddc4295abfa854244cbfbb2",
        "application-id": "application_9f6d6202-f5d9-445d-b856-82b44844f0d4",
        "api-key": "5b5fadd08ddc4295abfa854244cbfbb2",
        "language": lang,
        "rozie-correlation-id": str(int(time.time())),
        "channel": "webchat"
    }

    status, response = post_request(url, headers, json_body=params)
    print("response", response)
    if status == "Success":
        return response["data"]
    else:
        return None


def rozie_studio_navigator_call(
    chat_id,
    channel_id,
    channel_name,
    customer_details,
    customer_message
):
    """
    This function calls Rozie Studio API to get the response from LLM
    """
    param = rozie_studio_navigator_request(
        chat_id,
        customer_details,
        channel_id,
        channel_name,
        customer_message
    )
    print(param)
    result = rozie_studio_navigator_make_call(param)
    if result:
        return result
    return {"error": "Failed to Send/Initiate message"}
