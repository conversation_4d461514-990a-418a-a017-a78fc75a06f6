/* eslint-disable no-case-declarations */
const { ApiResp } = require("./helpers/api-helper.js")
const { convertAirlineCode } = require("./flight-util-helpers/convert-airline-code.js")

/**
 * AWS Lambda handler function to manage API requests for converting airline codes.
 *
 * This function handles API requests for converting either an airline code to an airline name
 * or vice versa. It determines the correct function to execute based on the provided API
 * resource path. Currently, it supports the `/convertAirlineCode` endpoint, which accepts 
 * a request body containing either an `airportCode` or `airportName`.
 * 
 * @param {Object} event - The event object from the API Gateway that triggers the Lambda function.
 * @param {string} event.resource - The resource path of the API request, used to determine 
 *                                  which operation to execute.
 * @param {string} event.body - The body of the API request, typically a JSON string containing
 *                              the necessary data such as an airport code or name.
 * 
 * @returns {Promise<Object>} - A promise that resolves to an API response object, containing 
 *                              the status, message, and any relevant data from the operation.
 * 
 * @throws {Error} - If an error occurs during processing, the error is caught, logged, and 
 *                   an appropriate error response is returned to the API caller.
 */
const lambda_handler = async (event) => {
	console.log(JSON.stringify(event))
	const path = event.resource
	try {
		switch (path) {
		case "/convertAirlineCode":
			const body = JSON.parse(event.body)
			const convertResponse = await convertAirlineCode(body)
			return convertResponse

		default:
			return ApiResp({
				status: "Error",
				message: "Invalid resource path"
			}, 400)
		}
	} catch (error) {
		console.error("Error occurred in Lambda:", error)
		return ApiResp({
			status: "Error",
			message: "Internal Server Error"
		}, 500)
	}
}

module.exports = {
	lambda_handler,
}
