# Zurich OCR Engine - Production Dockerfile
# Advanced OCR solution with LLM-powered intelligent routing

# Use Python 3.12 slim image for optimal performance
FROM python:3.12.5-slim

# Build arguments for deployment
ARG FASTAPI_ENV=production
ARG AWS_REGION=ca-central-1
ARG OPENAI_API_KEY=""
ARG GOOGLE_PROJECT_ID=""
ARG GOOGLE_CREDENTIALS_JSON=""

# Set environment variables with hardcoded values for development
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    DEBIAN_FRONTEND=noninteractive \
    FASTAPI_ENV=${FASTAPI_ENV} \
    AWS_REGION=${AWS_REGION} \
    OPENAI_API_KEY="********************************************************************************************************************************************************************" \
    GOOGLE_PROJECT_ID="************" \
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Install system dependencies required for OCR and image processing
RUN apt-get update && apt-get install -y \
    # Tesseract OCR and language packs
    tesseract-ocr \
    tesseract-ocr-eng \
    tesseract-ocr-fra \
    tesseract-ocr-deu \
    tesseract-ocr-spa \
    tesseract-ocr-ita \
    # Image processing libraries
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    # PDF processing
    poppler-utils \
    # File type detection
    libmagic1 \
    # Archive processing
    p7zip-full \
    # Build tools for Python packages
    gcc \
    g++ \
    # Health check utility
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set the working directory
WORKDIR /app

# Copy requirements first for better Docker layer caching
COPY requirements.txt /app/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY zurich_ocr /app/zurich_ocr

# Create necessary directories
RUN mkdir -p /app/temp /app/logs

# Set proper permissions
RUN chmod -R 755 /app

# Expose port 8000 (FastAPI default)
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/readiness || exit 1

# Command to run the application
CMD ["uvicorn", "zurich_ocr.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]
