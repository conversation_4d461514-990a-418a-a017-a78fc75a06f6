.DS_Store
# package directories
node_modules
jspm_packages

# Serverless directories
.serverless

layer

# =============================================================================
# ROZIEAI-ZURICH-UC05 SPECIFIC IGNORES
# =============================================================================

# Environment files
rozieai-zurich-uc05/zurich-workflow/.env
rozieai-zurich-uc05/zurich-workflow/.env.local
rozieai-zurich-uc05/zurich-workflow/.env.*.local
rozieai-zurich-uc05/zurich-workflow/.env.development
rozieai-zurich-uc05/zurich-workflow/.env.production

# Python cache and compiled files
rozieai-zurich-uc05/zurich-workflow/**/__pycache__/
rozieai-zurich-uc05/zurich-workflow/**/*.py[cod]
rozieai-zurich-uc05/zurich-workflow/**/*$py.class
rozieai-zurich-uc05/zurich-workflow/**/*.so
rozieai-zurich-uc05/zurich-workflow/**/.Python
rozieai-zurich-uc05/zurich-workflow/**/build/
rozieai-zurich-uc05/zurich-workflow/**/develop-eggs/
rozieai-zurich-uc05/zurich-workflow/**/dist/
rozieai-zurich-uc05/zurich-workflow/**/downloads/
rozieai-zurich-uc05/zurich-workflow/**/eggs/
rozieai-zurich-uc05/zurich-workflow/**/.eggs/
rozieai-zurich-uc05/zurich-workflow/**/lib/
rozieai-zurich-uc05/zurich-workflow/**/lib64/
rozieai-zurich-uc05/zurich-workflow/**/parts/
rozieai-zurich-uc05/zurich-workflow/**/sdist/
rozieai-zurich-uc05/zurich-workflow/**/var/
rozieai-zurich-uc05/zurich-workflow/**/wheels/
rozieai-zurich-uc05/zurich-workflow/**/*.egg-info/
rozieai-zurich-uc05/zurich-workflow/**/.installed.cfg
rozieai-zurich-uc05/zurich-workflow/**/*.egg

# BAML generated client files (keep source files, ignore generated)
rozieai-zurich-uc05/zurich-workflow/baml_models/baml_client/
rozieai-zurich-uc05/zurich-workflow/baml_models/.baml/

# Virtual environments
rozieai-zurich-uc05/zurich-workflow/venv/
rozieai-zurich-uc05/zurich-workflow/env/
rozieai-zurich-uc05/zurich-workflow/.venv/
rozieai-zurich-uc05/zurich-workflow/.env/
rozieai-zurich-uc05/zurich-workflow/ENV/
rozieai-zurich-uc05/zurich-workflow/env.bak/
rozieai-zurich-uc05/zurich-workflow/venv.bak/

# Node.js dependencies and build files
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/node_modules/
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/npm-debug.log*
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/yarn-debug.log*
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/yarn-error.log*
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/.npm
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/.yarn-integrity
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/coverage/
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/.nyc_output
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/.cache/
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/dist/
rozieai-zurich-uc05/zurich-workflow/zurich-dashboard/build/

# Frontend build files
rozieai-zurich-uc05/zurich-workflow/frontend/**/node_modules/
rozieai-zurich-uc05/zurich-workflow/frontend/**/dist/
rozieai-zurich-uc05/zurich-workflow/frontend/**/build/
rozieai-zurich-uc05/zurich-workflow/frontend/**/.cache/

# Docker files
rozieai-zurich-uc05/zurich-workflow/.dockerignore

# Logs
rozieai-zurich-uc05/zurich-workflow/**/*.log
rozieai-zurich-uc05/zurich-workflow/**/logs/
rozieai-zurich-uc05/zurich-workflow/**/.log

# IDE and editor files
rozieai-zurich-uc05/zurich-workflow/**/.vscode/
rozieai-zurich-uc05/zurich-workflow/**/.idea/
rozieai-zurich-uc05/zurich-workflow/**/*.swp
rozieai-zurich-uc05/zurich-workflow/**/*.swo
rozieai-zurich-uc05/zurich-workflow/**/*~

# Temporary files
rozieai-zurich-uc05/zurich-workflow/**/tmp/
rozieai-zurich-uc05/zurich-workflow/**/temp/
rozieai-zurich-uc05/zurich-workflow/**/.tmp/
rozieai-zurich-uc05/zurich-workflow/**/.temp/

# Testing
rozieai-zurich-uc05/zurich-workflow/**/.pytest_cache/
rozieai-zurich-uc05/zurich-workflow/**/.coverage
rozieai-zurich-uc05/zurich-workflow/**/htmlcov/
rozieai-zurich-uc05/zurich-workflow/**/.tox/

# Jupyter Notebook
rozieai-zurich-uc05/zurich-workflow/**/.ipynb_checkpoints

# pyenv
rozieai-zurich-uc05/zurich-workflow/**/.python-version

# Serverless specific for zurich-workflow
rozieai-zurich-uc05/zurich-workflow/.serverless/
rozieai-zurich-uc05/zurich-workflow/.serverless_plugins/

# AWS and deployment artifacts
rozieai-zurich-uc05/zurich-workflow/**/aws-exports.js
rozieai-zurich-uc05/zurich-workflow/**/amplify-build-config.json
rozieai-zurich-uc05/zurich-workflow/**/amplify-gradle-config.json
rozieai-zurich-uc05/zurich-workflow/**/amplifytools.xcconfig

# Local development files
rozieai-zurich-uc05/zurich-workflow/local_test.py
rozieai-zurich-uc05/zurich-workflow/test_*.py
rozieai-zurich-uc05/zurich-workflow/debug.py
rozieai-zurich-uc05/zurich-workflow/scratch.py