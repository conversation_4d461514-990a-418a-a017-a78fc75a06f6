const { ApiResp } = require("../helpers/api-helper.js")
const { updateDynamoTableDataWithParams, retrieveDynamoEntry } = require("../helpers/dynamo-helper.js")

/**
 * @function updatePNR
 * @description This function updates an existing PNR entry in a DynamoDB table based on the provided data.
 * 
 * @param {Object} body - The request body containing customer and booking details to update.
 * @param {string} body.customer_id - The customer ID for the PNR.
 * @param {string} body.flight_schedule_id - The flight schedule ID.
 * @param {number} [body.payment_amount] - (Optional) Updated payment amount.
 * @param {Array} body.travelers_details - Updated list of travelers' details.
 * 
 * @returns {Promise<Object>} - The response object indicating the result of the PNR update.
 * 
 * @throws {Error} - Throws an error if there is an issue during the PNR update process.
 */
const updatePNR = async (body) => {
	try {
		console.log("function: updatePNR body:", body)
		const { pnr_id, payment_amount, travelers_details, pnr_status, flight_schedule_id } = body

		const validationError = validatePNRInputs(pnr_id, travelers_details)
		if (validationError) {
			return ApiResp({ status: "Error", message: validationError }, 200)
		}


		const existingPNR = await retrieveDynamoEntry(process.env.PNR_TABLE_NAME, "pnr_id" ,pnr_id )
		if (!existingPNR) {
			return ApiResp({ status: "Error", message: "PNR not found" }, 200)
		}

		const flightScheduleData = await retrieveDynamoEntry(process.env.FLIGHT_SCHEDULE_TABLE, "scheduleId", flight_schedule_id)
		if (!flightScheduleData) {
			return ApiResp({ status: "Error", message: "Flight schedule not found" }, 200)
		}

		for (const previousTraveler of existingPNR.travelers_details) {
			const { seat_number } = previousTraveler
			await makeSeatAvailable(seat_number, flight_schedule_id, flightScheduleData)
		}

		for (const traveler of travelers_details) {
			const { seat_number } = traveler
			const seatBooked = await checkAndBookSeat(seat_number, flight_schedule_id, flightScheduleData)
			if (!seatBooked) {
				return ApiResp({ status: "Error", message: `Seat ${seat_number} is not available or booking failed` }, 200)
			}
		}

		const updateParams = {
			TableName: process.env.PNR_TABLE_NAME,
			Key: { pnr_id },
			UpdateExpression: "SET pnr_status = :pnr_status, payment_amount = :payment_amount, travelers_details = :travelers_details, updated_date = :updated_date",
			ExpressionAttributeValues: {
				":pnr_status": pnr_status || existingPNR.pnr_status,
				":payment_amount": payment_amount || existingPNR.payment_amount,
				":travelers_details": travelers_details || existingPNR.travelers_details,
				":updated_date": new Date().toISOString()
			}
		}

		await updateDynamoTableDataWithParams(updateParams)

		console.log("function: updatePNR successful for pnr_id:", pnr_id)
		return ApiResp({ status: "Success", message: "PNR updated successfully" }, 200)
	} catch (error) {
		console.error("Error in updatePNR:", error)
		return ApiResp({ status: "Error", message: "Internal server error while updating PNR" }, 500)
	}
}


const makeSeatAvailable = async (seat_number, flight_schedule_id) => {
	const row = seat_number.slice(0, -1) 
	const seat = seat_number.slice(-1)    

	const flight_schedule_data = await retrieveDynamoEntry(process.env.FLIGHT_SCHEDULE_TABLE, "scheduleId", flight_schedule_id)
    
	flight_schedule_data.seating[row][seat].status = "available"

	const updateParams = {
		TableName: process.env.FLIGHT_SCHEDULE_TABLE,
		Key: { scheduleId: flight_schedule_id },
		UpdateExpression: "set seating = :seating",
		ExpressionAttributeValues: {
			":seating": flight_schedule_data.seating
		},
		ReturnValues: "UPDATED_NEW"
	}

	await updateDynamoTableDataWithParams(updateParams)
}

async function checkAndBookSeat(seat_number, flight_schedule_id) {
	const flight_schedule_data = await retrieveDynamoEntry(process.env.FLIGHT_SCHEDULE_TABLE, "scheduleId", flight_schedule_id)
    
	const row = seat_number.slice(0, -1)  
	const seat = seat_number.slice(-1) 

	const seat_data = flight_schedule_data.seating[row][seat]

	console.log("seat_data:", seat_data)

	if (seat_data.status !== "available") {
		return false 
	}

    flight_schedule_data.seating[row][seat].status = "booked"

	const updateParams = {
		TableName: process.env.FLIGHT_SCHEDULE_TABLE,
		Key: { scheduleId: flight_schedule_id },
		UpdateExpression: "set seating = :seating",
		ExpressionAttributeValues: {
			":seating": flight_schedule_data.seating
		},
		ReturnValues: "UPDATED_NEW"
	}

	console.log("updateParams:", updateParams)

	try {
		await updateDynamoTableDataWithParams(updateParams)
	} catch (error) {
		console.error("Error updating seat status:", error)
		throw new Error("Failed to update seat status in the database.")
	}

	return true
}

const validatePNRInputs = (pnr_id, travelers_details) => {
	if (!pnr_id) {
		return "PNR ID is missing"
	}

	if (!travelers_details || travelers_details.length === 0) {
		return "Travelers details are missing or invalid"
	}

	for (const traveler of travelers_details) {
		const { first_name, seat_number } = traveler

		if (!first_name || !seat_number) {
			return "Missing traveler first name or seat number"
		}
	}

	return null
}

module.exports = {
	updatePNR
}
