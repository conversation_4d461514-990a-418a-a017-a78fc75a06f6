const { backOff } = require('exponential-backoff')
const { ConnectClient, UpdateContactAttributesCommand } = require("@aws-sdk/client-connect");

const connectClient = new ConnectClient({ region: 'ca-central-1' })

const setContactAttributes = async (InitialContactId, InstanceId, attributes) => {
    try {
        const input = { 
            InitialContactId: InitialContactId, 
            InstanceId: InstanceId, 
            Attributes: attributes,
          };
          const command = new UpdateContactAttributesCommand(input);
            const response = await backOff(
            () => connectClient.send(command),
            {
                jitter: 'full',
                numOfAttempts: 4,
                retry: (err) => {
                    console.log(err)
                    return true
                },
            }
        )
        return { message: 'Contact attributes updated successfully' }
    } catch (error) {
        console.log(error)
        throw new Error('Error while updating contact attributes')
    }
}

module.exports = {
    setContactAttributes,
}