service: rozie-air-resources

custom:
  dev:
    LayerBucket: scc-layers

provider:
  name: aws
  runtime: python3.9
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:Scan
        - dynamodb:DeleteItem
        - dynamodb:UpdateItem
        - dynamodb:Query
      Resource: "*"
  apiGateway:
    apiKeys:
      - ${self:service}-${self:provider.stage}-Api-Key

functions:
  mock-data-gen:
    name: ${self:service}-mock-data-gen-${self:provider.stage}
    handler: index.lambda_handler
    description: function to generate mock airline data
    layers:
      - !Ref TwilioLambdaLayer
    environment: 
      ENABLE_LOG: TRUE
      FLIGHTS_TABLE:  ${self:service}-flights-${self:provider.stage}
      CUSTOMERS_TABLE: ${self:service}-customers-${self:provider.stage}
      PNRS_TABLE: ${self:service}-pnrs-${self:provider.stage}
      LOYALTY_TABLE: ${self:service}-loyalty-details-${self:provider.stage}
      AIRPORT_MAPPING_TABLE: ${self:service}-airport-mapping-${self:provider.stage}
      BAGGAGE_TABLE: ${self:service}-baggage-${self:provider.stage}
    events:
      - http:
          path: /reset
          method: post
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
resources:
  extensions:
    ServerlessDeploymentBucket:
      Properties:
        PublicAccessBlockConfiguration:
          BlockPublicAcls: True
          BlockPublicPolicy: True
          IgnorePublicAcls: True
          RestrictPublicBuckets: True
  Resources:
    PNRDetailsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: pnr_id
            AttributeType: S
          - AttributeName: customer_id
            AttributeType: S
          - AttributeName: flight_id
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: pnr_id  
        GlobalSecondaryIndexes:
          - IndexName: CustomerIndex
            KeySchema:
              - AttributeName: customer_id
                KeyType: HASH  
            Projection:
              ProjectionType: ALL
          - IndexName: FlightIndex
            KeySchema:
              - AttributeName: flight_id
                KeyType: HASH  
            Projection:
              ProjectionType: ALL
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-pnrs-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True    
    CustomerProfileTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: customer_id
            AttributeType: S
          - AttributeName: phone_number
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: customer_id
        GlobalSecondaryIndexes:  
          - IndexName: PhoneNumberIndex  
            KeySchema:
              - AttributeName: phone_number
                KeyType: HASH 
            Projection:
              ProjectionType: ALL
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-customers-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    FlightDetailsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: flight_id
            AttributeType: S
          - AttributeName: arrival_airport
            AttributeType: S
          - AttributeName: departure_airport
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: flight_id
        GlobalSecondaryIndexes:
          - IndexName: ArrivalIndex
            KeySchema:
              - AttributeName: arrival_airport
                KeyType: HASH  
            Projection:
              ProjectionType: ALL
          - IndexName: DepartureIndex
            KeySchema:
              - AttributeName: departure_airport
                KeyType: HASH  
            Projection:
              ProjectionType: ALL
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-flights-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    LoyaltyDetailsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: customer_id
            AttributeType: S
          - AttributeName: loyalty_number
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: customer_id
        GlobalSecondaryIndexes:
          - IndexName: LoyaltyNumberIndex
            KeySchema:
              - KeyType: HASH
                AttributeName: loyalty_number
            Projection:
              ProjectionType: ALL
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-loyalty-details-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    AirportMappingTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: airportCode
            AttributeType: S
          - AttributeName: airportCityName
            AttributeType: S
        KeySchema:
          - AttributeName: airportCode
            KeyType: HASH  
        GlobalSecondaryIndexes:
          - IndexName: AirportCityNameIndex
            KeySchema:
              - AttributeName: airportCityName
                KeyType: HASH  
            Projection:
              ProjectionType: ALL
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-airport-mapping-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    caseManagementTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: case_id
            AttributeType: S
          - AttributeName: customer_id
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: case_id
        GlobalSecondaryIndexes:
          - IndexName: customerIdRetrieve
            KeySchema:
              - KeyType: HASH
                AttributeName: customer_id
            Projection:
              ProjectionType: "ALL"
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-case-management-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    BaggageDetailsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: baggage_tag
            AttributeType: S
          - AttributeName: pnr_id
            AttributeType: S
          # - AttributeName: flight_id
          #   AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: baggage_tag
        GlobalSecondaryIndexes:
          - IndexName: baggageIdByPnr
            KeySchema:
              - KeyType: HASH
                AttributeName: pnr_id
            Projection:
              ProjectionType: "ALL"
          # - IndexName: baggageIdByFlight
          #   KeySchema:
          #     - KeyType: HASH
          #       AttributeName: flight_id
          #   Projection:
          #     ProjectionType: "ALL"
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-baggage-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    TwilioLambdaLayer:
      Type: AWS::Lambda::LayerVersion
      Properties:
        CompatibleRuntimes:
          - python3.8
          - python3.9
          - python3.10
        Content:
          S3Bucket: ${self:custom.${self:provider.stage}.LayerBucket}
          S3Key: Twilio-python.zip
        LayerName: twilio-python-layer-${self:provider.stage}
  