const {ApiResp} = require("../helpers/api-helper")
const {putDynamodbTableItem, updateDynamoTableDataWithParams, queryDynamodbTableItemGSI} = require("../helpers/dynamo-helper")
const crypto = require("crypto");
async function createCase(body)
{
    console.log("body for createCase :- ", body)
    const item = {
        "caseId": crypto.randomUUID(),
        "caseTitle": body.caseTitle,
        "caseDescription": body.caseDescription,
        "customerId": body.customerId,
        "caseCategory": body.caseCategory,
        "caseStatus": "CREATED",
        "createdAt": new Date().toISOString()
        
    }

    const response = await putDynamodbTableItem(process.env.TABLE_NAME, item)
    console.log("response for createCase :- ", body)

    if(response)
    {
        return ApiResp({status: "Success", message: "case created successfully!"}, 200)
    }
    else
    {
        return ApiResp({status: "Error", message: "There was some error while creating the case!"}, 500)
    }
}

async function listCases(body)
{
    console.log("body for listCases :- ", body)
    let filterExpression = null
    let expressionAttributeValues = {}
    if(body)
    {
        if(Object.keys(body).length > 1)
        {
            filterExpression = formFilterExpression(body)
            expressionAttributeValues = formExpressionAttributeValues(body)
        }  
        else if(Object.keys(body).length == 1)
        {
            filterExpression = "caseStatus = :status1 OR caseStatus = :status2"
            expressionAttributeValues = {
                ":status1": "CREATED",
                ":status2": "IN PROGRESS",
                ":customerId": body.customerId
            }
        }     
    }
    const params = {
        TableName: process.env.TABLE_NAME,
        IndexName: "customerIdRetrieve",
        KeyConditionExpression: "customerId = :customerId",
        FilterExpression: filterExpression,
        ExpressionAttributeValues: expressionAttributeValues
    }
    const response = await queryDynamodbTableItemGSI(params)
    if(response)
    {
        return ApiResp({status: "Success", message: "cases retrieved successfully!", caseList: response}, 200)
    }
    else
    {
        return ApiResp({status: "Error", message: "There was an error while retrieving case list!"}, 500)
    }
}

async function updateCase(body)
{
    console.log("body for updateCase :- ", body)
    const updateExpression = "SET #status = :caseStatus, #timestamp = :timestamp"
    const expressionAttributeValues = {
        ":caseStatus": body.caseStatus,
        ":timestamp": new Date().toISOString()
    }
    let expressionAttributeNames = null

    if(body.caseStatus)
    {
        if(body.caseStatus == "IN PROGRESS")
        {
            expressionAttributeNames = {
                "#status": "caseStatus",
                "#timestamp": "inProgressTimeStamp"
            }
        }
        else if(body.caseStatus == "CLOSED")
        {
            expressionAttributeNames = {
                "#status": "caseStatus",
                "#timestamp": "closingTimeStamp"
            }
        }
        else
        {
            return ApiResp({status: "Error", message: "Invalid case status!"}, 500)
        }
    }

    const params = {
        TableName: process.env.TABLE_NAME,
        Key: {
            caseId : body.caseId
        },
        UpdateExpression : updateExpression,
        ExpressionAttributeNames : expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues
    }

    const response = await updateDynamoTableDataWithParams(params)
    console.log("RESPONSE :- ", response)
    if(response)
    {
        return ApiResp({status: "Success", message: "case updated successfully!"}, 200)
    }
    else
    {
        return ApiResp({status: "Error", message: "There was some error while updating the case!"}, 500)
    }
}

function formFilterExpression(filters)
{
    let filterExpression = ""
    let count = 0
    for(let filter in filters)
    {
        if(filter != "customerId")
        {
            filterExpression += `${filter} = :${filter}`
  
            if(count == Object.keys(filters).length - 1)
            {
                break
            }
            else
            {
                filterExpression += " AND "
                count += 1
            }
        }
        else
        {
            count += 1
        }
        
    }
    return filterExpression
}

function formExpressionAttributeValues(filters)
{
    let expressionAttributeValues = {}

    for(let filter in filters)
    {
        const key = ":" + filter
        expressionAttributeValues[key] = filters[filter]
    }

    return expressionAttributeValues
}

module.exports = {
    createCase,
    listCases,
    updateCase
}