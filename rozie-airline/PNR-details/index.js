/* eslint-disable no-case-declarations */
const { ApiResp } = require("./helpers/api-helper.js")
const { getPNR} = require("./pnr-helpers/get-pnr.js")
const { getCustomerPNR} = require("./pnr-helpers/get-customer-pnr.js")
const { deletePNR} = require("./pnr-helpers/delete-pnr.js")
const { createPNR} = require("./pnr-helpers/create-pnr.js")
const { updatePNR} = require("./pnr-helpers/update-pnr.js")


/**
 * AWS Lambda handler for managing PNR operations. 
 * This function processes incoming API requests to perform CRUD operations on PNR records.
 *
 * Supported endpoints:
 *  - /createPNR: Create a new PNR record.
 *  - /getCustomerPNR: Get all PNR records for a specific customer by customer_id.
 *  - /getPNR: Get a single PNR by pnr_id.
 *  - /updatePNR: Update an existing PNR.
 *  - /deletePNR: Delete a PNR by pnr_id.
 * 
 * @param {object} event - The event object representing the API request, including body and resource path.
 * @returns {object} - An API response object containing the result of the operation or an error message.
 */

const lambda_handler = async (event) => {
	console.log(JSON.stringify(event))
	let body = JSON.parse(event.body)
	const { pnr_id, customer_id , pnr_status_flag } = body
	const path = event.resource
    
	try {
		switch (path) {
		case "/createPNR":
			const createPNRResponse = await createPNR(body)
			return createPNRResponse
		case "/getCustomerPNR":
			if (!customer_id) {
				return ApiResp({
					status: "Error",
					message: "customer_id is required"
				}, 400)
			}
			const customerPNRs = await getCustomerPNR(customer_id , pnr_status_flag)
			return customerPNRs
		case "/getPNR":
			if (!pnr_id) {
				return ApiResp({
					status: "Error",
					message: "pnr_id is required"
				}, 400)
			}
			const pnr = await getPNR(pnr_id)
			return pnr
		case "/updatePNR":
			const updatePNRBody = body
			const updateResponse = await updatePNR(updatePNRBody)
			return updateResponse
		case "/deletePNR":
			if (!pnr_id) {
				return ApiResp({
					status: "Error",
					message: "pnr_id is required"
				}, 400)
			}			
			const deleteResponse = await deletePNR(pnr_id)
			return deleteResponse
		default:
			return ApiResp({
				status: "Error",
				message: "Invalid resource path"
			}, 400)
		}
	} catch (error) {
		console.log(error)
		return ApiResp({
			status: "Error",
			message: "Internal Server Error"
		}, 500)
	}
}

module.exports = {
	lambda_handler,
}
