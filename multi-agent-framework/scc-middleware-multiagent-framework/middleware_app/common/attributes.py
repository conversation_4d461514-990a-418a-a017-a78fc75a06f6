import os
import json
from middleware_app.helpers.dynamo_helper import get_item_by_primary_key, upsert_item

ECS_ATTRIBUTES = {}
LLM_PARAMETERS = {}
LLM_PROMPT_CONFIG = {}

def set_attributes():
    if os.getenv("FASTAPI_ENV")=="dev":
        config = get_item_by_primary_key(
            table= os.getenv("CONFIG_TABLE"),
            primary_key_id= "config_id",
            primary_key_value=os.getenv("BUSINESS_ID")
        )
    else:
        with open("middleware_app/data/business_config.json", "r") as f:
            config = json.load(f)

    for k, v in config.get("Attributes").items():
        ECS_ATTRIBUTES[k] = v

    for k, v in config.get("llm_parameters").items():
        LLM_PARAMETERS[k] = v

    for k, v in config.get("llm_prompts").items():
        LLM_PROMPT_CONFIG[k] = v

def set_business_config():
    """
    This function is to be executed at when server starts.
    It execute once pushing the business config to config table
    """
    with open("middleware_app/data/business_config.json", "r") as f:
        config = json.load(f)

    if os.getenv("FASTAPI_ENV")=="dev":
        upsert_item(os.getenv("CONFIG_TABLE"), config, "config_id")
