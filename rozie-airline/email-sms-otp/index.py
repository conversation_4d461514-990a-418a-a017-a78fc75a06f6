
from functions.send_otp import send_otp_handler
from functions.validate_otp import validate_otp_handler
from functions.send_email import send_email_handler
from functions.send_sms import send_sms_handler
from helpers.response_helper import format_response
from helpers.logging_helper import print_info_log, get_log_level, set_logging_level
import json
def lambda_handler(event, context):
    """
    Lambda handler that routes based on the HTTP path.
    """
    set_logging_level(get_log_level())
    print_info_log("abc", "EVENT", event)
    path = event["path"]
    body = json.loads(event["body"])
    try:
            if path == "/sendOtp":
                return send_otp_handler(body)

            elif path == "/validateOtp":
                return validate_otp_handler(body)
                
            elif path == "/sendEmail":
                return send_email_handler(body)

            elif path == "/sendSMS":
                return send_sms_handler(body)

            else:
                return format_response(404, {}, "Invalid Request")

    except Exception as e:
        # Catch any unexpected errors
        return format_response(500, {}, f"Error: {str(e)}")
