# Airline API Service

A FastAPI-based service that provides flight-related functionality using the Amadeus API with OpenAI fallback.

## Project Structure

```
airline_app/
├── api/
│   ├── routes/           # API route handlers
│   └── models/           # Pydantic models/schemas
├── core/                 # Core configuration
├── services/            # Business logic services
├── utils/               # Utility functions
├── tests/               # Test files
├── main.py             # Application entry point
└── requirements.txt    # Project dependencies
```

## Features

- Flight Status Information
- Flight Offers Search
- Flight Order Creation
- Amadeus API Integration
- OpenAI Fallback for Synthetic Data

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
AMADEUS_CLIENT_ID=your_client_id
AMADEUS_CLIENT_SECRET=your_client_secret
OPENAI_API_KEY=your_openai_api_key
AMADEUS_HOST=test  # or 'production'
```

3. Run the application:
```bash
python main.py
```

The API will be available at `http://localhost:8000`

## API Documentation

Once the server is running, you can access the API documentation at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Available Endpoints

- `GET /flight-status` - Get flight status information
- `GET /flight-offers-search` - Search for flight offers
- `POST /flight-order` - Create a flight order

## Development

The project follows a modular structure:
- Routes are separated by functionality
- Services contain business logic
- Models define data structures
- Utils provide shared functionality 