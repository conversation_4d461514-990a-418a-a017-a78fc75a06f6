import os
from middleware_app.helpers.request_helper import post_request


def chat_initiate_request(params):
    url = "http://agapi.dev-scc-demo.rozie.ai/" + "chat/v3/send_message"
    body = params
    headers = {
        "Content-Type": "application/json",
        "access_token": os.environ.get("MULTI_AGENT_ACCESS_TOKEN"),
    }

    status, result = post_request(url, headers, body=body)
    print(result, type(result))
    if status == "Success":
        return "Success", result
    return "Failed", result


def chat_send_message_request(chat_id, message):
    url = "http://llm-autogen.dev-scc-demo.rozie.ai/" + "chat/v3/send_message"
    body = {"chat_id": chat_id, "message": message}
    headers = {
        "Content-Type": "application/json",
        "access_token": os.environ.get("MULTI_AGENT_ACCESS_TOKEN"),
    }

    status, result = post_request(url, body, headers)
    if status == "Success":
        return "Success", {
            "chat_status": "Active",
            "chat_id": chat_id,
            "agent_response": result.get("follow_up"),
        }

    return "Failed", {
        "chat_status": "Terminated",
        "chat_id": chat_id,
        "error": result.get("error_message"),
    }
