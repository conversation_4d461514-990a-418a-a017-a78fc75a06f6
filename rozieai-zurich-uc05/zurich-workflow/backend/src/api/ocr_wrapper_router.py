"""
ZURICH OCR WRAPPER ROUTER
=========================

Purpose: FastAPI router for n8n-compatible Zurich OCR batch processing service
Integration: Accepts files and config from n8n, forwards to actual OCR API
Endpoints: POST /batch-process, GET /health, GET /info

Usage in n8n:
- HTTP Request node
- POST to {{base_url}}/ocr/batch-process
- Include files as binary data and config as JSON
- Response contains OCR results from all processed files
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import List, Optional, Dict, Any
import traceback
import tempfile
import os
from pathlib import Path

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Request, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
import httpx

# ================================================================================================
# LOGGING CONFIGURATION
# ================================================================================================

logger = logging.getLogger("ZurichOCRWrapper")

# ================================================================================================
# ROUTER INITIALIZATION 
# ================================================================================================

router = APIRouter(
    prefix="/ocr",
    tags=["ocr-processing"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)

# ================================================================================================
# CONFIGURATION MODELS
# ================================================================================================

class OCRConfig(BaseModel):
    """OCR Configuration model"""
    ocr_engine: str = Field(default="google", description="OCR engine to use")
    google_processor: str = Field(default="OCR_PROCESSOR", description="Google processor type")
    llm_routing_enabled: bool = Field(default=False, description="Enable LLM routing")
    post_processing: str = Field(default="v1", description="Post processing version")
    preprocessing: str = Field(default="none", description="Preprocessing options")
    parallel_processing: bool = Field(default=False, description="Enable parallel processing")
    
    @validator('ocr_engine')
    def validate_ocr_engine(cls, v):
        allowed_engines = ["google", "azure", "aws"]
        if v not in allowed_engines:
            raise ValueError(f"OCR engine must be one of {allowed_engines}")
        return v

class OCRResponse(BaseModel):
    """OCR Response model for n8n compatibility"""
    success: bool = Field(..., description="Whether the operation was successful")
    processed_files: int = Field(..., description="Number of files processed")
    results: List[Dict[str, Any]] = Field(..., description="OCR results for each file")
    processing_time: float = Field(..., description="Total processing time in seconds")
    config_used: Dict[str, Any] = Field(..., description="Configuration used for processing")
    error_message: Optional[str] = Field(None, description="Error message if any")

# ================================================================================================
# ZURICH OCR SERVICE CLIENT
# ================================================================================================

class ZurichOCRClient:
    """Client for interacting with the actual Zurich OCR service"""
    
    def __init__(self):
        self.base_url = "https://zurich-ocr.dev-scc-demo.rozie.ai"
        self.batch_endpoint = "/api/v1/batch-process"
        self.timeout = 300  # 5 minutes timeout for OCR processing
        
    async def process_files(self, files: List[UploadFile], config: OCRConfig) -> Dict[str, Any]:
        """
        Process files through the Zurich OCR service
        
        Args:
            files: List of uploaded files
            config: OCR configuration
            
        Returns:
            OCR processing results
        """
        start_time = datetime.now()
        
        try:
            # Prepare files for the actual OCR API
            files_data = []
            temp_files = []
            
            for file in files:
                # Create temporary file
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}")
                temp_files.append(temp_file.name)
                
                # Write uploaded file content to temp file
                content = await file.read()
                temp_file.write(content)
                temp_file.close()
                
                # Prepare for multipart upload
                files_data.append(
                    ("files", (file.filename, open(temp_file.name, "rb"), file.content_type))
                )
            
            # Prepare config data
            config_json = config.json()
            
            # Make request to actual OCR service
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}{self.batch_endpoint}",
                    files=files_data,
                    data={"config": config_json}
                )
                
                # Close temporary files
                for file_tuple in files_data:
                    file_tuple[1][1].close()
                
                # Clean up temporary files
                for temp_file_path in temp_files:
                    try:
                        os.unlink(temp_file_path)
                    except Exception as e:
                        logger.warning(f"Failed to delete temp file {temp_file_path}: {e}")
                
                # Check response
                if response.status_code == 200:
                    ocr_results = response.json()
                    processing_time = (datetime.now() - start_time).total_seconds()
                    
                    return {
                        "success": True,
                        "processed_files": len(files),
                        "results": ocr_results.get("results", []),
                        "processing_time": processing_time,
                        "config_used": config.dict(),
                        "raw_response": ocr_results  # Include full response for debugging
                    }
                else:
                    error_msg = f"OCR service returned status {response.status_code}: {response.text}"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "processed_files": 0,
                        "results": [],
                        "processing_time": (datetime.now() - start_time).total_seconds(),
                        "config_used": config.dict(),
                        "error_message": error_msg
                    }
                    
        except Exception as e:
            error_msg = f"Error processing files: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            
            return {
                "success": False,
                "processed_files": 0,
                "results": [],
                "processing_time": (datetime.now() - start_time).total_seconds(),
                "config_used": config.dict(),
                "error_message": error_msg
            }

# ================================================================================================
# API ENDPOINTS
# ================================================================================================

@router.post("/batch-process",
             response_model=OCRResponse,
             summary="Process files through Zurich OCR service",
             description="n8n-compatible endpoint for batch OCR processing")
async def process_files_batch(
    files: List[UploadFile] = File(..., description="Files to process through OCR"),
    # JSON config option (for complex workflows)
    config: Optional[str] = Form(None, description="OCR configuration as JSON string"),
    # Individual config parameters (for n8n HTTP Request node compatibility)
    ocr_engine: Optional[str] = Form("google", description="OCR engine: google, azure, aws"),
    google_processor: Optional[str] = Form("OCR_PROCESSOR", description="Google processor type"),
    llm_routing_enabled: Optional[bool] = Form(False, description="Enable LLM routing"),
    post_processing: Optional[str] = Form("v1", description="Post processing version"),
    preprocessing: Optional[str] = Form("none", description="Preprocessing options"), 
    parallel_processing: Optional[bool] = Form(False, description="Enable parallel processing")
):
    """
    Process multiple files through Zurich OCR service - FULLY n8n COMPATIBLE
    
    This endpoint supports TWO ways to provide configuration:
    
    1. JSON config string (for advanced users):
       --form 'config={"ocr_engine": "google", "google_processor": "OCR_PROCESSOR", ...}'
    
    2. Individual form parameters (for n8n HTTP Request nodes):
       --form 'ocr_engine=google' --form 'google_processor=OCR_PROCESSOR' ...
    
    Args:
        files: List of files to process (PDF, images, documents) 
        config: Optional JSON string with all OCR configuration
        ocr_engine: OCR engine to use (google/azure/aws)
        google_processor: Google processor type
        llm_routing_enabled: Enable LLM routing
        post_processing: Post processing version
        preprocessing: Preprocessing options
        parallel_processing: Enable parallel processing
        
    Returns:
        OCRResponse with processing results optimized for n8n workflows
    """
    
    processing_start = datetime.now()
    
    try:
        logger.info(f"🔍 Starting n8n-compatible OCR batch processing for {len(files)} files")
        
        # Validate files
        if not files:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No files provided for processing"
            )
        
        # Handle configuration - Priority: JSON config > individual parameters
        try:
            if config:
                # Use JSON config if provided
                config_dict = json.loads(config)
                logger.info("Using JSON configuration")
            else:
                # Use individual parameters (n8n HTTP Request node style)
                config_dict = {
                    "ocr_engine": ocr_engine,
                    "google_processor": google_processor,
                    "llm_routing_enabled": llm_routing_enabled,
                    "post_processing": post_processing,
                    "preprocessing": preprocessing,
                    "parallel_processing": parallel_processing
                }
                logger.info("Using individual form parameters")
            
            ocr_config = OCRConfig(**config_dict)
            
        except json.JSONDecodeError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid JSON configuration: {str(e)}"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid configuration: {str(e)}"
            )
        
        # Log file details for debugging
        for i, file in enumerate(files):
            logger.info(f"File {i+1}: {file.filename} ({file.content_type}, {file.size} bytes)")
        
        # Log configuration used
        logger.info(f"OCR Config: {ocr_config.dict()}")
        
        # Process files through OCR service
        ocr_client = ZurichOCRClient()
        results = await ocr_client.process_files(files, ocr_config)
        
        processing_time = (datetime.now() - processing_start).total_seconds()
        
        # Update processing time in results
        results["processing_time"] = processing_time
        
        # Enhanced logging for n8n debugging
        if results["success"]:
            logger.info(f"✅ n8n OCR processing completed successfully in {processing_time:.2f}s")
            logger.info(f"Processed {results['processed_files']} files")
            
            # Add n8n-friendly metadata to results
            for i, result in enumerate(results.get("results", [])):
                if isinstance(result, dict):
                    result["file_index"] = i
                    result["processing_timestamp"] = datetime.now().isoformat()
                    
        else:
            logger.error(f"❌ n8n OCR processing failed: {results.get('error_message', 'Unknown error')}")
        
        return OCRResponse(**results)
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"n8n OCR wrapper processing failed: {str(e)}"
        logger.error(f"❌ {error_msg}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        processing_time = (datetime.now() - processing_start).total_seconds()
        
        return OCRResponse(
            success=False,
            processed_files=0,
            results=[],
            processing_time=processing_time,
            config_used={},
            error_message=error_msg
        )

@router.get("/health",
            summary="Health check",
            description="Check if the OCR wrapper service is healthy")
async def health_check():
    """Health check for OCR wrapper service"""
    try:
        # Basic health check - could be expanded to test actual OCR service connectivity
        health_data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "wrapper_service": "running",
            "ocr_service_connection": "unavailable",  # Would need actual test
            "version": "1.0.0"
        }
        
        logger.info("🩺 OCR wrapper health check: healthy")
        return health_data
        
    except Exception as e:
        logger.error(f"❌ OCR wrapper health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "service": "ocr-wrapper"
            }
        )

@router.get("/info",
            summary="API information",
            description="Get API information and configuration options")
async def api_info():
    """Get comprehensive OCR API information"""
    return {
        "service": "Zurich OCR Wrapper API",
        "version": "1.0.0",
        "description": "n8n-compatible wrapper for Zurich OCR batch processing service",
        "endpoints": {
            "/ocr/batch-process": {
                "method": "POST",
                "description": "Process multiple files through OCR service - FULLY n8n COMPATIBLE",
                "content_type": "multipart/form-data",
                "parameters": {
                    "files": "List of files to process (required)",
                    "config": "JSON configuration string (optional - for advanced users)",
                    "ocr_engine": "OCR engine: google, azure, aws (optional, default: google)",
                    "google_processor": "Google processor type (optional, default: OCR_PROCESSOR)",
                    "llm_routing_enabled": "Enable LLM routing (optional, default: false)",
                    "post_processing": "Post processing version (optional, default: v1)",
                    "preprocessing": "Preprocessing options (optional, default: none)",
                    "parallel_processing": "Enable parallel processing (optional, default: false)"
                },
                "n8n_compatibility": {
                    "supports_individual_parameters": true,
                    "supports_json_config": true,
                    "response_format": "n8n-optimized with metadata"
                }
            },
            "/ocr/health": {
                "method": "GET",
                "description": "Health check endpoint"
            },
            "/ocr/info": {
                "method": "GET",
                "description": "API information and capabilities"
            }
        },
        "supported_file_types": [
            "application/pdf",
            "image/jpeg",
            "image/png", 
            "image/tiff",
            "image/webp",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        ],
        "ocr_engines": {
            "google": {
                "description": "Google Cloud Document AI",
                "processors": ["OCR_PROCESSOR", "FORM_PARSER", "DOCUMENT_SPLITTER"]
            },
            "azure": {
                "description": "Azure Computer Vision OCR",
                "features": ["read", "layout", "prebuilt-receipt", "prebuilt-invoice"]
            },
            "aws": {
                "description": "Amazon Textract",
                "features": ["detectDocumentText", "analyzeDocument", "analyzeExpense"]
            }
        },
        "configuration_options": {
            "ocr_engine": {
                "type": "string",
                "options": ["google", "azure", "aws"],
                "default": "google"
            },
            "google_processor": {
                "type": "string",
                "options": ["OCR_PROCESSOR", "FORM_PARSER", "DOCUMENT_SPLITTER"],
                "default": "OCR_PROCESSOR"
            },
            "llm_routing_enabled": {
                "type": "boolean",
                "default": False,
                "description": "Enable intelligent routing based on document type"
            },
            "post_processing": {
                "type": "string",
                "options": ["none", "v1", "v2"],
                "default": "v1"
            },
            "preprocessing": {
                "type": "string", 
                "options": ["none", "deskew", "noise_removal", "contrast_enhancement"],
                "default": "none"
            },
            "parallel_processing": {
                "type": "boolean",
                "default": False,
                "description": "Process multiple files concurrently"
            }
        },
        "usage_examples": {
            "curl": 'curl -X POST "http://localhost:8000/ocr/batch-process" -F \'files=@"document.pdf"\' -F \'config={"ocr_engine": "google"}\'',
            "n8n_config": {
                "url": "{{base_url}}/ocr/batch-process",
                "method": "POST", 
                "content_type": "multipart-form-data",
                "body_parameters": {
                    "config": '{"ocr_engine": "google", "post_processing": "v1"}'
                },
                "file_upload": "Use Binary File input type"
            }
        },
        "rate_limits": {
            "max_files_per_request": 10,
            "max_file_size": "50MB",
            "timeout": "300 seconds"
        },
        "timestamp": datetime.now().isoformat()
    } 