// ================================================================================================
// ZURICH LEVEL 01 ANALYSIS - BAML IMPLEMENTATION
// ================================================================================================
// Purpose: Comprehensive claim analysis with intelligent exit path routing
// Integration: Follows classification -> Level 01 -> Level 02 workflow
// Enhancement: Includes Spark NLP preprocessing capabilities for maximum accuracy
// ================================================================================================

// ================================================================================================
// CORE ANALYSIS ENUMS
// ================================================================================================

enum ClaimTypeDetailed {
  AUTO @description("Automotive insurance claims")
  PROPERTY @description("Property damage claims")
  LIABILITY @description("General liability claims") 
  WORKERS_COMPENSATION @description("Workers compensation claims")
  MEDICAL_MALPRACTICE @description("Medical malpractice claims")
  PROFESSIONAL_LIABILITY @description("Professional liability claims")
  PRODUCT_LIABILITY @description("Product liability claims")
  CYBER_LIABILITY @description("Cyber security and data breach claims")
  ENVIRONMENTAL @description("Environmental damage claims")
  GENERAL @description("General insurance claims")
  UNKNOWN @description("Cannot determine claim type from available information")
}

enum IncidentType {
  COLLISION @description("Vehicle collision or impact")
  WEATHER @description("Weather-related damage")
  THEFT @description("Theft or burglary")
  FIRE @description("Fire damage")
  VANDALISM @description("Vandalism or malicious damage")
  MEDICAL_EVENT @description("Medical incident or malpractice")
  SLIP_FALL @description("Slip and fall incident")
  WORKPLACE_INJURY @description("Workplace injury or accident")
  WATER_DAMAGE @description("Water damage or flooding")
  CYBER_ATTACK @description("Cyber security incident")
  OTHER @description("Other type of incident")
}

enum ContactRole {
  CLAIMANT @description("Person making the claim")
  INSURED @description("Insured party on the policy")
  OTHER_DRIVER @description("Other driver in vehicle incident")
  WITNESS @description("Witness to the incident")
  PASSENGER @description("Passenger in vehicle")
  ADJUSTER @description("Insurance adjuster")
  LAWYER @description("Legal representative")
  POLICE_OFFICER @description("Police officer")
  MEDICAL_PROFESSIONAL @description("Doctor, nurse, or medical professional")
  PROPERTY_OWNER @description("Owner of damaged property")
  CONTRACTOR @description("Repair contractor or service provider")
  EXPERT_WITNESS @description("Expert witness or investigator")
}

enum ExitPath {
  DOCUMENTS_INSUFFICIENT @description("Missing critical documents, cannot proceed")
  POLICY_LOOKUP_NEEDED @description("Policy number found, need to lookup complete policy details")
  PROCEED_TO_LEVEL02 @description("Sufficient information to proceed to coverage analysis")
}

enum PriorityLevel {
  URGENT @description("Requires immediate attention")
  HIGH @description("High priority processing")
  NORMAL @description("Standard priority processing")
  LOW @description("Low priority processing")
}

// ================================================================================================
// CLAIM DETAILS SCHEMAS
// ================================================================================================

class ClaimDetails {
  incidentDate string? @description("Date of incident if found (YYYY-MM-DD format)")
  incidentTime string? @description("Time of incident if available (HH:MM format)")
  incidentLocation string? @description("Location where incident occurred")
  claimantName string? @description("Person making the claim")
  insuredParty string? @description("Insured person/entity on the policy")
  claimType ClaimTypeDetailed @description("Type of claim identified")
  damageDescription string? @description("Description of damages/losses")
  estimatedAmount string? @description("Estimated claim amount if mentioned")
  policeReportNumber string? @description("Police report number if available")
  emergencyServicesInvolved bool @description("Were emergency services called")
  injuriesReported bool @description("Were any injuries reported")
  propertyDamage bool @description("Is there property damage")
  witnessesPresent bool @description("Were witnesses present")
  vehicleInvolved string? @description("Vehicle details if auto claim")
  thirdPartyInvolved bool @description("Is there a third party involved")
  medicalInformation MedicalSummary? @description("Comprehensive medical information if injuries reported")
}

class PolicyDetails {
  policyNumber string? @description("Insurance policy number (critical field)")
  policyHolder string? @description("Name of policy holder")
  insuredVehicle string? @description("Vehicle details if auto claim")
  effectiveDate string? @description("Policy effective date")
  expiryDate string? @description("Policy expiry date")
  coverageTypes string[] @description("Types of coverage mentioned")
  deductibleAmount string? @description("Deductible amount if mentioned")
  policyLimits string? @description("Policy limits if available")
  insuranceCompany string? @description("Insurance company name")
  agentDetails string? @description("Agent contact information")
  hasCompleteInformation bool @description("Whether we have complete policy info for processing")
}

class CauseOfLoss {
  primaryCause string @description("Main cause of the loss/incident")
  contributingFactors string[] @description("Additional factors that contributed")
  incidentType IncidentType @description("Type of incident that occurred")
  atFaultParties string[] @description("Parties potentially at fault")
  circumstances string @description("Detailed circumstances of the incident")
  weatherConditions string? @description("Weather conditions at time of incident")
  roadConditions string? @description("Road conditions if applicable")
  trafficViolations string[] @description("Any traffic violations mentioned")
  negligenceFactors string[] @description("Potential negligence factors identified")
  causationChain string[] @description("Sequence of events leading to the incident")
}

class ContactInfo {
  name string @description("Person's full name")
  role ContactRole @description("Role of this person in the claim")
  phoneNumber string? @description("Phone number")
  email string? @description("Email address") 
  address string? @description("Physical address")
  driverLicense string? @description("Driver's license number if applicable")
  relationship string? @description("Relationship to the incident")
  insuranceInfo string? @description("Their insurance information if available")
  employer string? @description("Employer if relevant to claim")
  notes string? @description("Additional notes about this contact")
}

// ================================================================================================
// SPARK NLP ENHANCEMENT SCHEMAS
// ================================================================================================

class SparkNlpEntity {
  text string @description("The extracted entity text")
  entityType string @description("Type of entity (PERSON, ORG, DATE, MONEY, etc.)")
  confidence float @description("Confidence score for this entity")
  context string @description("Surrounding context where entity was found")
  startPosition int? @description("Character position where entity starts")
  endPosition int? @description("Character position where entity ends")
}

class SparkNlpFinancialEntity {
  text string @description("The financial entity text")
  entityType "MONETARY_AMOUNT" | "POLICY_NUMBER" | "CLAIM_NUMBER" | "PERCENTAGE" | "DEDUCTIBLE" | "COVERAGE_LIMIT"
  normalizedValue string? @description("Normalized value if applicable")
  confidence float @description("Confidence score for this entity")
  context string @description("Context where financial entity was found")
}

class SparkNlpDateEntity {
  text string @description("The date text as found")
  normalizedDate string @description("Normalized date in YYYY-MM-DD format")
  dateType "INCIDENT_DATE" | "POLICY_DATE" | "REPORT_DATE" | "EFFECTIVE_DATE" | "EXPIRY_DATE" | "OTHER"
  confidence float @description("Confidence score for this date entity")
  context string @description("Context where date was found")
}

class SparkNlpLocationEntity {
  text string @description("The location text as found")
  addressType "INCIDENT_LOCATION" | "MAILING_ADDRESS" | "BUSINESS_ADDRESS" | "PROPERTY_ADDRESS" | "OTHER"
  normalizedAddress string? @description("Normalized address if possible")
  confidence float @description("Confidence score for this location")
  context string @description("Context where location was found")
}

class SparkNlpEnhancedData {
  enhancedEntities SparkNlpEntity[] @description("All entities found via Spark NLP")
  financialEntities SparkNlpFinancialEntity[] @description("Financial/insurance specific entities")
  extractedDates SparkNlpDateEntity[] @description("All dates found with context")
  locationEntities SparkNlpLocationEntity[] @description("Addresses and locations found")
  confidenceBoost float @description("How much Spark NLP improved extraction confidence (0.0-1.0)")
  processingNotes string[] @description("Notes about Spark NLP processing")
}

// ================================================================================================
// EXIT PATH ANALYSIS SCHEMAS
// ================================================================================================

class ConfidenceBreakdown {
  claimDetailsConfidence float @description("Confidence in claim details extraction (0.0-1.0)")
  policyDetailsConfidence float @description("Confidence in policy details extraction (0.0-1.0)")
  causeOfLossConfidence float @description("Confidence in cause of loss analysis (0.0-1.0)")
  contactDetailsConfidence float @description("Confidence in contact details extraction (0.0-1.0)")
  overallConfidence float @description("Overall analysis confidence (0.0-1.0)")
}

class NextSteps {
  documentsNeeded string[] @description("Specific documents required to proceed")
  informationNeeded string[] @description("Specific information to request from claimant")
  contactsToReach string[] @description("People who need to be contacted")
  timelineForResponse string @description("How quickly response is needed")
  escalationRequired bool @description("Whether immediate escalation is needed")
  automatedActions string[] @description("Actions that can be automated")
  manualActions string[] @description("Actions requiring human intervention")
}

class AnalysisDetails {
  extractedInformation string @description("Summary of all information extracted")
  confidenceBySection ConfidenceBreakdown @description("Confidence breakdown by analysis section")
  identifiedGaps string[] @description("Specific information gaps identified")
  assumptionsMade string[] @description("Any reasonable assumptions made in analysis")
  qualityAssessment string @description("Assessment of data quality and completeness")
  riskIndicators string[] @description("Potential risk factors identified")
}

class ExitAnalysis {
  exitReason string @description("Detailed reason for choosing this exit path")
  analysisProvided AnalysisDetails @description("All analysis completed so far")
  nextStepsRequired NextSteps @description("What needs to happen next")
  estimatedTimeToResolution string @description("Estimated time to resolve missing information")
  priorityLevel PriorityLevel @description("Priority level for this case")
  automationOpportunities string[] @description("Opportunities for automated processing")
  humanReviewItems string[] @description("Items requiring human review")
}

// ================================================================================================
// MAIN LEVEL 01 ANALYSIS RESULT SCHEMA
// ================================================================================================

class Level01Analysis {
  // Core Extractions
  claimDetails ClaimDetails @description("Comprehensive claim information")
  policyDetails PolicyDetails @description("Policy information extracted")
  causeOfLoss CauseOfLoss @description("Analysis of what caused the loss")
  contactDetails ContactInfo[] @description("All relevant contact information")
  
  // Decision Logic Results
  documentsSufficient bool @description("Are submitted documents sufficient for processing")
  policyNumberAvailable bool @description("Is policy number identified in documents")
  policyDetailsComplete bool @description("Are complete policy details available")
  
  // Intelligent Exit Path
  exitPath ExitPath @description("Determined next step in workflow")
  exitAnalysis ExitAnalysis @description("Detailed analysis for chosen exit path")
  
  // Enhanced Analysis (Spark NLP Integration)
  sparkNlpInsights SparkNlpEnhancedData? @description("Enhanced insights from Spark NLP preprocessing")
  
  // Quality Metrics
  confidenceScore float @description("Overall analysis confidence (0.0-1.0)")
  processingNotes string[] @description("Technical notes about the analysis process")
  dataQualityScore float @description("Quality of input data (0.0-1.0)")
  
  // Canadian Legal Context
  canadianJurisdiction string? @description("Relevant Canadian province/territory")
  legalConsiderations string[] @description("Canadian legal factors identified")
  regulatoryNotes string[] @description("Regulatory considerations")
  
  // UI METADATA AND EXPLAINABILITY (NEW)
  uiMetadata UIMetadata @description("UI metadata for frontend entity mapping and highlighting")
  explainabilityInsights ExplainabilityInsight[] @description("LIME/SHAP explainability analysis")
  
  // Processing Metadata
  analysisTimestamp string @description("When analysis was performed")
  modelVersion string @description("Version of analysis model used")
  processingTimeMs int @description("Time taken for analysis in milliseconds")
}

// ================================================================================================
// INPUT SCHEMAS
// ================================================================================================

class ClaimDocumentInput {
  claimId string @description("Unique claim identifier")
  emailContent string @description("Email body content")
  emailSubject string @description("Email subject line")
  attachmentNames string[] @description("Names of all attachments")
  attachmentsText string[] @description("Extracted text content from all attachments")
  preprocessingNotes string[] @description("Notes from document preprocessing")
}

// ================================================================================================
// MAIN LEVEL 01 ANALYSIS FUNCTION
// ================================================================================================

function AnalyzeClaimLevel01(
  claimInput: ClaimDocumentInput
) -> Level01Analysis {
  client "openai/gpt-4o"
  prompt #"
    You are Zurich Insurance's expert Level 01 claims analyst for Canadian insurance processing.
    
    MISSION: Perform comprehensive claim analysis and determine the appropriate next workflow step.
    
    CLAIM DATA TO ANALYZE:
    Claim ID: {{ claimInput.claimId }}
    Email Subject: {{ claimInput.emailSubject }}
    Email Content: {{ claimInput.emailContent }}
    
    ATTACHMENTS ANALYZED:
    {% for i in range(claimInput.attachmentNames|length) %}
    File {{ i+1 }}: {{ claimInput.attachmentNames[i] }}
    Content: {{ claimInput.attachmentsText[i] }}
    
    {% endfor %}
    
    COMPREHENSIVE ANALYSIS REQUIREMENTS:
    
    1. CLAIM DETAILS EXTRACTION:
       - Incident date, time, and location (be specific)
       - Claimant and insured party identification
       - Claim type classification (auto, property, liability, etc.)
       - Damage description and estimated amounts
       - Police report numbers and emergency services involvement
       - Injury reports and witness information
       - Vehicle details for auto claims
       - Third-party involvement assessment
       
    1a. MEDICAL INFORMATION EXTRACTION (if injuries reported):
       - Specific injuries and body parts affected
       - Injury severity and type (fracture, soft tissue, etc.)
       - Treatment history (emergency care, physiotherapy, chiropractic, etc.)
       - Diagnostic results (X-rays, MRI, ultrasound findings)
       - Medical timeline (first treatment, duration, return to work)
       - Healthcare providers and facilities involved
       - Medical costs and future care needs
       - Impact on daily life and work capacity
       - Available and needed medical documentation
    
    2. POLICY DETAILS EXTRACTION (CRITICAL):
       - Policy number identification (look for patterns: POL123456, P-123456, *********, etc.)
       - Policy holder name verification
       - Coverage types and limits mentioned
       - Deductible amounts
       - Policy effective and expiry dates
       - Insurance company and agent details
       - Assess completeness of policy information
    
    3. CAUSE OF LOSS ANALYSIS:
       - Primary cause identification
       - Contributing factors and circumstances
       - At-fault party determination
       - Weather and road conditions
       - Traffic violations or negligence factors
       - Sequence of events (causation chain)
    
    4. CONTACT DETAILS EXTRACTION:
       - All parties involved with complete contact information
       - Role classification (claimant, insured, other driver, witness, etc.)
       - Driver's license numbers
       - Insurance information for other parties
       - Professional contacts (lawyers, adjusters, medical professionals)
       - Relationships to the incident
    
    CRITICAL DECISION LOGIC - CHOOSE APPROPRIATE EXIT PATH:
    
    EXIT PATH 1 - "DOCUMENTS_INSUFFICIENT":
    Choose this if:
    - Critical documents are missing (police report, medical records, repair estimates)
    - Information is too vague or incomplete to make coverage decisions
    - No policy information is available
    - Key details like incident date/location are unclear
    
    EXIT PATH 2 - "POLICY_LOOKUP_NEEDED":
    Choose this if:
    - Policy number is clearly identified
    - Basic claim information is available
    - But complete policy details (coverage types, limits, deductibles) are missing
    - Need to lookup policy in system for coverage analysis
    
    EXIT PATH 3 - "PROCEED_TO_LEVEL02":
    Choose this if:
    - Sufficient documents AND complete policy information are available
    - Claim details are comprehensive enough for coverage analysis
    - Ready for liability assessment and coverage determination
    
    EXIT ANALYSIS REQUIREMENTS:
    For whichever exit path you choose:
    - Provide detailed reasoning for the decision
    - List specific next steps required
    - Identify automation opportunities
    - Flag items requiring human review
    - Estimate timeline for resolution
    - Set appropriate priority level
    
    CANADIAN INSURANCE CONTEXT:
    - Apply Canadian insurance terminology and regulations
    - Consider provincial differences (especially Quebec civil law)
    - Include relevant legal considerations
    - Factor in comparative negligence rules
    - Consider no-fault insurance implications where applicable
    
    CONFIDENCE SCORING:
    Rate confidence for each section (0.0-1.0):
    - How certain are you about claim details?
    - How confident about policy information?
    - How clear is the cause of loss?
    - How complete are contact details?
    - What's the overall data quality?
    
    QUALITY ASSESSMENT:
    Evaluate:
    - Completeness of information provided
    - Quality and clarity of documentation
    - Consistency across different sources
    - Potential contradictions or red flags
    - Missing critical information
    
    UI METADATA GENERATION (CRITICAL FOR EXPLAINABLE CLAIM ANALYSIS):
    - Generate precise entity mappings for every extracted field
    - Create document highlights with exact coordinates for key information
    - Map each entity to its source document and position
    - Assign appropriate colors based on entity type and confidence
    - Include confidence breakdown for each analysis element
    - Create visual analysis flow for claim processing decisions
    
    EXPLAINABILITY ANALYSIS (LIME/SHAP FOR CLAIM ANALYSIS):
    - Generate SHAP-style feature importance for decision factors
    - Identify top factors supporting/opposing each analysis decision
    - Provide confidence contributors for analysis results
    - Generate alternative scenarios with probability changes
    - Create visualization data for interactive claim exploration
    - Explain methodology used for claim analysis approach
    
    UI METADATA REQUIREMENTS:
    - Every extracted field MUST have source document mapping
    - Highlight coordinates MUST be provided for key evidence
    - Color coding MUST reflect entity type and confidence levels
    - Analysis flow MUST show logical progression of claim processing
    - Confidence scores MUST be provided for all determinations
    
    EXPLAINABILITY REQUIREMENTS:
    - Feature importance MUST explain analysis reasoning
    - Alternative scenarios MUST show sensitivity of decisions
    - Confidence contributors MUST be clearly documented
    - Visualization data MUST enable interactive claim exploration
    - Methodology notes MUST explain analysis approach
    
    Provide comprehensive analysis regardless of which exit path is chosen.
    Be thorough but decisive in your routing decision.
    
    {{ ctx.output_format }}
  "#
}

// ================================================================================================
// SPARK NLP PREPROCESSING FUNCTION (FOR FUTURE INTEGRATION)
// ================================================================================================

function EnhanceWithSparkNLP(
  emailContent: string,
  attachmentsText: string[]
) -> SparkNlpEnhancedData {
  client "openai/gpt-4o-mini"
  prompt #"
    You are simulating Spark NLP entity extraction for insurance claim processing.
    This is a placeholder function until actual Spark NLP integration is implemented.
    
    TEXT TO ANALYZE:
    Email: {{ emailContent }}
    Attachments: {{ attachmentsText }}
    
    Extract and categorize entities as Spark NLP would:
    - People, organizations, locations
    - Financial amounts and policy numbers  
    - Dates and times
    - Addresses and locations
    
    Focus on insurance-relevant entities and provide confidence scores.
    
    {{ ctx.output_format }}
  "#
}

// ================================================================================================
// UI METADATA AND EXPLAINABILITY SCHEMAS (IMPORTED FROM ui_metadata_schemas.baml)
// ================================================================================================

// ================================================================================================
// MEDICAL INFORMATION SCHEMAS (NEW)
// ================================================================================================

enum InjurySeverity {
  MINOR @description("Minor injuries requiring minimal treatment")
  MODERATE @description("Moderate injuries requiring ongoing treatment")
  SEVERE @description("Severe injuries with long-term impact")
  CATASTROPHIC @description("Life-altering or permanent disabilities")
}

enum TreatmentType {
  EMERGENCY_CARE @description("Emergency department or urgent care")
  PHYSIOTHERAPY @description("Physical therapy treatment")
  CHIROPRACTIC @description("Chiropractic treatment")
  MASSAGE_THERAPY @description("Massage therapy")
  SURGICAL @description("Surgical intervention")
  MEDICATION @description("Prescription medication")
  DIAGNOSTIC_IMAGING @description("X-rays, MRI, CT scans, ultrasound")
  PSYCHOLOGICAL @description("Mental health or psychological treatment")
  OCCUPATIONAL_THERAPY @description("Occupational therapy")
  OTHER_MEDICAL @description("Other medical treatment")
}

class InjuryDetail {
  bodyPart string @description("Specific body part injured (e.g., right shoulder, lower back)")
  injuryType string @description("Type of injury (e.g., fracture, soft tissue, tear, strain)")
  severity InjurySeverity @description("Severity level of the injury")
  description string @description("Detailed description of the injury")
  isPermanent bool @description("Whether injury has permanent implications")
  causationClear bool @description("Whether injury is clearly caused by incident")
  priorHistory bool @description("Whether there's evidence of prior injury to same area")
}

class TreatmentRecord {
  treatmentType TreatmentType @description("Type of treatment provided")
  provider string @description("Healthcare provider or facility")
  treatmentDate string @description("Date of treatment (if available)")
  treatmentDetails string @description("Specific details of treatment provided")
  treatmentDuration string @description("Duration or frequency of treatment")
  treatmentCost string @description("Cost of treatment if mentioned")
  ongoing bool @description("Whether treatment is ongoing")
}

class DiagnosticResult {
  diagnosticType string @description("Type of diagnostic test (X-ray, MRI, etc.)")
  findings string @description("Key findings from diagnostic test")
  datePerformed string @description("Date diagnostic was performed")
  facility string @description("Where diagnostic was performed")
  abnormalFindings bool @description("Whether abnormal findings were detected")
  supportsCausation bool @description("Whether findings support incident causation")
}

class MedicalTimeline {
  incidentDate string @description("Date of incident")
  firstTreatmentDate string @description("Date of first medical treatment")
  emergencyTreatment bool @description("Whether emergency treatment was required")
  timeOffWork string @description("Duration of time off work due to injuries")
  returnToWorkDate string @description("Date returned to work (if applicable)")
  treatmentEndDate string @description("Expected or actual end of treatment")
  functionalLimitations string[] @description("Ongoing functional limitations")
}

class MedicalSummary {
  injuriesReported bool @description("Were any injuries reported")
  injuryDetails InjuryDetail[] @description("Detailed injury information")
  treatmentRecords TreatmentRecord[] @description("Treatment history and records")
  diagnosticResults DiagnosticResult[] @description("Diagnostic test results")
  medicalTimeline MedicalTimeline @description("Timeline of medical events")
  medicalProfessionals ContactInfo[] @description("Medical professionals involved")
  medicalCosts string @description("Total or estimated medical costs")
  futureCareneeds string @description("Future care requirements")
  impactOnDailyLife string @description("Impact on claimant's daily activities")
  returnToWorkPrognosis string @description("Prognosis for return to work")
  medicalDocumentsAvailable string[] @description("Available medical documents")
  medicalDocumentsNeeded string[] @description("Medical documents still needed")
}
