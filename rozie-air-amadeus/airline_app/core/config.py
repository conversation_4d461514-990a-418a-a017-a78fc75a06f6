import os
from dotenv import load_dotenv
from pydantic_settings import BaseSettings

# Load environment variables from .env file
load_dotenv()

class Settings(BaseSettings):
    """Application settings."""
    # Amadeus API credentials
    AMADEUS_CLIENT_ID: str = os.environ.get("AMADEUS_CLIENT_ID", "")
    AMADEUS_CLIENT_SECRET: str = os.environ.get("AMADEUS_CLIENT_SECRET", "")
    AMADEUS_HOST: str = os.environ.get("AMADEUS_HOST", "test")  # "test" or "production"

    # OpenAI configuration
    OPENAI_API_KEY: str = os.environ.get("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = os.environ.get("OPENAI_MODEL", "gpt-3.5-turbo")

    # API settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Airline API"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "API for flight status, offers, and orders"

    # Server settings
    HOST: str = os.environ.get("HOST", "0.0.0.0")
    PORT: int = int(os.environ.get("PORT", "8000"))
    DEBUG: bool = os.environ.get("DEBUG", "False").lower() == "true"

    # Logging
    LOG_LEVEL: str = os.environ.get("LOG_LEVEL", "INFO")

    class Config:
        case_sensitive = True

# Create settings instance
settings = Settings()

# For backward compatibility
AMADEUS_CLIENT_ID = settings.AMADEUS_CLIENT_ID
AMADEUS_CLIENT_SECRET = settings.AMADEUS_CLIENT_SECRET
AMADEUS_HOST = settings.AMADEUS_HOST
OPENAI_API_KEY = settings.OPENAI_API_KEY
OPENAI_MODEL = settings.OPENAI_MODEL 