import traceback
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from airline_app.adapters.auth_adapter import get_api_key

from airline_app.services.email_notification import send_email_boarding_pass, send_email_prohibited_item_doc_link, send_email_ticket

email_notification_router = APIRouter(prefix="/notification/email")

@email_notification_router.post(
    "/send-boarding-pass",
    dependencies=[Depends(get_api_key)]
)
async def send_boarding_pass(pnr_id: str, customer_id: str) -> dict:
    try:
        email_status = send_email_boarding_pass(pnr_id=pnr_id, customer_id=customer_id)
        if not email_status:
            raise HTTPException(status_code=404, detail="Customer ID not found")
        return email_status
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )

@email_notification_router.post(
    "/send-prohibited-item-doc-link",
    dependencies=[Depends(get_api_key)]
)
async def send_prohibited_item_doc_link(customer_id: str) -> dict:
    try:
        email_status = send_email_prohibited_item_doc_link(customer_id=customer_id)
        if not email_status:
            raise HTTPException(status_code=404, detail="Customer ID not found")
        return email_status
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )

@email_notification_router.post(
    "/send-ticket",
    dependencies=[Depends(get_api_key)]
)
async def send_ticket(pnr_id: str) -> dict:
    try:
        email_status = send_email_ticket(pnr_id=pnr_id)
        if not email_status:
            raise HTTPException(status_code=404, detail="Invalid PNR")
        return email_status
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )
