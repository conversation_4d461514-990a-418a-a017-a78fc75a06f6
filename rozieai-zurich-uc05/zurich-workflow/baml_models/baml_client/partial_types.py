###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import baml_py
from enum import Enum

from pydantic import BaseModel, ConfigDict

from typing_extensions import TypeAlias, Literal
from typing import Dict, Generic, List, Optional, TypeVar, Union

from . import types
from .types import Checked, Check

###############################################################################
#
#  These types are used for streaming, for when an instance of a type
#  is still being built up and any of its fields is not yet fully available.
#
###############################################################################

T = TypeVar('T')
class StreamState(BaseModel, Generic[T]):
    value: T
    state: Literal["Pending", "Incomplete", "Complete"]


class AccidentClassification(BaseModel):
    primaryType: Optional[Union[Literal["auto_collision"], Literal["slip_fall"], Literal["occupiers_liability"], Literal["general_liability"], Literal["product_liability"]]] = None
    specificSubtype: Optional[str] = None
    incidentComplexity: Optional[Union[Literal["simple"], Literal["moderate"], Literal["complex"], Literal["multi_party"]]] = None
    applicableLaw: Optional[str] = None
    certaintyLevel: Optional[float] = None

class AgentObtainableInformation(BaseModel):
    informationType: Optional[str] = None
    sourceOfInformation: Optional[str] = None
    procedureRequired: Optional[str] = None
    timelineToObtain: Optional[str] = None
    costToCompany: Optional[str] = None
    reliabilityLevel: Optional[float] = None

class AlternativeScenario(BaseModel):
    scenario: Optional[str] = None
    probabilityChange: Optional[float] = None
    requiredChanges: List[str]
    likelihood: Optional[Union[Literal["HIGH"], Literal["MEDIUM"], Literal["LOW"]]] = None
    implications: List[str]

class AnalysisColorScheme(BaseModel):
    criticalColor: Optional[str] = None
    highPriorityColor: Optional[str] = None
    mediumPriorityColor: Optional[str] = None
    lowPriorityColor: Optional[str] = None

class AnalysisDetails(BaseModel):
    extractedInformation: Optional[str] = None
    confidenceBySection: Optional["ConfidenceBreakdown"] = None
    identifiedGaps: List[str]
    assumptionsMade: List[str]
    qualityAssessment: Optional[str] = None
    riskIndicators: List[str]

class AnalysisFlow(BaseModel):
    flowSteps: List["AnalysisFlowStep"]
    decisionPoints: List["DecisionPoint"]
    dataFlow: List["DataFlowStep"]
    alternativeScenarios: List["AlternativeScenario"]

class AnalysisFlowStep(BaseModel):
    stepNumber: Optional[int] = None
    stepName: Optional[str] = None
    stepDescription: Optional[str] = None
    inputData: List[str]
    outcome: Optional[str] = None
    nextSteps: List[str]
    confidence: Optional[float] = None

class AnalysisQuality(BaseModel):
    dataCompleteness: Optional[float] = None
    dataReliability: Optional[float] = None
    analysisDepth: Optional[float] = None
    analysisConsistency: Optional[float] = None
    expertValidation: Optional[float] = None
    overallQuality: Optional[float] = None
    qualityNotes: List[str]

class AttachmentAnalysis(BaseModel):
    hasAttachments: Optional[bool] = None
    attachmentCount: Optional[int] = None
    attachments: List["AttachmentInfo"]
    mentionsAttachments: Optional[bool] = None
    attachmentMismatch: Optional[types.AttachmentMismatchType] = None
    suspiciousFilenames: Optional[bool] = None

class AttachmentInfo(BaseModel):
    filename: Optional[str] = None
    fileType: Optional[str] = None
    isClaimRelated: Optional[bool] = None
    documentType: Optional[str] = None

class CanLIILegalPrecedent(BaseModel):
    caseId: Optional[str] = None
    caseName: Optional[str] = None
    court: Optional[str] = None
    jurisdiction: Optional[str] = None
    decisionDate: Optional[str] = None
    relevanceToCase: Optional[str] = None
    keyPrinciple: Optional[str] = None
    supportsCoverage: Optional[bool] = None
    confidenceLevel: Optional[float] = None
    canliiUrl: Optional[str] = None

class CanadianLegalAnalysis(BaseModel):
    applicableLaw: Optional[str] = None
    provincialRegulations: List[str]
    federalStatutes: List[str]
    legalPrecedents: List["CanLIILegalPrecedent"]
    regulatoryGuidance: List[str]
    interpretationNotes: Optional[str] = None
    jurisdictionalCompliance: Optional[bool] = None
    legalRiskAssessment: Optional[str] = None

class CareAssistanceAssessment(BaseModel):
    familyCareProvided: Optional[bool] = None
    familyCareHours: Optional[float] = None
    familyCareValue: Optional[float] = None
    professionalCareRequired: Optional[bool] = None
    professionalCareHours: Optional[float] = None
    professionalCareCosts: Optional[float] = None
    housekeepingAssistance: Optional[bool] = None
    housekeepingCosts: Optional[float] = None
    personalCareNeeds: List[str]
    careAssistanceDuration: Optional[str] = None
    totalCareAssistanceCosts: Optional[float] = None

class CauseOfLoss(BaseModel):
    primaryCause: Optional[str] = None
    contributingFactors: List[str]
    incidentType: Optional[types.IncidentType] = None
    atFaultParties: List[str]
    circumstances: Optional[str] = None
    weatherConditions: Optional[str] = None
    roadConditions: Optional[str] = None
    trafficViolations: List[str]
    negligenceFactors: List[str]
    causationChain: List[str]

class CauseOfLossMapping(BaseModel):
    primaryCause: Optional[str] = None
    proximateCause: Optional[str] = None
    coverageApplicability: Optional[str] = None
    causationChain: List[str]
    concurrentCauses: List[str]
    causationAnalysis: Optional[str] = None
    canadianCausationLaw: Optional[str] = None

class ChartAnnotation(BaseModel):
    text: Optional[str] = None
    position: Optional[str] = None
    style: Optional[str] = None
    importance: Optional[Union[Literal["HIGH"], Literal["MEDIUM"], Literal["LOW"]]] = None
    analysisRelevance: Optional[float] = None

class ChartDataPoint(BaseModel):
    label: Optional[str] = None
    value: Optional[float] = None
    color: Optional[str] = None
    metadata: Optional[str] = None
    tooltip: Optional[str] = None
    analysisImpact: Optional[float] = None

class CircumstanceDetails(BaseModel):
    environmentalFactors: Optional["EnvironmentalFactors"] = None
    locationDetails: List[str]
    timingFactors: List[str]
    humanBehaviorFactors: List[str]
    equipmentConditions: List[str]

class ClaimDetails(BaseModel):
    incidentDate: Optional[str] = None
    incidentTime: Optional[str] = None
    incidentLocation: Optional[str] = None
    claimantName: Optional[str] = None
    insuredParty: Optional[str] = None
    claimType: Optional[types.ClaimTypeDetailed] = None
    damageDescription: Optional[str] = None
    estimatedAmount: Optional[str] = None
    policeReportNumber: Optional[str] = None
    emergencyServicesInvolved: Optional[bool] = None
    injuriesReported: Optional[bool] = None
    propertyDamage: Optional[bool] = None
    witnessesPresent: Optional[bool] = None
    vehicleInvolved: Optional[str] = None
    thirdPartyInvolved: Optional[bool] = None
    medicalInformation: Optional["MedicalSummary"] = None

class ClaimDocumentInput(BaseModel):
    claimId: Optional[str] = None
    emailContent: Optional[str] = None
    emailSubject: Optional[str] = None
    attachmentNames: List[str]
    attachmentsText: List[str]
    preprocessingNotes: List[str]

class ClaimIndicators(BaseModel):
    incidentKeywords: List[str]
    damageKeywords: List[str]
    legalKeywords: List[str]
    medicalKeywords: List[str]
    timeIndicators: List[str]
    locationIndicators: List[str]
    partyIndicators: List[str]

class ClaimantInformationNeeded(BaseModel):
    documentsRequired: List[str]
    clarificationsNeeded: List[str]
    evidenceRequired: List[str]
    timelineForResponse: Optional[str] = None
    consequencesOfNonCompliance: Optional[str] = None
    assistanceAvailable: Optional[str] = None

class ColorLegend(BaseModel):
    color: Optional[str] = None
    meaning: Optional[str] = None
    examples: List[str]

class ColorSchemeMapping(BaseModel):
    entityTypeColors: List["EntityTypeColor"]
    confidenceColorScheme: Optional["ConfidenceColorScheme"] = None
    analysisColorScheme: Optional["AnalysisColorScheme"] = None

class CommunicationAction(BaseModel):
    communicationType: Optional[str] = None
    communicationRecipient: Optional[str] = None
    communicationPurpose: Optional[str] = None
    communicationTimeline: Optional[str] = None
    communicationMethod: Optional[str] = None
    followUpRequired: Optional[bool] = None

class ConfidenceBreakdown(BaseModel):
    claimDetailsConfidence: Optional[float] = None
    policyDetailsConfidence: Optional[float] = None
    causeOfLossConfidence: Optional[float] = None
    contactDetailsConfidence: Optional[float] = None
    overallConfidence: Optional[float] = None

class ConfidenceColorScheme(BaseModel):
    highConfidenceColor: Optional[str] = None
    mediumConfidenceColor: Optional[str] = None
    lowConfidenceColor: Optional[str] = None

class ConfidenceContributor(BaseModel):
    contributor: Optional[str] = None
    contribution: Optional[float] = None
    reason: Optional[str] = None
    sourceType: Optional[Union[Literal["DOCUMENT_QUALITY"], Literal["TEXT_CLARITY"], Literal["PATTERN_MATCH"], Literal["CROSS_VALIDATION"], Literal["EXPERT_VALIDATION"]]] = None

class ConfidenceDistribution(BaseModel):
    highConfidenceFields: List[str]
    mediumConfidenceFields: List[str]
    lowConfidenceFields: List[str]
    averageConfidence: Optional[float] = None

class ContactInfo(BaseModel):
    name: Optional[str] = None
    role: Optional[types.ContactRole] = None
    phoneNumber: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    driverLicense: Optional[str] = None
    relationship: Optional[str] = None
    insuranceInfo: Optional[str] = None
    employer: Optional[str] = None
    notes: Optional[str] = None

class ContextualFactors(BaseModel):
    analysisLevel: Optional[str] = None
    analysisContext: Optional[str] = None
    domainSpecificFactors: List[str]
    regulatoryFactors: List[str]
    businessRules: List[str]
    qualityFactors: List[str]

class ContradictoryEvidence(BaseModel):
    evidenceType: Optional[str] = None
    evidenceDescription: Optional[str] = None
    contradictionReason: Optional[str] = None
    responseToContradiction: Optional[str] = None
    contradictionWeight: Optional[float] = None
    mitigationStrategy: Optional[str] = None

class ContributoryAnalysis(BaseModel):
    claimantFactors: List[str]
    awarenessLevel: Optional[Union[Literal["fully_aware"], Literal["should_have_known"], Literal["unaware"], Literal["distracted"]]] = None
    avoidabilityFactor: Optional[Union[Literal["easily_avoidable"], Literal["avoidable_with_care"], Literal["difficult_to_avoid"], Literal["unavoidable"]]] = None
    behaviorFactors: List[str]
    mitigatingCircumstances: List[str]
    aggravatingCircumstances: List[str]
    contributionPercentage: Optional[float] = None

class CoverageAnalysisDetails(BaseModel):
    analysisCompleteness: Optional[float] = None
    analysisConfidence: Optional[float] = None
    keyFindings: List[str]
    analysisLimitations: List[str]
    assumptionsMade: List[str]
    uncertaintyAreas: List[str]
    additionalAnalysisNeeded: List[str]

class CoverageDecisionSupport(BaseModel):
    supportingEvidence: List["SupportingEvidence"]
    contradictoryEvidence: List["ContradictoryEvidence"]
    evidenceWeighting: Optional["EvidenceWeighting"] = None
    expertOpinions: List["ExpertOpinion"]
    documentationQuality: Optional["DocumentationQuality"] = None
    evidenceGaps: List[str]

class CoverageJustification(BaseModel):
    primaryReason: Optional[types.CoverageReason] = None
    detailedReasoning: Optional[str] = None
    policyBasis: Optional[str] = None
    legalBasis: Optional[str] = None
    factualBasis: Optional[str] = None
    industryPractice: Optional[str] = None
    precedentSupport: List[str]
    riskFactors: List[str]
    alternativeInterpretations: List[str]
    decisionStrength: Optional[float] = None

class CoverageMapping(BaseModel):
    causeOfLoss: Optional[str] = None
    policySection: Optional[str] = None
    coverageRationale: Optional[str] = None
    coverageStrength: Optional[float] = None
    counterArguments: List[str]
    supportingArguments: List[str]
    industryPractice: Optional[str] = None

class DataCompletenessAssessment(BaseModel):
    overallCompletenessScore: Optional[float] = None
    level01AnalysisCompleteness: Optional[float] = None
    claimDetailsCompleteness: Optional[float] = None
    ocrDocumentsCompleteness: Optional[float] = None
    attachmentsCompleteness: Optional[float] = None
    level01AnalysisImportance: Optional[float] = None
    claimDetailsImportance: Optional[float] = None
    ocrDocumentsImportance: Optional[float] = None
    attachmentsImportance: Optional[float] = None
    criticalMissingItems: List[str]
    minorMissingItems: List[str]
    dataQualityScore: Optional[float] = None
    analysisReadiness: Optional[str] = None
    improvementRecommendations: List[str]
    alternativeDataSources: List[str]
    claimTypeSpecificNeeds: Optional[str] = None
    riskFactorsFromGaps: List[str]
    assessmentConfidence: Optional[float] = None
    uncertaintyAreas: List[str]

class DataFlowStep(BaseModel):
    stepName: Optional[str] = None
    sourceData: List[str]
    processing: Optional[str] = None
    outputData: List[str]
    dataQuality: Optional[float] = None

class DataItem(BaseModel):
    itemName: Optional[str] = None
    isPresent: Optional[bool] = None
    qualityScore: Optional[float] = None
    importanceForClaimType: Optional[float] = None
    alternativeSources: List[str]
    impactOnAnalysis: Optional[str] = None

class DecisionPoint(BaseModel):
    decisionName: Optional[str] = None
    decisionDescription: Optional[str] = None
    inputFactors: List[str]
    outcome: Optional[str] = None
    confidence: Optional[float] = None
    alternativeOptions: List[str]

class DiagnosticResult(BaseModel):
    diagnosticType: Optional[str] = None
    findings: Optional[str] = None
    datePerformed: Optional[str] = None
    facility: Optional[str] = None
    abnormalFindings: Optional[bool] = None
    supportsCausation: Optional[bool] = None

class DocumentHighlight(BaseModel):
    documentId: Optional[str] = None
    documentName: Optional[str] = None
    pageNumber: Optional[int] = None
    highlights: List["HighlightRegion"]

class DocumentationAction(BaseModel):
    documentType: Optional[str] = None
    documentPurpose: Optional[str] = None
    documentRecipient: Optional[str] = None
    documentTimeline: Optional[str] = None
    documentImportance: Optional[Union[Literal["CRITICAL"], Literal["HIGH"], Literal["NORMAL"], Literal["LOW"]]] = None

class DocumentationQuality(BaseModel):
    completeness: Optional[float] = None
    reliability: Optional[float] = None
    consistency: Optional[float] = None
    timeliness: Optional[float] = None
    authentication: Optional[float] = None
    qualityNotes: Optional[str] = None

class EmailAttachment(BaseModel):
    filename: Optional[str] = None
    contentType: Optional[str] = None
    size: Optional[int] = None

class EmailForClassification(BaseModel):
    subject: Optional[str] = None
    body: Optional[str] = None
    senderEmail: Optional[str] = None
    senderName: Optional[str] = None
    receivedDate: Optional[str] = None
    attachments: List["EmailAttachment"]

class EntityMapping(BaseModel):
    fieldName: Optional[str] = None
    extractedValue: Optional[str] = None
    originalText: Optional[str] = None
    sourceDocument: Optional[str] = None
    pageNumber: Optional[int] = None
    lineNumber: Optional[int] = None
    startPosition: Optional[int] = None
    endPosition: Optional[int] = None
    boundingBox: Optional[List[float]] = None
    extractionMethod: Optional[str] = None
    confidence: Optional[float] = None
    highlightColor: Optional[str] = None
    entityType: Optional[str] = None
    relevanceScore: Optional[float] = None

class EntityTypeColor(BaseModel):
    entityType: Optional[str] = None
    primaryColor: Optional[str] = None
    secondaryColor: Optional[str] = None
    textColor: Optional[str] = None
    weight: Optional[float] = None

class EnvironmentalFactors(BaseModel):
    weatherConditions: Optional[str] = None
    lightingConditions: Optional[str] = None
    surfaceConditions: Optional[str] = None
    visibilityFactors: List[str]
    crowdingLevel: Optional[Union[Literal["empty"], Literal["light"], Literal["moderate"], Literal["heavy"], Literal["overcrowded"]]] = None

class EvidenceQuality(BaseModel):
    witnessStatements: List[str]
    documentaryEvidence: List[str]
    physicalEvidence: List[str]
    expertOpinions: List[str]
    overallReliability: Optional[float] = None
    evidenceGaps: List[str]

class EvidenceWeighting(BaseModel):
    documentaryEvidence: Optional[float] = None
    witnessTestimony: Optional[float] = None
    expertOpinion: Optional[float] = None
    physicalEvidence: Optional[float] = None
    circumstantialEvidence: Optional[float] = None
    weightingRationale: Optional[str] = None

class ExclusionAnalysis(BaseModel):
    potentialExclusions: List["PolicyExclusion"]
    applicableExclusions: List["PolicyExclusion"]
    exclusionJustification: Optional[str] = None
    canadianExclusionPrecedents: List["CanLIILegalPrecedent"]
    exclusionInterpretation: Optional[str] = None
    exclusionRisk: Optional[types.RiskLevel] = None

class ExitAnalysis(BaseModel):
    exitReason: Optional[str] = None
    analysisProvided: Optional["AnalysisDetails"] = None
    nextStepsRequired: Optional["NextSteps"] = None
    estimatedTimeToResolution: Optional[str] = None
    priorityLevel: Optional[types.PriorityLevel] = None
    automationOpportunities: List[str]
    humanReviewItems: List[str]

class ExpertOpinion(BaseModel):
    expertType: Optional[str] = None
    expertCredentials: Optional[str] = None
    opinionSummary: Optional[str] = None
    opinionBasis: Optional[str] = None
    opinionReliability: Optional[float] = None
    opinionImpact: Optional[str] = None
    conflictingOpinions: List[str]

class ExplainabilityInsight(BaseModel):
    analysisType: Optional[Union[Literal["LIME"], Literal["SHAP"], Literal["INTEGRATED_GRADIENTS"], Literal["ATTENTION"]]] = None
    decisionContext: Optional[str] = None
    featureImportances: List["FeatureImportance"]
    topPositiveFactors: List["ExplanationFactor"]
    topNegativeFactors: List["ExplanationFactor"]
    confidenceContributors: List["ConfidenceContributor"]
    alternativeScenarios: List["AlternativeScenario"]
    visualizationData: Optional["VisualizationData"] = None
    methodologyNotes: List[str]
    contextualFactors: Optional["ContextualFactors"] = None

class ExplanationFactor(BaseModel):
    factor: Optional[str] = None
    impact: Optional[float] = None
    explanation: Optional[str] = None
    sourceLocation: Optional[str] = None
    evidenceStrength: Optional[Union[Literal["STRONG"], Literal["MODERATE"], Literal["WEAK"]]] = None
    supportingEvidence: List[str]

class FaultGuidance(BaseModel):
    suggestedPrimaryFault: Optional[float] = None
    suggestedSecondaryFault: Optional[float] = None
    faultRationale: Optional[str] = None
    uncertaintyAreas: List[str]
    comparisonCases: List[str]
    recommendedRuleSet: Optional[str] = None
    confidenceInGuidance: Optional[float] = None

class FaultImpactOnQuantum(BaseModel):
    claimantFaultPercentage: Optional[float] = None
    faultReductionApplicable: Optional[bool] = None
    specialDamagesReduction: Optional[float] = None
    generalDamagesReduction: Optional[float] = None
    futureCareDamagesReduction: Optional[float] = None
    thresholdConsiderations: List[str]
    faultImpactOnSettlement: Optional[str] = None
    netRecoverableAmount: Optional[float] = None

class FeatureImportance(BaseModel):
    featureName: Optional[str] = None
    importance: Optional[float] = None
    direction: Optional[Union[Literal["POSITIVE"], Literal["NEGATIVE"], Literal["NEUTRAL"]]] = None
    explanation: Optional[str] = None
    sourceText: Optional[str] = None
    confidence: Optional[float] = None
    relatedFields: List[str]
    analysisContext: Optional[str] = None

class FieldConfidence(BaseModel):
    fieldName: Optional[str] = None
    confidence: Optional[float] = None
    contributingFactors: List[str]
    uncertaintyReasons: List[str]
    sourceQuality: Optional[float] = None
    validationStrength: Optional[float] = None

class FieldConfidenceBreakdown(BaseModel):
    overallConfidence: Optional[float] = None
    fieldConfidences: List["FieldConfidence"]
    uncertaintyFactors: List[str]
    confidenceDistribution: Optional["ConfidenceDistribution"] = None
    analysisCertainty: Optional[float] = None

class FutureCareAssessment(BaseModel):
    futureMedicalTreatment: Optional[bool] = None
    futureTreatmentCosts: Optional[float] = None
    futureTherapyCosts: Optional[float] = None
    futureCarePeriod: Optional[str] = None
    assistiveDevices: List[str]
    assistiveDeviceCosts: Optional[float] = None
    homeModifications: List[str]
    homeModificationCosts: Optional[float] = None
    attendantCareRequired: Optional[bool] = None
    attendantCareCosts: Optional[float] = None
    totalFutureCareCosts: Optional[float] = None
    futureCareUncertainty: Optional[float] = None

class GeneralDamagesAssessment(BaseModel):
    painLevel: Optional[Union[Literal["minimal"], Literal["mild"], Literal["moderate"], Literal["severe"], Literal["extreme"]]] = None
    sufferingLevel: Optional[Union[Literal["minimal"], Literal["mild"], Literal["moderate"], Literal["severe"], Literal["extreme"]]] = None
    functionalImpairment: Optional[Union[Literal["none"], Literal["minimal"], Literal["moderate"], Literal["significant"], Literal["severe"], Literal["unknown"]]] = None
    lifestyleImpact: Optional[Union[Literal["none"], Literal["minimal"], Literal["moderate"], Literal["significant"], Literal["severe"], Literal["unknown"]]] = None
    psychologicalImpact: Optional[Union[Literal["none"], Literal["minimal"], Literal["moderate"], Literal["significant"], Literal["severe"], Literal["unknown"]]] = None
    relationshipImpact: Optional[Union[Literal["none"], Literal["minimal"], Literal["moderate"], Literal["significant"], Literal["severe"], Literal["unknown"]]] = None
    ageAtTimeOfAccident: Optional[int] = None
    genderConsiderations: Optional[str] = None
    permanentDisabilityFactor: Optional[float] = None
    comparableAwards: List[str]
    generalDamagesRange: Optional[str] = None
    recommendedGeneralDamages: Optional[float] = None
    ontarioBenchmarkCategory: Optional[Union[Literal["minor"], Literal["moderate"], Literal["severe"], Literal["catastrophic"], Literal["unknown"]]] = None
    benchmarkJustification: Optional[str] = None
    conservativeEstimate: Optional[bool] = None

class HighlightRegion(BaseModel):
    fieldName: Optional[str] = None
    text: Optional[str] = None
    startPos: Optional[int] = None
    endPos: Optional[int] = None
    boundingBox: Optional[List[float]] = None
    highlightColor: Optional[str] = None
    confidence: Optional[float] = None
    entityType: Optional[str] = None
    analysisRelevance: Optional[float] = None

class IncomeLossAssessment(BaseModel):
    preAccidentIncome: Optional[float] = None
    employmentStatus: Optional[Union[Literal["employed"], Literal["self_employed"], Literal["unemployed"], Literal["student"], Literal["retired"], Literal["unknown"]]] = None
    jobTitle: Optional[str] = None
    timeOffWork: Optional[str] = None
    workDaysLost: Optional[int] = None
    wageReplacementAmount: Optional[float] = None
    returnToWorkStatus: Optional[Union[Literal["full_capacity"], Literal["reduced_capacity"], Literal["modified_duties"], Literal["unable_to_return"]]] = None
    futureEarningImpact: Optional[Union[Literal["no_impact"], Literal["temporary_reduction"], Literal["permanent_reduction"], Literal["total_disability"]]] = None
    documentedIncomeLoss: Optional[float] = None
    projectedFutureIncomeLoss: Optional[float] = None
    incomeLossCertainty: Optional[float] = None
    returnToWorkCapacity: Optional[float] = None
    ageFactorAdjustment: Optional[Union[Literal["none"], Literal["minor"], Literal["significant"], Literal["unknown"]]] = None

class InformationRequest(BaseModel):
    informationType: Optional[str] = None
    informationSource: Optional[types.InformationSource] = None
    informationDetails: Optional[str] = None
    justification: Optional[str] = None
    urgencyLevel: Optional[Union[Literal["IMMEDIATE"], Literal["HIGH"], Literal["NORMAL"], Literal["LOW"]]] = None
    timelineRequired: Optional[str] = None
    alternativeSources: List[str]
    impactOnDecision: Optional[str] = None

class InjuryDetail(BaseModel):
    bodyPart: Optional[str] = None
    injuryType: Optional[str] = None
    severity: Optional[types.InjurySeverity] = None
    description: Optional[str] = None
    isPermanent: Optional[bool] = None
    causationClear: Optional[bool] = None
    priorHistory: Optional[bool] = None

class InjuryImpactAnalysis(BaseModel):
    injurySeverityFactor: Optional[Union[Literal["minor"], Literal["moderate"], Literal["severe"], Literal["catastrophic"]]] = None
    medicalCausationCertainty: Optional[float] = None
    treatmentComplexity: Optional[Union[Literal["simple"], Literal["moderate"], Literal["complex"], Literal["ongoing_care"]]] = None
    functionalImpact: Optional[Union[Literal["minimal"], Literal["moderate"], Literal["significant"], Literal["life_altering"]]] = None
    workImpact: Optional[Union[Literal["no_time_off"], Literal["short_absence"], Literal["extended_absence"], Literal["permanent_disability"]]] = None
    recoveryPrognosis: Optional[Union[Literal["full_recovery"], Literal["partial_recovery"], Literal["ongoing_limitations"], Literal["permanent_impairment"]]] = None
    preExistingFactors: List[str]
    medicalDocumentationQuality: Optional[float] = None
    liabilityAggravationFactor: Optional[float] = None

class InteractiveElement(BaseModel):
    elementType: Optional[Union[Literal["HOVER_DETAIL"], Literal["CLICK_DRILL_DOWN"], Literal["FILTER"], Literal["ZOOM"], Literal["COMPARE"]]] = None
    targetField: Optional[str] = None
    action: Optional[str] = None
    description: Optional[str] = None
    context: Optional[str] = None

class InvestigationAction(BaseModel):
    investigationType: Optional[str] = None
    investigationScope: Optional[str] = None
    investigationTimeline: Optional[str] = None
    investigationResources: Optional[str] = None
    investigationCost: Optional[str] = None
    investigationExpectedOutcome: Optional[str] = None

class LegalAction(BaseModel):
    legalActionType: Optional[str] = None
    legalBasis: Optional[str] = None
    legalTimeline: Optional[str] = None
    legalCost: Optional[str] = None
    legalRisk: Optional[str] = None
    alternativesToLegalAction: List[str]

class Level01Analysis(BaseModel):
    claimDetails: Optional["ClaimDetails"] = None
    policyDetails: Optional["PolicyDetails"] = None
    causeOfLoss: Optional["CauseOfLoss"] = None
    contactDetails: List["ContactInfo"]
    documentsSufficient: Optional[bool] = None
    policyNumberAvailable: Optional[bool] = None
    policyDetailsComplete: Optional[bool] = None
    exitPath: Optional[types.ExitPath] = None
    exitAnalysis: Optional["ExitAnalysis"] = None
    sparkNlpInsights: Optional["SparkNlpEnhancedData"] = None
    confidenceScore: Optional[float] = None
    processingNotes: List[str]
    dataQualityScore: Optional[float] = None
    canadianJurisdiction: Optional[str] = None
    legalConsiderations: List[str]
    regulatoryNotes: List[str]
    uiMetadata: Optional["UIMetadata"] = None
    explainabilityInsights: List["ExplainabilityInsight"]
    analysisTimestamp: Optional[str] = None
    modelVersion: Optional[str] = None
    processingTimeMs: Optional[int] = None

class Level01Summary(BaseModel):
    claimId: Optional[str] = None
    claimType: Optional[str] = None
    policyNumber: Optional[str] = None
    incidentDate: Optional[str] = None
    primaryCause: Optional[str] = None
    level01Confidence: Optional[float] = None
    level01ExitPath: Optional[str] = None
    keyFindings: List[str]

class Level02AnalysisInput(BaseModel):
    claimId: Optional[str] = None
    level01Analysis: Optional["Level01Summary"] = None
    policyDocuments: List[str]
    additionalEvidence: List[str]
    humanInputs: List[str]
    processingNotes: List[str]
    urgencyLevel: Optional[Union[Literal["IMMEDIATE"], Literal["HIGH"], Literal["NORMAL"], Literal["LOW"]]] = None
    specialInstructions: List[str]

class Level02CoverageAnalysis(BaseModel):
    coverageDecision: Optional[types.CoverageDecision] = None
    confidenceScore: Optional[float] = None
    policyAnalysis: Optional["PolicyCoverageAnalysis"] = None
    exclusionAnalysis: Optional["ExclusionAnalysis"] = None
    coverageMapping: Optional["CoverageMapping"] = None
    causeMapping: Optional["CauseOfLossMapping"] = None
    coverageJustification: Optional["CoverageJustification"] = None
    decisionSupport: Optional["CoverageDecisionSupport"] = None
    canadianLegalAnalysis: Optional["CanadianLegalAnalysis"] = None
    medicalImpactAssessment: Optional["MedicalImpactAssessment"] = None
    claimantInformation: Optional["ClaimantInformationNeeded"] = None
    thirdPartyInformation: List["ThirdPartyInformationNeeded"]
    agentInformation: List["AgentObtainableInformation"]
    allInformationRequests: List["InformationRequest"]
    exitAnalysis: Optional["Level02ExitAnalysis"] = None
    analysisQuality: Optional["AnalysisQuality"] = None
    riskAssessment: Optional["Level02RiskAssessment"] = None
    uncertaintyAreas: List["UncertaintyArea"]
    uiMetadata: Optional["UIMetadata"] = None
    explainabilityInsights: List["ExplainabilityInsight"]
    level01Data: Optional["Level01Summary"] = None
    analysisTimestamp: Optional[str] = None
    processingTimeMs: Optional[int] = None
    modelVersion: Optional[str] = None
    analystId: Optional[str] = None

class Level02ExitAnalysis(BaseModel):
    exitPath: Optional[types.CoverageDecision] = None
    exitReason: Optional[str] = None
    coverageAnalysisProvided: Optional["CoverageAnalysisDetails"] = None
    nextStepsRequired: Optional["Level02NextSteps"] = None
    riskAssessment: Optional["Level02RiskAssessment"] = None
    humanReviewRequired: Optional[bool] = None
    legalCounselRequired: Optional[bool] = None
    timelineForResolution: Optional[str] = None
    priorityLevel: Optional[types.PriorityLevel] = None
    escalationTriggers: List[str]

class Level02NextSteps(BaseModel):
    immediateActions: List[str]
    shortTermActions: List[str]
    longTermActions: List[str]
    documentationRequired: List["DocumentationAction"]
    communicationRequired: List["CommunicationAction"]
    investigationRequired: List["InvestigationAction"]
    legalActionRequired: List["LegalAction"]

class Level02RiskAssessment(BaseModel):
    coverageRisk: Optional[types.RiskLevel] = None
    legalRisk: Optional[types.RiskLevel] = None
    financialRisk: Optional[types.RiskLevel] = None
    reputationalRisk: Optional[types.RiskLevel] = None
    regulatoryRisk: Optional[types.RiskLevel] = None
    overallRisk: Optional[types.RiskLevel] = None
    riskMitigationStrategies: List[str]
    riskMonitoringRequired: Optional[bool] = None

class Level03AnalysisInput(BaseModel):
    claimReference: Optional[str] = None
    province: Optional[str] = None
    level01Analysis: Optional[str] = None
    level02Coverage: Optional[str] = None
    emailContent: List[str]
    sparkNlpInsights: Optional[str] = None
    ocrTexts: List[str]
    attachmentDetails: List[str]

class Level04AnalysisInput(BaseModel):
    claimReference: Optional[str] = None
    province: Optional[str] = None
    level01Analysis: Optional[str] = None
    level02Coverage: Optional[str] = None
    level03Fault: Optional[str] = None
    emailContent: List[str]
    sparkNlpInsights: Optional[str] = None
    ocrTexts: List[str]
    attachmentDetails: List[str]

class LiabilityFactorExtraction(BaseModel):
    accidentClassification: Optional["AccidentClassification"] = None
    negligenceFactors: Optional["NegligenceAnalysis"] = None
    contributoryFactors: Optional["ContributoryAnalysis"] = None
    evidenceQuality: Optional["EvidenceQuality"] = None
    circumstanceDetails: Optional["CircumstanceDetails"] = None
    structuredForRules: Optional["StructuredCircumstances"] = None
    injuryImpactAnalysis: Optional["InjuryImpactAnalysis"] = None
    faultGuidance: Optional["FaultGuidance"] = None
    uiMetadata: Optional["UIMetadata"] = None
    explainabilityInsights: List["ExplainabilityInsight"]

class MedicalDamageAssessment(BaseModel):
    emergencyCareCosts: Optional[float] = None
    ongoingTreatmentCosts: Optional[float] = None
    diagnosticCosts: Optional[float] = None
    medicationCosts: Optional[float] = None
    medicalEquipmentCosts: Optional[float] = None
    treatmentSessions: Optional[int] = None
    treatmentDuration: Optional[str] = None
    medicalProviders: List[str]
    medicalComplexity: Optional[Union[Literal["simple"], Literal["moderate"], Literal["complex"], Literal["severe"], Literal["unknown"]]] = None
    documentedMedicalCosts: Optional[float] = None
    estimatedFutureMedical: Optional[float] = None
    medicalCostCertainty: Optional[float] = None
    currentTreatmentStatus: Optional[Union[Literal["ongoing"], Literal["completed"], Literal["planned"], Literal["unknown"]]] = None
    recoveryPrognosis: Optional[Union[Literal["excellent"], Literal["good"], Literal["fair"], Literal["poor"], Literal["unknown"]]] = None

class MedicalImpactAssessment(BaseModel):
    injurySeverityLevel: Optional[types.MedicalComplexity] = None
    treatmentComplexity: Optional[types.MedicalComplexity] = None
    functionalImpairment: Optional[str] = None
    returnToWorkLikelihood: Optional[Union[Literal["full_return"], Literal["modified_duties"], Literal["partial_return"], Literal["unlikely"]]] = None
    futureCareCosts: Optional[str] = None
    permanentDisability: Optional[bool] = None
    medicalCausationClear: Optional[bool] = None
    preExistingConditions: List[str]
    medicalDocumentationQuality: Optional[Union[Literal["excellent"], Literal["good"], Literal["adequate"], Literal["poor"]]] = None
    expertMedicalOpinionNeeded: Optional[bool] = None

class MedicalSummary(BaseModel):
    injuriesReported: Optional[bool] = None
    injuryDetails: List["InjuryDetail"]
    treatmentRecords: List["TreatmentRecord"]
    diagnosticResults: List["DiagnosticResult"]
    medicalTimeline: Optional["MedicalTimeline"] = None
    medicalProfessionals: List["ContactInfo"]
    medicalCosts: Optional[str] = None
    futureCareneeds: Optional[str] = None
    impactOnDailyLife: Optional[str] = None
    returnToWorkPrognosis: Optional[str] = None
    medicalDocumentsAvailable: List[str]
    medicalDocumentsNeeded: List[str]

class MedicalTimeline(BaseModel):
    incidentDate: Optional[str] = None
    firstTreatmentDate: Optional[str] = None
    emergencyTreatment: Optional[bool] = None
    timeOffWork: Optional[str] = None
    returnToWorkDate: Optional[str] = None
    treatmentEndDate: Optional[str] = None
    functionalLimitations: List[str]

class NegligenceAnalysis(BaseModel):
    propertyOwnerFactors: List[str]
    vehicleOperatorFactors: List[str]
    thirdPartyFactors: List[str]
    institutionalFactors: List[str]
    maintenanceFailures: List[str]
    warningDeficiencies: List[str]
    dutyOfCareBreaches: List[str]
    statutoryViolations: List[str]

class NextSteps(BaseModel):
    documentsNeeded: List[str]
    informationNeeded: List[str]
    contactsToReach: List[str]
    timelineForResponse: Optional[str] = None
    escalationRequired: Optional[bool] = None
    automatedActions: List[str]
    manualActions: List[str]

class OntarioIncomeLossStandards(BaseModel):
    averageWeeklyEarnings: Optional[str] = None
    maximumBenefitPeriod: Optional[str] = None
    returnToWorkAssumption: Optional[str] = None
    ageFactors: Optional[str] = None
    documentationRequired: Optional[str] = None
    ageSpecificGuidelines: Optional[str] = None
    evidenceBasedCalculations: Optional[str] = None
    conservativeApproach: Optional[str] = None

class OntarioMedicalGuidelines(BaseModel):
    emergencyCareTypical: Optional[str] = None
    physiotherapySessionCost: Optional[float] = None
    chiropracticSessionCost: Optional[float] = None
    diagnosticImagingCosts: Optional[str] = None
    maximumTreatmentDuration: Optional[str] = None
    documentationRequirement: Optional[str] = None
    evidenceBasedLimits: Optional[str] = None
    moderateInjuryMedicalCap: Optional[float] = None
    ageAdjustedRecovery: Optional[str] = None

class OntarioPainSufferingGuide(BaseModel):
    minorInjuryRange: Optional[str] = None
    moderateInjuryRange: Optional[str] = None
    severeInjuryRange: Optional[str] = None
    catastrophicInjuryRange: Optional[str] = None
    slipFallBenchmarks: Optional[str] = None
    andrewsCap2025: Optional[float] = None
    deductible2025: Optional[float] = None
    conservativeApproach: Optional[str] = None

class OntarioQuantumBenchmarks(BaseModel):
    painAndSufferingBenchmarks: Optional["OntarioPainSufferingGuide"] = None
    medicalCostGuidelines: Optional["OntarioMedicalGuidelines"] = None
    incomeLossStandards: Optional["OntarioIncomeLossStandards"] = None
    statutoryThresholds: Optional["OntarioStatutoryThresholds"] = None

class OntarioStatutoryThresholds(BaseModel):
    motorVehicleThreshold: Optional[str] = None
    nonEconomicLossDeductible: Optional[float] = None
    andrewsCapAmount: Optional[float] = None
    catastrophicThreshold: Optional[str] = None
    faultReductionRules: Optional[str] = None

class PolicyCoverageAnalysis(BaseModel):
    applicableCoverageTypes: List[str]
    coverageLimits: Optional[str] = None
    deductibleAmount: Optional[str] = None
    policyConditions: List[str]
    conditionsMet: Optional[bool] = None
    coverageInterpretation: Optional[str] = None
    ambiguousTerms: List[str]
    industryStandards: Optional[str] = None

class PolicyDetails(BaseModel):
    policyNumber: Optional[str] = None
    policyHolder: Optional[str] = None
    insuredVehicle: Optional[str] = None
    effectiveDate: Optional[str] = None
    expiryDate: Optional[str] = None
    coverageTypes: List[str]
    deductibleAmount: Optional[str] = None
    policyLimits: Optional[str] = None
    insuranceCompany: Optional[str] = None
    agentDetails: Optional[str] = None
    hasCompleteInformation: Optional[bool] = None

class PolicyExclusion(BaseModel):
    exclusionType: Optional[str] = None
    exclusionText: Optional[str] = None
    applicabilityReason: Optional[str] = None
    legalBasis: Optional[str] = None
    precedentSupport: List[str]
    interpretationChallenges: List[str]

class PriorityRiskAssessment(BaseModel):
    overallPriorityLevel: Optional[types.PriorityLevel] = None
    overallRiskScore: Optional[float] = None
    priorityDrivers: List[str]
    timelineSensitivity: Optional[str] = None
    stakeholderImpact: Optional[str] = None
    identifiedRisks: List["RiskFactor"]
    highestRiskCategory: Optional[types.RiskCategory] = None
    riskMitigationPriority: List[str]
    requiresHumanReview: Optional[bool] = None
    requiresLegalCounsel: Optional[bool] = None
    requiresSpecialistReview: Optional[bool] = None
    reviewReason: Optional[str] = None
    recommendedProcessingTimeline: Optional[str] = None
    escalationTriggers: List[str]
    urgencyJustification: Optional[str] = None
    estimatedFinancialExposure: Optional[str] = None
    costOfDelay: Optional[str] = None
    assessmentConfidence: Optional[float] = None
    uncertaintyFactors: List[str]
    immediateActions: List[str]
    processOptimizations: List[str]
    preventiveRecommendations: List[str]

class QuantumCalculationGuidance(BaseModel):
    totalSpecialDamages: Optional[float] = None
    totalGeneralDamages: Optional[float] = None
    totalFutureCareDamages: Optional[float] = None
    grossDamagesTotal: Optional[float] = None
    netDamagesTotal: Optional[float] = None
    quantumConfidence: Optional[float] = None
    quantumRationale: Optional[str] = None
    provincialFactors: List[str]
    uncertaintyAreas: List[str]
    recommendedExpertReports: List[str]
    settlementRange: Optional[str] = None
    litigationRisk: Optional[Union[Literal["low"], Literal["moderate"], Literal["high"], Literal["very_high"], Literal["unknown"]]] = None
    settlementStrategy: Optional[Union[Literal["negotiate"], Literal["mediate"], Literal["litigate"], Literal["settle_fast"], Literal["unknown"]]] = None

class QuantumDamageExtraction(BaseModel):
    medicalDamages: Optional["MedicalDamageAssessment"] = None
    incomeLossDamages: Optional["IncomeLossAssessment"] = None
    careAndAssistanceCosts: Optional["CareAssistanceAssessment"] = None
    specialDamages: Optional["SpecialDamagesBreakdown"] = None
    generalDamages: Optional["GeneralDamagesAssessment"] = None
    futureCareCosts: Optional["FutureCareAssessment"] = None
    faultImpactAnalysis: Optional["FaultImpactOnQuantum"] = None
    quantumGuidance: Optional["QuantumCalculationGuidance"] = None
    ontarioBenchmarks: Optional["OntarioQuantumBenchmarks"] = None
    validationChecks: Optional["QuantumValidationChecks"] = None
    uiMetadata: Optional["UIMetadata"] = None
    explainabilityInsights: List["ExplainabilityInsight"]

class QuantumValidationChecks(BaseModel):
    painSufferingValidation: Optional[str] = None
    totalDamagesValidation: Optional[str] = None
    medicalCostValidation: Optional[str] = None
    settlementRangeValidation: Optional[str] = None
    overvaluationRisk: Optional[Union[Literal["low"], Literal["moderate"], Literal["high"], Literal["critical"]]] = None
    recommendedAdjustments: List[str]
    comparisonToActualClaims: Optional[str] = None

class Resume(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    experience: List[str]
    skills: List[str]

class RiskAssessment(BaseModel):
    liabilityRisk: Optional[Union[Literal["low"], Literal["medium"], Literal["high"], Literal["critical"]]] = None
    financialRisk: Optional[Union[Literal["low"], Literal["medium"], Literal["high"], Literal["critical"]]] = None
    reputationalRisk: Optional[Union[Literal["low"], Literal["medium"], Literal["high"], Literal["critical"]]] = None
    regulatoryRisk: Optional[Union[Literal["low"], Literal["medium"], Literal["high"], Literal["critical"]]] = None
    overallRiskScore: Optional[float] = None

class RiskFactor(BaseModel):
    category: Optional[types.RiskCategory] = None
    severity: Optional[Union[Literal["LOW"], Literal["MEDIUM"], Literal["HIGH"], Literal["CRITICAL"]]] = None
    description: Optional[str] = None
    likelihood: Optional[float] = None
    potentialImpact: Optional[str] = None
    mitigationStrategy: Optional[str] = None
    timeframeToResolve: Optional[str] = None
    requiresSpecialistReview: Optional[bool] = None

class SparkNlpDateEntity(BaseModel):
    text: Optional[str] = None
    normalizedDate: Optional[str] = None
    dateType: Optional[Union[Literal["INCIDENT_DATE"], Literal["POLICY_DATE"], Literal["REPORT_DATE"], Literal["EFFECTIVE_DATE"], Literal["EXPIRY_DATE"], Literal["OTHER"]]] = None
    confidence: Optional[float] = None
    context: Optional[str] = None

class SparkNlpEnhancedData(BaseModel):
    enhancedEntities: List["SparkNlpEntity"]
    financialEntities: List["SparkNlpFinancialEntity"]
    extractedDates: List["SparkNlpDateEntity"]
    locationEntities: List["SparkNlpLocationEntity"]
    confidenceBoost: Optional[float] = None
    processingNotes: List[str]

class SparkNlpEntity(BaseModel):
    text: Optional[str] = None
    entityType: Optional[str] = None
    confidence: Optional[float] = None
    context: Optional[str] = None
    startPosition: Optional[int] = None
    endPosition: Optional[int] = None

class SparkNlpFinancialEntity(BaseModel):
    text: Optional[str] = None
    entityType: Optional[Union[Literal["MONETARY_AMOUNT"], Literal["POLICY_NUMBER"], Literal["CLAIM_NUMBER"], Literal["PERCENTAGE"], Literal["DEDUCTIBLE"], Literal["COVERAGE_LIMIT"]]] = None
    normalizedValue: Optional[str] = None
    confidence: Optional[float] = None
    context: Optional[str] = None

class SparkNlpLocationEntity(BaseModel):
    text: Optional[str] = None
    addressType: Optional[Union[Literal["INCIDENT_LOCATION"], Literal["MAILING_ADDRESS"], Literal["BUSINESS_ADDRESS"], Literal["PROPERTY_ADDRESS"], Literal["OTHER"]]] = None
    normalizedAddress: Optional[str] = None
    confidence: Optional[float] = None
    context: Optional[str] = None

class SpecialDamagesBreakdown(BaseModel):
    transportationCosts: Optional[float] = None
    accommodationCosts: Optional[float] = None
    prescriptionCosts: Optional[float] = None
    medicalSuppliesCosts: Optional[float] = None
    parkingFees: Optional[float] = None
    lostBenefits: Optional[float] = None
    otherOutOfPocketCosts: Optional[float] = None
    outOfPocketReceipts: List[str]
    specialDamagesTotal: Optional[float] = None
    specialDamagesCertainty: Optional[float] = None

class StructuredCircumstances(BaseModel):
    trafficViolations: List[str]
    rightOfWayFactors: List[str]
    vehicleConditions: List[str]
    roadConditions: List[str]
    visitorStatus: Optional[Union[Literal["invitee"], Literal["licensee"], Literal["trespasser"], Literal["employee"]]] = None
    locationType: Optional[Union[Literal["commercial"], Literal["residential"], Literal["industrial"], Literal["public"], Literal["recreational"]]] = None
    hazardType: Optional[str] = None
    warningsPosted: Optional[bool] = None
    mitigationEfforts: List[str]
    dutyOfCareLevel: Optional[Union[Literal["low"], Literal["standard"], Literal["high"], Literal["statutory"]]] = None
    breachFactors: List[str]
    causationChain: List[str]
    contributingFactors: List[str]
    mitigatingFactors: List[str]
    atFaultParties: List[str]

class SupportingEvidence(BaseModel):
    evidenceType: Optional[str] = None
    evidenceDescription: Optional[str] = None
    evidenceSource: Optional[str] = None
    evidenceStrength: Optional[float] = None
    evidenceReliability: Optional[float] = None
    evidenceImpact: Optional[str] = None

class ThirdPartyInformationNeeded(BaseModel):
    thirdPartyType: Optional[str] = None
    thirdPartyContact: Optional[str] = None
    informationNeeded: Optional[str] = None
    legalBasisForRequest: Optional[str] = None
    voluntaryVsMandatory: Optional[Union[Literal["VOLUNTARY"], Literal["SUBPOENA_REQUIRED"], Literal["COURT_ORDER_NEEDED"]]] = None
    costImplications: Optional[str] = None
    alternativeApproaches: List[str]

class TreatmentRecord(BaseModel):
    treatmentType: Optional[types.TreatmentType] = None
    provider: Optional[str] = None
    treatmentDate: Optional[str] = None
    treatmentDetails: Optional[str] = None
    treatmentDuration: Optional[str] = None
    treatmentCost: Optional[str] = None
    ongoing: Optional[bool] = None

class UIMetadata(BaseModel):
    entityMappings: List["EntityMapping"]
    explainabilityData: List["ExplainabilityInsight"]
    documentHighlights: List["DocumentHighlight"]
    confidenceBreakdown: Optional["FieldConfidenceBreakdown"] = None
    colorScheme: Optional["ColorSchemeMapping"] = None
    analysisFlow: Optional["AnalysisFlow"] = None

class UncertaintyArea(BaseModel):
    uncertaintyType: Optional[str] = None
    uncertaintyDescription: Optional[str] = None
    uncertaintyImpact: Optional[str] = None
    resolutionApproach: Optional[str] = None
    resolutionTimeline: Optional[str] = None
    uncertaintyRisk: Optional[types.RiskLevel] = None

class VisualizationData(BaseModel):
    chartType: Optional[Union[Literal["BAR"], Literal["WATERFALL"], Literal["HEATMAP"], Literal["SANKEY"], Literal["FEATURE_IMPORTANCE"], Literal["FLOW_DIAGRAM"]]] = None
    chartData: List["ChartDataPoint"]
    colorScheme: List[str]
    annotations: List["ChartAnnotation"]
    interactiveElements: List["InteractiveElement"]

class ZurichEmailClassificationResult(BaseModel):
    isClaimRelated: Optional[bool] = None
    claimType: Optional[types.ClaimType] = None
    workflowAction: Optional[Union[Literal["PROCEED_TO_ZENDESK"], Literal["IGNORE_EMAIL"], Literal["REQUEST_ATTACHMENTS"], Literal["HUMAN_REVIEW_REQUIRED"]]] = None
    confidenceScore: Optional[float] = None
    urgencyLevel: Optional[types.UrgencyLevel] = None
    canadianJurisdiction: Optional[types.CanadianProvince] = None
    attachmentAnalysis: Optional["AttachmentAnalysis"] = None
    claimIndicators: Optional["ClaimIndicators"] = None
    riskAssessment: Optional["RiskAssessment"] = None
    classificationReasoning: Optional[str] = None
    recommendedNextSteps: List[str]
    flagsForHumanReview: List[str]
    processingNotes: List[str]
    canadianLegalConsiderations: List[str]
