# =============================================================================
# ZURICH WORKFLOW DOCKER IGNORE
# =============================================================================
# This file excludes files and directories from Docker build context
# to reduce build time and image size

# =============================================================================
# VERSION CONTROL
# =============================================================================
.git
.gitignore
.gitattributes
.gitmodules

# =============================================================================
# DOCUMENTATION
# =============================================================================
README.md
CHANGELOG.md
LICENSE
*.md
docs/
documentation/

# =============================================================================
# DEVELOPMENT FILES
# =============================================================================
.env
.env.*
.env.example
.env.local
.env.development
.env.production
.env.staging

# Development scripts
deploy.sh
cleanup.sh
local_test.py
debug.py
scratch.py
test_*.py

# =============================================================================
# PYTHON CACHE AND BUILD
# =============================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
.venv/
.env/
ENV/
env.bak/
venv.bak/

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/
nosetests.xml

# =============================================================================
# NODE.JS / DASHBOARD BUILD ARTIFACTS
# =============================================================================
zurich-dashboard/node_modules/
zurich-dashboard/npm-debug.log*
zurich-dashboard/yarn-debug.log*
zurich-dashboard/yarn-error.log*
zurich-dashboard/.npm
zurich-dashboard/.yarn-integrity
zurich-dashboard/coverage/
zurich-dashboard/.nyc_output

# Frontend build artifacts
frontend/**/node_modules/
frontend/**/dist/
frontend/**/build/
frontend/**/.cache/
frontend/**/npm-debug.log*
frontend/**/yarn-debug.log*

# =============================================================================
# BAML GENERATED FILES
# =============================================================================
# Include source BAML files but exclude generated client
baml_models/baml_client/
baml_models/.baml/
*.baml.log

# =============================================================================
# LOGS AND TEMPORARY FILES
# =============================================================================
*.log
logs/
.log
tmp/
temp/
.tmp/
.temp/
*.bak
*.backup
*.old

# =============================================================================
# IDE AND EDITOR FILES
# =============================================================================
.vscode/
.idea/
*.swp
*.swo
*~
*.sublime-project
*.sublime-workspace
.DS_Store

# =============================================================================
# SERVERLESS AND DEPLOYMENT
# =============================================================================
.serverless/
.serverless_plugins/
serverless.yml
aws-exports.js
amplify-build-config.json
.aws-sam/

# =============================================================================
# DOCKER FILES
# =============================================================================
# Exclude other Dockerfiles to avoid confusion
Dockerfile.*
docker-compose.yml
docker-compose.*.yml

# =============================================================================
# SECURITY FILES
# =============================================================================
**/secrets.json
**/credentials.json
**/*_key.json
**/*_secret.json
**/api_keys.txt
*.pem
*.key
*.crt
*.cert

# =============================================================================
# ARCHIVES AND BINARIES
# =============================================================================
*.zip
*.tar.gz
*.tgz
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar

# =============================================================================
# DATABASE FILES
# =============================================================================
*.db
*.sqlite
*.sqlite3

# =============================================================================
# MISC
# =============================================================================
.dockerignore
*.thumb
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
