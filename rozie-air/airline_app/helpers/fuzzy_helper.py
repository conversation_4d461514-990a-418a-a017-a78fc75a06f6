from rapidfuzz import fuzz

def fuzzy_match_str(str1, str2, threshold=80):
    """
    Compare two strings using fuzzy matching and return a similarity score.
    Returns True if the score meets or exceeds the threshold, otherwise False.
    
    :param str1: First string
    :param str2: Second string
    :param threshold: Similarity threshold (default is 80)
    :return: Tuple (similarity_score, match_result)
    """
    similarity_score = fuzz.ratio(str1, str2)
    return similarity_score, similarity_score >= threshold