import random
import string
from datetime import datetime
from zoneinfo import ZoneInfo

from airline_app.helpers.dynamo_helper import put_record_into_dynamodb,query_dynamodb_table_item_gsi, get_item_by_primary_key
from airline_app.common.constants import (
    FLIGHT_TABLE,
    PNR_TABLE,
    PNR_TABLE_FLIGHT_INDEX,
    CASE_MANAGEMENT_TABLE
)
from airline_app.helpers.time_helper import get_rounded_relative_time
from airline_app.helpers.logging_helper import print_error_log,print_info_log

def create_case(case_details):
    try:
        date_part = datetime.now().strftime("%y%m%d")
        random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        case_id = f"C{date_part}{random_part}"
        creation_time = datetime.now(ZoneInfo("UTC")).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        print_info_log("", "case_details", case_details.dict())
        
        case_details_dict = case_details.dict(exclude={"customer_id"})
        case_details_dict["creation_time"] = creation_time
        record = {
            "case_id": case_id,
            "customer_id": case_details.customer_id,  
            "case_details": case_details_dict
        }
        print_info_log("", "record", record)
        put_record_into_dynamodb(CASE_MANAGEMENT_TABLE, record)
        
        print_info_log("INFO", "create_case", f"Case created with ID: {case_id}")
        return {
            "status": "Success",
            "message": f"Successfully created case with ID: {case_id}",
            "response": record
        }
    except Exception as e:
        print_error_log("create_case", f"Error creating case: {str(e)}")
        return {
            "status": "Error",
            "message": "Failed to create case"
        }
    
def get_case_by_customer_or_case_id(id_input, input_type):
    try:
        case_details = []
        id_type = ""
        if input_type == "customer_id":
            id_type = "customer_id"
            case_details = query_dynamodb_table_item_gsi(CASE_MANAGEMENT_TABLE, "customerIdRetrieve", {"customer_id": id_input})
        elif input_type == "case_id":
            id_type = "case_id"
            case_details = get_item_by_primary_key(CASE_MANAGEMENT_TABLE, "case_id", id_input)
        
        if not case_details:
            print_info_log("INFO", "get_case_details", f"No case found with {id_type}: {id_input}")
            return {
                "status": "Error",
                "message": f"No case found with {id_type } : {id_input}",
                "response":[]
            }

        print_info_log("INFO", "get_case_details", f"Case details retrieved for {id_type}:{id_input}")
        return {
            "status": "Success",
            "message": f"Successfully retrieved case details for {id_type} : {id_input}",
            "response": case_details
        }
    except Exception as e:
        print_error_log("get_case_details", f"Error retrieving case details: {str(e)}")
        return {
            "status": "Error",
            "message": "Failed to retrieve case details"
        }