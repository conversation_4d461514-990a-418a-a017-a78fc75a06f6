"""
Module for handling flight details CRUD operations
"""

import json
import os
from helpers.logging_helper import get_log_level, set_logging_level, print_info_log
from helpers.decorator_helper import exception_handler
from helpers.dynamo_helper import (
    get_item_by_primary_key,
    put_table_item,
    scan_table_with_filter,
    upsert_item,
    delete_item,
)
from helpers.response_helper import format_response


@exception_handler
def lambda_handler(event, context):
    """
    Lambda handler for flight details crud operation APIs
    """
    set_logging_level(get_log_level())
    print_info_log("", "Flight Details api invoked", event)
    http_method = event.get("httpMethod", "")
    path = event.get("path", "")

    if http_method == "GET" and path == "/flights":
        return get_flight(event)
    if http_method == "POST" and path == "/flights":
        return create_flight(event)
    if http_method == "PUT" and path == "/flights":
        return update_flight(event)
    if http_method == "DELETE" and path == "/flights":
        return delete_flight(event)
    return format_response(400, None, "Invalid request")


def create_flight(event):
    """
    Create a new flight details record in the database
    """
    flight_data = json.loads(event.get("body", "{}"))
    flight_id = flight_data.get("flight_id")
    seat_type_mapping = flight_data.get("seat_type_mapping", {})
    flight_details = {
        "flight_id": flight_data.get("flight_id"),
        "aircraftName": flight_data.get("aircraft"),
        "airlineName": flight_data.get("airline"),
        "arrivalAirport": flight_data.get("arrival"),
        "arrivalAirportTerminal": flight_data.get("arrivalTerminal"),
        "departureAirport": flight_data.get("destination"),
        "departureAirportTerminal": flight_data.get("destinationTerminal"),
        "flightDuration": flight_data.get("flight_duration"),
        "baggageConfig": flight_data.get("baggage_config"),
        "scheduleConfig": flight_data.get("schedule_config"),
        "seatTypeMap": seat_type_mapping,
    }
    price_details = flight_data.get("price", {})
    flight_details["basePrice"] = price_details.get("amount")
    seat_details = flight_data.get("seats", {})
    setting_config = {}
    for classType, available_seats in seat_details.items():
        for seat in available_seats:
            seat_colum = seat[0]
            seat_row = seat[1:]
            if seat_row not in setting_config:
                setting_config[seat_row] = {}

            setting_config[seat_row][seat_colum] = {
                "seatClass": classType,
                "seatType": seat_type_mapping.get(seat_colum),
                "status": "available",
            }
    flight_details["settingConfig"] = setting_config

    status, _ = put_table_item(
        os.environ.get("FLIGHT_DETAILS_TABLE"), flight_details, "flight_id"
    )
    if status == "Update":
        return format_response(400, None, "Unique Flight ID is required")
    elif status == "Error":
        return format_response(500, None, "Encountered Technical Issue")
    else:
        return format_response(
            200, None, f"Flight created successfully, Flight_id: {flight_id}"
        )


def get_flight(event):
    """
    Get flight details by flight ID
    """
    query_params = event.get("queryStringParameters", {})
    if not query_params or not query_params.get("flight_id"):
        flight_data = scan_table_with_filter(
            os.environ.get("FLIGHT_DETAILS_TABLE"), None, None
        )
        if query_params:
            filtered_data = [
                flight
                for flight in flight_data
                if (
                    not query_params.get("arrival")
                    or flight["arrival"] == query_params["arrival"]
                )
                and (
                    not query_params.get("destination")
                    or flight["destination"] == query_params["destination"]
                )
                and (
                    not query_params.get("airline")
                    or flight["airline"] == query_params["airline"]
                )
                and (
                    not query_params.get("departure_time")
                    or flight["departure_time"] >= query_params["departure_time"]
                )
                and (
                    not query_params.get("arrival_time")
                    or flight["arrival_time"] >= query_params["arrival_time"]
                )
            ]
            flight_data = filtered_data
    else:
        flight_data = [
            get_item_by_primary_key(
                os.environ.get("FLIGHT_DETAILS_TABLE"),
                "flight_id",
                query_params.get("flight_id"),
            )
        ]
    if flight_data:
        return format_response(200, flight_data)
    return format_response(404, None, "Flight not found")


def update_flight(event):
    """
    Update flight details by flight ID
    """
    flight_data = json.loads(event.get("body", "{}"))
    flight_id = flight_data.get("flight_id")
    details = get_item_by_primary_key(
        os.environ.get("FLIGHT_DETAILS_TABLE"), "flight_id", flight_id
    )

    seat_type_mapping = (
        flight_data.get("seat_type_mapping")
        if flight_data.get("seat_type_mapping")
        else details.get("seatTypeMap")
    )
    flight_details = {
        "flight_id": (
            flight_data.get("flight_id")
            if flight_data.get("flight_id")
            else details.get("flight_id")
        ),
        "aircraftName": (
            flight_data.get("aircraft")
            if flight_data.get("aircraft")
            else details.get("aircraftName")
        ),
        "airlineName": (
            flight_data.get("airline")
            if flight_data.get("airline")
            else details.get("airlineName")
        ),
        "arrivalAirport": (
            flight_data.get("arrival")
            if flight_data.get("arrival")
            else details.get("arrivalAirport")
        ),
        "arrivalAirportTerminal": (
            flight_data.get("arrivalTerminal")
            if flight_data.get("arrivalTerminal")
            else details.get("arrivalAirportTerminal")
        ),
        "departureAirport": (
            flight_data.get("destination")
            if flight_data.get("destination")
            else details.get("departureAirport")
        ),
        "departureAirportTerminal": (
            flight_data.get("destinationTerminal")
            if flight_data.get("destinationTerminal")
            else details.get("departureAirportTerminal")
        ),
        "flightDuration": (
            flight_data.get("flight_duration")
            if flight_data.get("flight_duration")
            else details.get("flightDuration")
        ),
        "scheduleConfig": (
            flight_data.get("schedule_config")
            if flight_data.get("schedule_config")
            else details.get("scheduleConfig")
        ),
        "baggageConfig": (
            flight_data.get("baggage_config")
            if flight_data.get("baggage_config")
            else details.get("baggageConfig")
        ),
    }
    price_details = flight_data.get("price", {})
    if price_details:
        flight_details["basePrice"] = price_details.get("amount")
    seat_details = flight_data.get("seats", {})
    if seat_details:
        setting_config = {}
        for classType, available_seats in seat_details.items():
            for seat in available_seats:
                seat_colum = seat[0]
                seat_row = seat[1:]
                if seat_row not in setting_config:
                    setting_config[seat_row] = {}

                setting_config[seat_row][seat_colum] = {
                    "seatClass": classType,
                    "seatType": seat_type_mapping.get(seat_colum),
                    "status": "available",
                }
        flight_details["settingConfig"] = setting_config

    status = upsert_item(
        os.environ.get("FLIGHT_DETAILS_TABLE"), flight_details, "flight_id"
    )
    if status == "Error":
        return format_response(500, None, "Encountered Technical Issue")
    else:
        return format_response(200, None, "Flight Details Updated Successfully")


def delete_flight(event):
    """
    Delete flight details by flight ID
    """
    query_params = event.get("queryStringParameters", {})
    if not query_params:
        return format_response(400, None, "Provide flight id to delete.")
    status = delete_item(
        os.environ.get("FLIGHT_DETAILS_TABLE"),
        {"flight_id": query_params.get("flight_id")},
    )
    if status == "Error":
        return format_response(500, None, "Encountered Technical Issue")
    else:
        return format_response(200, None, "Flight Details Deleted Successfully")
