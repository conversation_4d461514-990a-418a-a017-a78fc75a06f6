# Zurich Dashboard Environment Variables

# Application Settings
NODE_ENV=production
PORT=2000

# Zendesk API Configuration
ZENDESK_SUBDOMAIN=d3v-rozieai5417
ZENDESK_EMAIL=<EMAIL>
ZENDESK_TOKEN=your_zendesk_api_token_here

# Database Configuration (Optional)
POSTGRES_DB=zurich_dashboard
POSTGRES_USER=zurich
POSTGRES_PASSWORD=zurich_secure_password

# Redis Configuration (Optional)
REDIS_URL=redis://redis:6379

# Security Settings
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS Settings
CORS_ORIGIN=http://localhost:2000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
