const axios = require("axios")

/**
 * Helper function to make a POST request using Axios.
 * 
 * @param {string} endpoint - The URL endpoint to which the POST request will be made.
 * @param {Object} requestBody - The request body to be sent with the POST request.
 * @param {Object} headers - Optional headers to be included in the request.
 * @returns {Promise<Object>} A Promise that resolves to the data returned from the POST request.
 * @throws {Error} If the POST request fails.
 */
async function axiosPostRequest(endpoint, requestBody, headers = {}) {
	console.log("axiosPostRequest endpoint:", endpoint)
	console.log("axiosPostRequest requestBody:", requestBody)
	console.log("axiosPostRequest headers:", headers)

	try {
		const response = await axios.post(endpoint, JSON.stringify(requestBody), { headers })
		console.log("axios Response:", response.data)
		return response.data
	} catch (error) {
		if (error.response && error.response.status === 404) {
			console.error("Error 404: Resource not found at the given endpoint:", endpoint)
			return {
				status: 404,
				message: "Not Found: The requested resource was not found at the given endpoint."
			}
		} else {
			console.error("Error:", error.message)
			throw error
		}
	}
}

/**
 * Helper function to make a GET request using Axios.
 * 
 * @param {string} endpoint - The URL endpoint to which the GET request will be made.
 * @param {Object} queryParams - Optional query parameters to be included in the request.
 * @param {Object} headers - Optional headers to be included in the request.
 * @returns {Promise<Object>} A Promise that resolves to the data returned from the GET request.
 * @throws {Error} If the GET request fails.
 */
async function axiosGetRequest(endpoint, queryParams = {}, headers = {}) {
	console.log("axiosGetRequest endpoint:", endpoint)
	console.log("axiosGetRequest queryParams:", queryParams)
	console.log("axiosGetRequest headers:", headers)

	try {
		const response = await axios.get(endpoint, { params: queryParams, headers })
		console.log("axios Response:", response.data)
		return response.data
	} catch (error) {
		console.error("Error:", error)
		throw error
	}
}

module.exports = {
	axiosPostRequest,
	axiosGetRequest
}
