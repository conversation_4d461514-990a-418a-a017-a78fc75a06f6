"""
This module contains DynamoDB helper functions used in the lambda functions.

Functions:
 - put_record_into_dynamodb
 - get_item_by_primary_key
 - update_ddb_table_item
"""

import boto3
from boto3.dynamodb.conditions import Attr
from botocore.exceptions import ClientError
from .logging_helper import print_info_log, print_error_log

dynamodb = boto3.resource("dynamodb")


def put_record_into_dynamodb(table_name, record):
    """
    puts item in table
    """
    # Get the DynamoDB table
    table = dynamodb.Table(table_name)
    # Put record into DynamoDB table
    try:
        response = table.put_item(Item=record)
        print_info_log("", "Record added successfully:%s ", response)
    except Exception as e:
        print_error_log("", e)


def get_item(table, primary_key_id, primary_key_value, sort_key_id=None, sort_key_value=None):
    """
    Gets an item from the specified DynamoDB table.

    Args:
        table (string) : table name
        primary_key_id (string): The primary key attribute name of the item to get from the table.
        primary_key_value (string): The primary key value of the item to get from the table.

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    table = dynamodb.Table(table)
    try:
        key = {primary_key_id: primary_key_value}
        if sort_key_id and sort_key_value:
            key[sort_key_id] = sort_key_value
        response = table.get_item(Key=key)
        return response.get("Item", {})
    except Exception as error:
        print_error_log("", error)
        raise error


def update_ddb_table_item(table_name, key_dict, item_dict):
    """
    update a specific item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        key_dict (dict): key dictionary
        item_dict (dict): dictionary of attribute and value to update

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)
        for _key in key_dict:
            del item_dict[_key]

        update_expression = ",".join(
            list(map(lambda key: f"{key} = :{key}", list(item_dict.keys())))
        )  # converts keys in 'key = :key' format
        expression_attribute_values = {}
        for key in item_dict:
            expression_attribute_values[f":{key}"] = item_dict[key]
        param = {
            "Key": key_dict,
            "UpdateExpression": f"SET {update_expression}",
            "ExpressionAttributeValues": expression_attribute_values,
        }
        table.update_item(**param)
        return "Success"
    except Exception as exception:
        print_error_log("", "Error while putting item from table")
        print_error_log("", exception)
        return "Error"


def put_table_item(table_name, item_dict, primary_key, sort_key=None):
    """
    Gets an item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        item_dict (dict): dictionary of attribute and value to update
        primary_key: "primary key to check if item exists"
        sort_key: "sort key to check if item exists"

    Returns:
        String : A string indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)
        condition_expression = f"attribute_not_exists({primary_key})"

        if sort_key:
            condition_expression = f"attribute_not_exists({primary_key}) and attribute_not_exists({sort_key})"
        param = {"Item": item_dict, "ConditionExpression": condition_expression}
        table.put_item(**param)
        return "Success", {}
    except ClientError as exception:
        if exception.response["Error"]["Code"] == "ConditionalCheckFailedException":
            key_dict = {primary_key: item_dict[primary_key]}
            if sort_key:
                key_dict[sort_key] = item_dict[sort_key]
            print_error_log("", "key found in table")
            return "Update", key_dict
        print_error_log("", "Error while putting in table")
        return "Error", {}


def upsert_item(table_name, item_dict, primary_key, sort_key=None):
    """
    Updated item is already exists and creates if not.

    Args:
        table_name (string): The DynamoDB table name.
        item_dict (dict): dictionary of attribute and value to update
        primary_key: "primary key to check if item exists"
        sort_key: "sort key to check if item exists"

    Returns:
        String : A string indicating the success or failure of the operation.
    """

    status, key_dict = put_table_item(table_name, item_dict, primary_key, sort_key)

    if status == "Update":
        status = update_ddb_table_item(table_name, key_dict, item_dict)

    return status


def scan_table_with_filter(
    table_name, filter_expression_dict=None, projection_expression_list=None
):
    """
    Scan the DynamoDB table with optional filter and projection expressions.

    Args:
        table (string) : table name
        filter_expression_dict (dict): A dictionary of key-value pairs to filter the scan results.
        projection_expression_list (list): A list of attribute names to include in the scan results.

    Returns:
        list: A list of items from the DynamoDB table that match the filter conditions and
      include only the specified attributes.
    """
    try:
        table = dynamodb.Table(table_name)
        params = {}
        filter_expression = None
        projection_expression = None
        expression_attribute_names = None
        if filter_expression_dict:
            for key, value in filter_expression_dict.items():
                if isinstance(value, list):
                    attr_condition = None
                    for v in value:
                        condition = Attr(key).eq(v)
                        if attr_condition is None:
                            attr_condition = condition
                        else:
                            attr_condition = attr_condition | condition
                else:
                    attr_condition = Attr(key).eq(value)
                if filter_expression is None:
                    filter_expression = attr_condition
                else:
                    filter_expression = filter_expression & attr_condition
            params["FilterExpression"] = filter_expression

        if projection_expression_list:
            projection_expression = ", ".join(
                f"#{attr}" for attr in projection_expression_list
            )
            params["ProjectionExpression"] = projection_expression
            expression_attribute_names = {
                f"#{attr}": attr for attr in projection_expression_list
            }
            params["ExpressionAttributeNames"] = expression_attribute_names

        items = []
        while True:
            response = table.scan(**params)
            items.extend(response.get("Items", []))
            last_evaluated_key = response.get("LastEvaluatedKey")
            if not last_evaluated_key:
                break
            params["ExclusiveStartKey"] = last_evaluated_key

        return response.get("Items", [])
    except Exception as error:
        print_error_log("", error)
        raise error


def delete_item(table_name, key_dict):
    """
    Delete an item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        key_dict (dict): A dictionary containing the primary key(s) of the item to delete.

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)
        table.delete_item(Key=key_dict)
        return "Success"
    except Exception as error:
        print_error_log("", error)
        return "Error"
