import traceback
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from airline_app.adapters.auth_adapter import get_api_key


from airline_app.helpers.logging_helper import print_info_log
from airline_app.services.customer import get_customer_id, get_customer_details_by_id

customer_router = APIRouter(prefix="/customer")

@customer_router.get(
    "/get_customer_id",
    dependencies=[Depends(get_api_key)]
)
def get_customer_ids(phone_number: str = None, last_name: str = None) -> list:
    try:
        pnr_data = get_customer_id(phone_number, last_name)
        if not pnr_data:
            raise HTTPException(status_code=404, detail="Customer ID not found for given credentials")
        return pnr_data
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )
    
@customer_router.get(
    "/get_customer_details_by_id",
    dependencies=[Depends(get_api_key)]
)
def get_customer_details_by_id_api(customer_id: str) -> dict:
    try:
        customer_detail = get_customer_details_by_id(customer_id)
        if not customer_detail:
            raise HTTPException(status_code=404, detail="Customer ID not found for given credentials")
        return customer_detail
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail={"status": "Error occurred", "error": str(e)}
        )