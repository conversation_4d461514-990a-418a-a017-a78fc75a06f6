const { queryDynamodbTableItemGSI, updateDynamoTableDataWithParams } = require("../helpers/dynamo-helper.js")

/**
 * @function updatePNRStatus
 * @description This function queries the PNR table by flight_schedule_id and updates the pnr_status for all matching PNRs.
 * 
 * @param {string} scheduleId - The flight schedule ID to search for in the PNR table.
 * @param {string} status - The new status to set for the PNR.
 * 
 * @returns {Promise<void>} - Returns nothing on success, throws an error if any issue occurs.
 */
const updatePNRStatus = async (scheduleId, status) => {
	try {
		console.log(`Updating PNRs for flight_schedule_id: ${scheduleId} with status: ${status}`)
        
		// Query the PNR table using the flight_schedule_id
		if (scheduleId) {
			const params = {
				TableName: process.env.PNR_TABLE_NAME,
				IndexName: "FlightScheduleIndex", 
				KeyConditionExpression: "flight_schedule_id = :flight_schedule_id",
				ExpressionAttributeValues: {
					":flight_schedule_id": scheduleId
				},
				ScanIndexForward: false
			}

			const pnrItems = await queryDynamodbTableItemGSI(params)
			console.log("PNR items retrieved for scheduleId:", pnrItems)

			// If PNRs are found, update their status
			if (pnrItems && pnrItems.length > 0) {
				await Promise.all(pnrItems.map(async pnr => {
					if (pnr.pnr_status !== status) {
						const updateParams = {
							TableName: process.env.PNR_TABLE_NAME,
							Key: { pnr_id: pnr.pnr_id }, // Use the primary key (pnr_id) to update
							UpdateExpression: "set pnr_status = :pnr_status",
							ExpressionAttributeValues: {
								":pnr_status": status,
	
							},
							ReturnValues: "UPDATED_NEW"
						}

						console.log("Updating PNR status:", updateParams)
						await updateDynamoTableDataWithParams(updateParams)
					}
				}))
				console.log("PNR statuses updated successfully.")
			} else {
				console.log("No PNRs found for the provided flight_schedule_id.")
			}
		}
	} catch (error) {
		console.error("Error in updatePNRStatus:", error)
		throw new Error("Failed to update PNR status.")
	}
}

module.exports = {
	updatePNRStatus
}
