import datetime as dt
import uuid

from fastapi import APIRouter, Response

from middleware_app.common.constants import ALLOWED_ORIGINS
from middleware_app.common.schemas import SuccessResponse

router = APIRouter()


@router.get("")
async def readiness(response: Response) -> SuccessResponse:
    response.headers["Access-Control-Allow-Origin"] = ALLOWED_ORIGINS
    return SuccessResponse(
        id=str(uuid.uuid4()),
        object="Scc.AutoGen.Health",
        created=int(dt.datetime.now().timestamp()),
        message="Service is ready to serve requests.",
    )
