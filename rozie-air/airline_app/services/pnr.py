from airline_app.helpers.dynamo_helper import (
    query_dynamodb_table_item_gsi,
    get_item_by_primary_key,
    scan_table_with_filter,
    update_ddb_table_item,
)
from airline_app.common.constants import (
    PNR_TABLE,
    PNR_TABLE_CUSTOMER_INDEX,
    CUSTOMER_TABLE,
    FLIGHT_TABLE,
    BCC_EMAIL
)
from airline_app.helpers.time_helper import get_rounded_relative_time
from airline_app.helpers.airline_code_helper import get_airline_name
from airline_app.helpers.fuzzy_helper import fuzzy_match_str
from airline_app.helpers.sendgrid_helper import send_email
from airline_app.services.flight import get_flight_status
from airline_app.services.customer import get_customer_id, get_customer_details_by_id


def get_active_pnr(customer_id):
    customer_details = get_item_by_primary_key(
        CUSTOMER_TABLE, "customer_id", customer_id
    )
    if customer_details:
        _active_pnrs = query_dynamodb_table_item_gsi(
            PNR_TABLE, PNR_TABLE_CUSTOMER_INDEX, {"customer_id": customer_id}
        )
        active_pnrs_details = {
            "customer_details": {
                "customer_id": customer_details["customer_id"],
                "first_name": customer_details["first_name"],
                "last_name": customer_details["last_name"],
                "email": customer_details["email"],
                "phone_number": customer_details["phone_number"],
            },
            "active_pnrs": [],
        }

        for pnr_details in _active_pnrs:
            flight_details = get_item_by_primary_key(
                FLIGHT_TABLE, "flight_id", pnr_details["flight_id"]
            )
            if flight_details:
                item = {
                    "pnr": pnr_details["pnr_id"],
                }
                booking_date = get_rounded_relative_time(
                    int(pnr_details["booking_date"])
                )
                if flight_details["departure"]<=0:
                    continue
                boarding_time = get_rounded_relative_time(int(flight_details["departure"]))
                item["booking_date"] = booking_date.strftime("%Y-%m-%d")
                item["flight_details"] = {
                    "flight_number": flight_details["flight_number"],
                    "airline": flight_details["airline"],
                    "departure_airport": get_airline_name(
                        flight_details["departure_airport"]
                    ),
                    "arrival_airport": get_airline_name(
                        flight_details["arrival_airport"]
                    ),
                    "date": boarding_time.strftime("%a, %b %d"),
                    "time": boarding_time.strftime("%H:%M")
                }
                item["passengers"] = [
                    {
                        "first_name": passenger["first_name"],
                        "last_name": passenger["last_name"],
                        "seat": passenger["seat"],
                        "passenger_class": passenger["passenger_class"],
                        "checked_in": passenger["checked_in"],
                        "additional_services": passenger.get("additional_services", []),
                    }
                    for passenger in pnr_details.get("passengers", [])
                ]
                active_pnrs_details["active_pnrs"].append(item)

        return active_pnrs_details
    return None


def get_pnr_details_by_pnr_id(pnr_id):
    pnr_details = get_item_by_primary_key(PNR_TABLE, "pnr_id", pnr_id)
    if pnr_details:
        flight_details = get_item_by_primary_key(
            FLIGHT_TABLE, "flight_id", pnr_details["flight_id"]
        )
        if flight_details:
            price_map = {}
            for seat in flight_details["seats"]:
                price_map[seat["seat_number"]] = seat["price"]
            item = {
                "pnr": pnr_details["pnr_id"],
                "payment_status": pnr_details["payment_status"],
                "customer_id": pnr_details["customer_id"]
            }
            boarding_time = get_rounded_relative_time(int(flight_details["departure"]))
            booking_date = get_rounded_relative_time(int(pnr_details["booking_date"]))
            item["booking_date"] = booking_date.strftime("%Y-%m-%d")
            item["flight_details"] = {
                "flight_number": flight_details["flight_number"],
                "airline": flight_details["airline"],
                "departure_airport": get_airline_name(
                    flight_details["departure_airport"]
                ),
                "arrival_airport": get_airline_name(flight_details["arrival_airport"]),
                "date": boarding_time.strftime("%a, %b %d"),
                "time": boarding_time.strftime("%H:%M")
            }
            item["passengers"] = [
                {
                    "first_name": passenger["first_name"],
                    "last_name": passenger["last_name"],
                    "seat": passenger["seat"],
                    "passenger_class": passenger["passenger_class"],
                    "checked_in": passenger["checked_in"],
                    "additional_services": passenger.get("additional_services", []),
                    "price": int(price_map.get(passenger["seat"])),
                }
                for passenger in pnr_details.get("passengers", [])
            ]
            return [item]
    return {"status": "PNR not found"}


def get_pnr_details_by_customer_id(customer_id):
    pnr_details = query_dynamodb_table_item_gsi(
        PNR_TABLE, PNR_TABLE_CUSTOMER_INDEX, {"customer_id": customer_id}
    )
    result = []
    if pnr_details:
        for pnr_detail in pnr_details:
            flight_details = get_item_by_primary_key(
                FLIGHT_TABLE, "flight_id", pnr_detail["flight_id"]
            )
            if flight_details:
                price_map = {}
                for seat in flight_details["seats"]:
                    price_map[seat["seat_number"]] = seat["price"]
                item = {
                    "pnr": pnr_detail["pnr_id"],
                    "payment_status": pnr_detail["payment_status"],
                }
                boarding_time = get_rounded_relative_time(int(flight_details["departure"]))
                booking_date = get_rounded_relative_time(
                    int(pnr_detail["booking_date"])
                )
                item["booking_date"] = booking_date.strftime("%Y-%m-%d")
                item["flight_details"] = {
                    "flight_number": flight_details["flight_number"],
                    "airline": flight_details["airline"],
                    "departure_airport": get_airline_name(
                        flight_details["departure_airport"]
                    ),
                    "arrival_airport": get_airline_name(
                        flight_details["arrival_airport"]
                    ),
                    "date": boarding_time.strftime("%a, %b %d"),
                    "time": boarding_time.strftime("%H:%M")
                }
                item["passengers"] = [
                    {
                        "first_name": passenger["first_name"],
                        "last_name": passenger["last_name"],
                        "seat": passenger["seat"],
                        "passenger_class": passenger["passenger_class"],
                        "checked_in": passenger["checked_in"],
                        "additional_services": passenger.get("additional_services", []),
                        "price": int(price_map.get(passenger["seat"])),
                    }
                    for passenger in pnr_detail.get("passengers", [])
                ]
            result.append(item)
        return result
    return {"status": "PNR not found"}


def update_pnr_details(pnr_id, pnr_details):
    try:
        key_dict = {"pnr_id": pnr_id}
        item_dict = {"pnr_id": pnr_id}
        if "alternative_email" in pnr_details:
            item_dict["alternative_email"] = pnr_details["alternative_email"]
        item_dict["passengers"] = [
            {
                "first_name": passenger["first_name"],
                "last_name": passenger["last_name"],
                "seat": passenger["seat"],
                "passenger_class": passenger["passenger_class"],
                "checked_in": passenger["checked_in"],
                "additional_services": passenger.get("additional_services", []),
            }
            for passenger in pnr_details.get("passengers", [])
        ]
        print(PNR_TABLE, key_dict, item_dict)
        update_ddb_table_item(PNR_TABLE, key_dict, item_dict)
        return True
    except Exception as e:
        print("error ", str(e))
        return False


def get_pnr_details(pnr_id, last_name):
    pnr_details = get_item_by_primary_key(PNR_TABLE, "pnr_id", pnr_id)
    customer_ids = get_customer_id(last_name=last_name)
    if customer_ids:
        customer_ids = [customer["customer_id"] for customer in customer_ids]
    else:
        customer_ids = []
    print("customer_ids", customer_ids)
    if pnr_details:
        print("pnr_details", pnr_details)

        if pnr_details["customer_id"] not in customer_ids:
            return {
                "status": "Last name and PNR ID do not match",
                "suggested_action": "Asks user to verify the captured details (PNR id and Customer Last name) and correct if something wrongly captured.",
            }
        flight_details = get_item_by_primary_key(
            FLIGHT_TABLE, "flight_id", pnr_details["flight_id"]
        )
        if flight_details:
            price_map = {}
            for seat in flight_details["seats"]:
                price_map[seat["seat_number"]] = seat["price"]
            item = {
                "pnr": pnr_details["pnr_id"],
                "payment_status": pnr_details["payment_status"],
            }
            boarding_time = get_rounded_relative_time(int(flight_details["departure"]))
            booking_date = get_rounded_relative_time(int(pnr_details["booking_date"]))
            item["booking_date"] = booking_date.strftime("%Y-%m-%d")
            item["flight_details"] = {
                "flight_number": flight_details["flight_number"],
                "airline": flight_details["airline"],
                "departure_airport": get_airline_name(
                    flight_details["departure_airport"]
                ),
                "arrival_airport": get_airline_name(flight_details["arrival_airport"]),
                "date": boarding_time.strftime("%a, %b %d"),
                "time": boarding_time.strftime("%H:%M")
            }
            item["passengers"] = [
                {
                    "first_name": passenger["first_name"],
                    "last_name": passenger["last_name"],
                    "seat": passenger["seat"],
                    "passenger_class": passenger["passenger_class"],
                    "checked_in": passenger["checked_in"],
                    "additional_services": passenger.get("additional_services", []),
                    "price": int(price_map.get(passenger["seat"])),
                }
                for passenger in pnr_details.get("passengers", [])
            ]
            return [item]
    return {"status": "PNR not found"}


def get_flight_id_for_pnr(pnr_id, last_name):
    pnr_details = get_item_by_primary_key(PNR_TABLE, "pnr_id", pnr_id)
    customer_ids = get_customer_id(last_name=last_name)
    if customer_ids:
        customer_ids = [customer["customer_id"] for customer in customer_ids]
    else:
        customer_ids = []
    print("customer_ids", customer_ids)
    if pnr_details:
        print("pnr_details", pnr_details)

        if pnr_details["customer_id"] not in customer_ids:
            return {
                "status": "Last name and PNR ID do not match",
                "suggested_action": "Asks user to verify the captured details (PNR id and Customer Last name) and correct if something wrongly captured.",
            }
        flight_details = get_item_by_primary_key(
            FLIGHT_TABLE, "flight_id", pnr_details["flight_id"]
        )
        if flight_details:
            item = {
                "pnr": pnr_details["pnr_id"],
                "payment_status": pnr_details["payment_status"],
            }
            booking_date = get_rounded_relative_time(int(pnr_details["booking_date"]))
            item["booking_date"] = booking_date.strftime("%Y-%m-%d")
            item["flight_details"] = {
                "flight_id": flight_details["flight_id"],
                "flight_number": flight_details["flight_number"],
                "airline": flight_details["airline"]
            }
            return [item]
    return {"status": "PNR not found"}

def update_passenger_name(pnr_id, new_name):
    pnr_details = get_pnr_details_by_pnr_id(pnr_id)[0]
    old_name = pnr_details["passengers"][0]["first_name"]
    score, match_flag = fuzzy_match_str(old_name, new_name, 60)
    print("Fuzzy Match", score)
    if match_flag:
        pnr_details["passengers"][0]["first_name"] = new_name
        update_pnr_details(pnr_id, pnr_details)
        customer_id = pnr_details["customer_id"]
        customer_details = get_customer_details_by_id(customer_id)
        body = f"""
        <html>
        <body>
            <h1>Flight Ticket</h1>
            <p>Dear {customer_details['first_name']} {customer_details['last_name']},</p>
            <p>Your ticket details are as follows:</p>
                {pnr_details}
            <p>Enjoy your flight!</p>
        </body>
        </html>
        """
        bcc_list = BCC_EMAIL
        if customer_details["email"] in bcc_list:
            bcc_list.remove(customer_details["email"])
        email_config = {
            "reference_id": pnr_id+customer_id,
            "from": "Nina<<EMAIL>>",
            "to": customer_details["email"],
            "bcc": bcc_list,
            "subject": "Boarding Pass",
            "html_body": body,
            "send_separate": True
        }
        send_email(email_config)
        return {
            "status": "Success",
            "suggested_action": "Let customer know that name updated successfully."
        }
    return {
        "status": "Failed",
        "suggested_action": "Let customer know you can only update minor changes like typos and missing letter."
    }
