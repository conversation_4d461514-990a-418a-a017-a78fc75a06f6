import os
import json
from twilio.twiml.voice_response import Voice<PERSON><PERSON>ponse, Say
from twilio.request_validator import Re<PERSON><PERSON><PERSON><PERSON><PERSON>
from helpers.logging_helper import (
    get_log_level,
    set_logging_level,
    print_info_log,
)
from helpers.twilio_util import (
    parse_twilio_body
)
from helpers.webhooks import (
    autogen_greeting_flow,
    autogen_initiate_call,
    autogen_gather_input_flow,
    autogen_process_gathered_input_flow,
    autogen_error_handling
)
from helpers.webhooks_V2 import (
    middleware_greeting_flow,
    middleware_initiate_call,
    middleware_gather_input_flow,
    middleware_process_gathered_input_flow,
    middleware_play_prompt_flow
)

enable_logs = os.environ.get("ENABLE_LOGS", "TRUE")


def lambda_handler(event, context):
    """
    Lambda handler for email-handler
    """
    set_logging_level(get_log_level())
    print_info_log("", "Twilio webhook invoked, Event", event)
    print_info_log("", "Twilio webhook isAuthorized : ", request_authorizer(event))
    if request_authorizer(event):
        if event["resource"] == "/webhooks/greetingFlow":
            return autogen_greeting_flow(event)
        if event["resource"] == "/webhooks/entryFlow":
            return autogen_initiate_call(event)
        if event["resource"] == "/webhooks/gatherInputFlow":
            return autogen_gather_input_flow(event)
        if event["resource"] == "/webhooks/processInputFlow":
            return autogen_process_gathered_input_flow(event)
        if event["resource"] == "/webhooks/errorFlow":
            return autogen_error_handling(event)
        
        if event["resource"] == "/webhooks_v2/greetingFlow":
            return middleware_greeting_flow(event)
        if event["resource"] == "/webhooks_v2/entryFlow":
            return middleware_initiate_call(event)
        if event["resource"] == "/webhooks_v2/gatherInputFlow":
            return middleware_gather_input_flow(event)
        if event["resource"] == "/webhooks_v2/processInputFlow":
            return middleware_process_gathered_input_flow(event)
        if event["resource"] == "/webhooks_v2/playPromptFlow":
            return middleware_play_prompt_flow(event)
        
        return {"statusCode": 200, "body": json.dumps("Endpoint Not Found")}

    response = VoiceResponse()
    welcome_prompt = Say(
        "Sorry! Unauthorized Access",
        language="en-US",
        voice="Google.en-US-Neural2-F",
    )
    response.append(welcome_prompt)
    response.hangup()
    return {
        "statusCode": 401,
        "headers": {"Content-Type": "application/xml"},
        "body": str(response),
    }


def request_authorizer(event):
    """
    This function will authorize the request sent to lambda
    """
    request_validator = RequestValidator(os.environ.get("TWILIO_AUTH_TOKEN"))
    request_url = (
        "https://"
        + event.get("requestContext").get("domainName")
        + event.get("requestContext").get("path")
    )
    params = parse_twilio_body(event.get("body"))
    signature = event.get("headers").get("X-Twilio-Signature")
    print(request_url, params, signature)
    is_authorized = request_validator.validate(request_url, params, signature)
    return is_authorized

