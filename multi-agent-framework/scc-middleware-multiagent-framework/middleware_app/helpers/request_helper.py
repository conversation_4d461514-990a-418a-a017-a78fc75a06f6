"""
This helper function assist in sending http request.
"""

import requests
import json
from middleware_app.helpers.logging_helper import print_error_log


def sync_get_request(url, query_params=None, username=None, password=None):
    """
    This function used for invoking get method and wait for result.
    """
    param = {"url": url, "timeout": 30}
    if query_params:
        param["params"] = query_params
    if username and password:
        param["auth"] = (username, password)
    try:
        response = requests.get(**param)

        if response.status_code != 200:
            print(response.json())
            return "Failed", {}

        response = response.json()
        return "Success", response

    except Exception as e:
        print_error_log("", e)
        return "Failed", str(e)


def post_request(url, headers, body=None, json_body=None):
    """
    This function used for invoking get method and wait for result.
    """
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        body["request_from"]= "multi_agent_middleware"
        param["data"] = json.dumps(body)
    if json_body:
        param["json"] = json_body
    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            if response.json():
                response = response.json()
                return "Failed", {"error_message": response}
            return "Failed", {"error_message": "Technical Issue"}

        response = response.json()
        print(response, "response")
        if not response:
            response = json.dumps({
                "status": "connected"
            })
        return "Success", response

    except Exception as e:
        return "Failed", {"error_message": "test"+str(e)}
