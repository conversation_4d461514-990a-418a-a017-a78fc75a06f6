const { ApiResp } = require("../helpers/api-helper.js");
const { retrieveDynamoEntry, queryDynamodbTableItemGSI } = require("../helpers/dynamo-helper.js");

/**
 * @function getBaggage
 * @description Retrieves baggage details by either pnr_id or baggage_id.
 * 
 * @param {Object} body - The request body containing search parameters.
 * @param {string} [body.pnr_id] - The PNR ID to search by.
 * @param {string} [body.baggage_id] - The baggage ID to search by.
 * 
 * @returns {Promise<Object>} - The response object with baggage details and flight information.
 * 
 * @throws {Error} - Throws an error if there is an issue during the retrieval process.
 */
const getBaggage = async (body) => {
    try {
        console.log("function: getBaggage body:", body);
        const { pnr_id, baggage_id } = body;

        if (!pnr_id && !baggage_id) {
            return ApiResp({ status: "Error", message: "Either PNR ID or Baggage ID must be provided" }, 400);
        }

        let baggageItems = [];

        if (pnr_id) {
            const params = {
                TableName: process.env.BAGGAGE_TABLE_NAME,
                IndexName: "PNRIndex",
                KeyConditionExpression: "pnr_id = :pnr_id",
                ExpressionAttributeValues: {
                    ":pnr_id": pnr_id
                },
                ScanIndexForward: false
            }
            baggageItems = await queryDynamodbTableItemGSI(params)
        }

        if (baggage_id) {
            const baggageItem = await retrieveDynamoEntry(process.env.BAGGAGE_TABLE_NAME, "baggage_id", baggage_id);
            if (baggageItem) baggageItems.push(baggageItem);
        }

        if (baggageItems.length === 0) {
            return ApiResp({ status: "Error", message: "No baggage found" }, 404);
        }

        const flightDetailsPromises = baggageItems.map(async (baggage) => {
            const flightDetails = await retrieveDynamoEntry(process.env.FLIGHT_SCHEDULE_TABLE, "flight_schedule_id", baggage.flight_schedule_id);
            return {
                ...baggage,
                departureAirport: flightDetails.departureAirport,
                arrivalAirport: flightDetails.arrivalAirport
            };
        });

        const baggageWithFlightDetails = await Promise.all(flightDetailsPromises);

        return ApiResp({ status: "Success", baggage: baggageWithFlightDetails }, 200);
    } catch (error) {
        console.error("Error in getBaggage: ", error);
        return ApiResp({ status: "Error", message: "Internal server error while retrieving baggage" }, 500);
    }
};

module.exports = {
    getBaggage
};
