# AC CARE Analytics

## Local Development

### Prerequisites

1. Install [conda](https://www.anaconda.com/download/)
2. Install [pipx](https://pypa.github.io/pipx/)
3. Install [Poetry](https://python-poetry.org/docs/) using pipx

```console
pipx install poetry
```

### Setup

1. Create a conda environment. Modify the conda environment as needed.
   ```console
   conda create -n rozie_care_analytics_py311 python=3.11.2
   ```
2. Activate the conda environment
   ```console
   conda activate rozie_care_analytics_py311
   ```
3. Install the required packages
   ```console
   poetry install --no-root --with dev,test,lint,typing,codespell
   ```
