const { deleteDynamodbTableItem } = require("../helpers/dynamo-helper.js")
const { ApiResp } = require("../helpers/api-helper.js")

/**
 * @function deletePNR
 * @description Deletes a PNR entry from DynamoDB based on the provided PNR ID.
 * 
 * @param {Object} event - The event object containing the pnr_id.
 * @param {string} event.pnr_id - The PNR ID to be deleted.
 * 
 * @returns {Promise<Object>} - A structured API response with the status and a success or error message.
 * 
 * @throws {Error} - Throws an error if there is an issue with deleting the PNR entry.
 */
const deletePNR = async (pnr_id) => {

	try {
		console.log(`Function deletePNR initiated for PNR ID: ${pnr_id}`)
        
		await deleteDynamodbTableItem(process.env.PNR_TABLE_NAME, "pnr_id", pnr_id)
		return ApiResp({
			status: "Success",
			message: "PNR deleted successfully"
		})
		
	} catch (error) {
		console.error("Error in deletePNR:", error)
		return ApiResp({
			status: "Error",
			message: "Error while deleting PNR"
		}, 500)
	}
}

module.exports = {
	deletePNR
}
