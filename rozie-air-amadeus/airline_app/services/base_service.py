import logging
from airline_app.core import config
from airline_app.utils.token_manager import get_token_manager
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BaseApiService:
    def __init__(self):
        self.token_manager = get_token_manager()
        self.base_url = "https://test.api.amadeus.com" if config.AMADEUS_HOST == "test" else "https://api.amadeus.com"
        self.openai_client = OpenAI()

    def _get_headers(self):
        token = self.token_manager.get_valid_token()
        return {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

    def _handle_unauthorized(self):
        """Handle 401 Unauthorized response by refreshing the token."""
        logger.warning("Received 401 Unauthorized, refreshing token")
        self.token_manager.refresh_token() 