# =============================================================================
# ZURICH WORKFLOW DEPLOYMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values

# =============================================================================
# AWS CONFIGURATION
# =============================================================================
AWS_REGION=ca-central-1
AWS_ACCOUNT_ID=your-aws-account-id

# =============================================================================
# ECR REPOSITORIES
# =============================================================================
# Backend API ECR Repository
BACKEND_ECR_REPO_URI=your-account-id.dkr.ecr.ca-central-1.amazonaws.com/zurich-workflow-backend

# Dashboard ECR Repository  
DASHBOARD_ECR_REPO_URI=your-account-id.dkr.ecr.ca-central-1.amazonaws.com/zurich-workflow-dashboard

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
# Backend API Domain
backend-domain-name=api.zurich-workflow.yourdomain.com

# Dashboard Domain
dashboard-domain-name=dashboard.zurich-workflow.yourdomain.com

# Frontend Domain
frontend-domain-name=app.zurich-workflow.yourdomain.com

# Route53 Hosted Zone ID
hosted-zone-id=your-hosted-zone-id

# ACM Certificate ARN (must cover all subdomains)
acm-cert-arn=arn:aws:acm:ca-central-1:your-account-id:certificate/your-cert-id

# =============================================================================
# S3 CONFIGURATION
# =============================================================================
# Frontend S3 Bucket Name
frontend-s3-bucket=zurich-workflow-frontend-dev

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key

# BAML Configuration
BAML_ENVIRONMENT=development

# FastAPI Configuration
FASTAPI_ENV=development
LOG_LEVEL=INFO

# Node.js Configuration
NODE_ENV=production

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key

# =============================================================================
# SUPPORT SYSTEM CONFIGURATION
# =============================================================================
SUPPORT_SUBDOMAIN=d3v-rozieai5417
SUPPORT_EMAIL=<EMAIL>
SUPPORT_TOKEN=your-support-token

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# Deployment stage (dev, staging, prod)
STAGE=dev

# GitHub Configuration (for CI/CD)
GITHUB_TOKEN=your-github-token
GITHUB_REPOSITORY=your-org/your-repo

# =============================================================================
# OPTIONAL: N8N CONFIGURATION
# =============================================================================
# If deploying n8n separately
N8N_DOMAIN=n8n.zurich-workflow.yourdomain.com
N8N_ECR_REPO_URI=your-account-id.dkr.ecr.ca-central-1.amazonaws.com/zurich-workflow-n8n

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Never commit this file with real values to version control
# 2. Use AWS Secrets Manager for production secrets
# 3. Rotate API keys regularly
# 4. Use least privilege IAM policies
# 5. Enable CloudTrail for audit logging
