#!/bin/bash

# =============================================================================
# ZURICH WORKFLOW CLEANUP SCRIPT
# =============================================================================
# This script removes the Zurich Workflow system from AWS
# Usage: ./cleanup.sh [stage] [region]
# Example: ./cleanup.sh dev ca-central-1

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================

STAGE=${1:-dev}
REGION=${2:-ca-central-1}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🧹 Starting Zurich Workflow cleanup..."
echo "📍 Stage: $STAGE"
echo "🌍 Region: $REGION"
echo "📁 Working directory: $SCRIPT_DIR"

# Confirmation prompt
echo ""
echo "⚠️  WARNING: This will permanently delete all resources for stage '$STAGE'"
echo "   This includes:"
echo "   - ECS services and tasks"
echo "   - Load balancers and target groups"
echo "   - VPC and networking resources"
echo "   - ECR repositories and images"
echo "   - S3 bucket contents (if configured)"
echo "   - CloudWatch logs"
echo ""
read -p "Are you sure you want to continue? (yes/no): " -r
if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    echo "❌ Cleanup cancelled"
    exit 1
fi

# =============================================================================
# ENVIRONMENT VALIDATION
# =============================================================================

echo "🔍 Validating environment..."

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ Error: AWS CLI is not configured"
    exit 1
fi

# Check if Serverless Framework is installed
if ! command -v sls &> /dev/null; then
    echo "❌ Error: Serverless Framework is not installed"
    exit 1
fi

echo "✅ Environment validation passed"

# =============================================================================
# SERVERLESS STACK REMOVAL
# =============================================================================

echo "☁️ Removing Serverless stack..."

cd "$SCRIPT_DIR"

# Remove the serverless stack
if sls info --stage $STAGE --region $REGION > /dev/null 2>&1; then
    echo "🗑️ Removing serverless stack for stage: $STAGE"
    sls remove --stage $STAGE --region $REGION --verbose
    echo "✅ Serverless stack removed"
else
    echo "ℹ️ No serverless stack found for stage: $STAGE"
fi

# =============================================================================
# ECR CLEANUP
# =============================================================================

echo "🐳 Cleaning up ECR repositories..."

AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

# Backend repository
BACKEND_REPO_NAME="zurich-workflow-backend-$STAGE"
if aws ecr describe-repositories --repository-names $BACKEND_REPO_NAME --region $REGION > /dev/null 2>&1; then
    echo "🗑️ Deleting ECR repository: $BACKEND_REPO_NAME"
    # Delete all images first
    aws ecr list-images --repository-name $BACKEND_REPO_NAME --region $REGION --query 'imageIds[*]' --output json | \
    jq '.[] | select(.imageTag != null) | {imageTag: .imageTag}' | \
    jq -s '.' | \
    xargs -I {} aws ecr batch-delete-image --repository-name $BACKEND_REPO_NAME --region $REGION --image-ids '{}' || true
    
    # Delete repository
    aws ecr delete-repository --repository-name $BACKEND_REPO_NAME --region $REGION --force
    echo "✅ Backend ECR repository deleted"
else
    echo "ℹ️ Backend ECR repository not found: $BACKEND_REPO_NAME"
fi

# Dashboard repository
DASHBOARD_REPO_NAME="zurich-workflow-dashboard-$STAGE"
if aws ecr describe-repositories --repository-names $DASHBOARD_REPO_NAME --region $REGION > /dev/null 2>&1; then
    echo "🗑️ Deleting ECR repository: $DASHBOARD_REPO_NAME"
    # Delete all images first
    aws ecr list-images --repository-name $DASHBOARD_REPO_NAME --region $REGION --query 'imageIds[*]' --output json | \
    jq '.[] | select(.imageTag != null) | {imageTag: .imageTag}' | \
    jq -s '.' | \
    xargs -I {} aws ecr batch-delete-image --repository-name $DASHBOARD_REPO_NAME --region $REGION --image-ids '{}' || true
    
    # Delete repository
    aws ecr delete-repository --repository-name $DASHBOARD_REPO_NAME --region $REGION --force
    echo "✅ Dashboard ECR repository deleted"
else
    echo "ℹ️ Dashboard ECR repository not found: $DASHBOARD_REPO_NAME"
fi

# =============================================================================
# S3 CLEANUP
# =============================================================================

echo "🪣 Cleaning up S3 resources..."

# Load environment variables if .env exists
if [ -f "$SCRIPT_DIR/.env" ]; then
    source "$SCRIPT_DIR/.env"
    
    # Clean up frontend S3 bucket
    if [ -n "$frontend_s3_bucket" ]; then
        if aws s3 ls "s3://$frontend_s3_bucket" > /dev/null 2>&1; then
            echo "🗑️ Emptying S3 bucket: $frontend_s3_bucket"
            aws s3 rm "s3://$frontend_s3_bucket" --recursive
            echo "✅ S3 bucket emptied: $frontend_s3_bucket"
            
            # Note: We don't delete the bucket itself as it might be used by other stages
            echo "ℹ️ S3 bucket retained (may be used by other stages): $frontend_s3_bucket"
        else
            echo "ℹ️ S3 bucket not found: $frontend_s3_bucket"
        fi
    else
        echo "ℹ️ Frontend S3 bucket not configured in .env"
    fi
else
    echo "ℹ️ .env file not found, skipping S3 cleanup"
fi

# =============================================================================
# CLOUDWATCH LOGS CLEANUP
# =============================================================================

echo "📊 Cleaning up CloudWatch logs..."

LOG_GROUP_NAME="zurich-workflow-$STAGE"
if aws logs describe-log-groups --log-group-name-prefix $LOG_GROUP_NAME --region $REGION --query 'logGroups[*].logGroupName' --output text | grep -q $LOG_GROUP_NAME; then
    echo "🗑️ Deleting CloudWatch log group: $LOG_GROUP_NAME"
    aws logs delete-log-group --log-group-name $LOG_GROUP_NAME --region $REGION
    echo "✅ CloudWatch log group deleted"
else
    echo "ℹ️ CloudWatch log group not found: $LOG_GROUP_NAME"
fi

# =============================================================================
# DOCKER CLEANUP
# =============================================================================

echo "🐳 Cleaning up local Docker images..."

# Remove local Docker images
docker images --format "table {{.Repository}}:{{.Tag}}" | grep "zurich-workflow" | grep $STAGE | while read image; do
    if [ -n "$image" ]; then
        echo "🗑️ Removing local Docker image: $image"
        docker rmi $image || true
    fi
done

# Clean up dangling images
echo "🧹 Removing dangling Docker images..."
docker image prune -f

echo "✅ Docker cleanup completed"

# =============================================================================
# CLEANUP COMPLETION
# =============================================================================

echo ""
echo "🎉 Cleanup completed successfully!"
echo ""
echo "📋 Cleanup Summary:"
echo "   Stage: $STAGE"
echo "   Region: $REGION"
echo "   ✅ Serverless stack removed"
echo "   ✅ ECR repositories deleted"
echo "   ✅ S3 bucket contents removed"
echo "   ✅ CloudWatch logs deleted"
echo "   ✅ Local Docker images cleaned"
echo ""
echo "ℹ️ Note: Some resources like Route53 records and ACM certificates"
echo "   may need to be manually removed if they were created outside"
echo "   of the Serverless stack."
echo ""
echo "✨ Environment cleaned!"
