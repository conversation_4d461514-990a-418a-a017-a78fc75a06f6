import time
from middleware_app.helpers.multiagent_framework_request import (
    chat_initiate_request
)


def initiate_chat_webchat(
    chat_id: str,
    customer_details: dict,
    rosters_id: str,
    channel_id: str,
    channel_name: str,
    initial_message: str = None,
):
    user_info = {
        "PhoneNumber": customer_details.get("phone_number", None),
    }
    if customer_details.get("customer_name", None):
        user_info["UserName"] = customer_details.get("name")

    user_id = {"id": chat_id, "id_type": "chat_id", "id_resource": "chat"}

    initiate_chat_request = {
        "version": "1.0",
        "user_info": {
            "user_id": user_id,
            "user_info": user_info,
        },
        "channel": {
            "channel_id": channel_id,
            "channel_name": channel_name,
            "ui_info": {"should_consolidate_buttons": False},
        },
        "incoming_events": [
            {
                "event_id": "abc-abc-abc",
                "event_user": {
                    "user_id": user_id,
                    "user_info": user_info,
                },
                "event_template": {"event_type": "initiate", "rosters_id": rosters_id},
            }
        ],
    }
    print(initiate_chat_request)
    status, result = chat_initiate_request(initiate_chat_request)
    if status == "Success":
        return result
    return {"error": "Failed to initiate chat"}


def send_message_webchat(
    chat_id: str,
    customer_details: dict,
    channel_id: str,
    channel_name: str,
    customer_message: str,
):
    user_info = {
        "phone_number": customer_details.get("phone_number", None),
    }
    if customer_details.get("customer_name", None):
        user_info["user_name"] = customer_details.get("name")

    user_id = {"id": chat_id, "id_type": "chat_id", "id_resource": "chat"}

    send_message_request = {
        "version": "1.0",
        "user_info": {
            "user_id": user_id,
            "user_info": user_info,
        },
        "channel": {
            "channel_id": channel_id,
            "channel_name": channel_name,
            "ui_info": {"should_consolidate_buttons": False},
        },
        "incoming_events": [
            {
                "event_id": "abc-abc-abc",
                "event_user": {
                    "user_id": user_id,
                    "user_info": user_info,
                },
                "event_template": {
                    "event_type": "text",
                    "text": customer_message,
                },
            }
        ],
    }
    status, result = chat_initiate_request(send_message_request)
    if status == "Success":
        return result
    return {"error": "Failed to send message"}
