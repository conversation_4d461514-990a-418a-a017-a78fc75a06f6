"""
AI-Controlled Image Preprocessing Engine

Intelligent preprocessing pipeline with document-type specific optimization
based on analysis of Zurich Challenge data and OCR quality requirements.
"""

import cv2
import numpy as np
import logging
import time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from PIL import Image, ImageEnhance, ImageFilter
import io

logger = logging.getLogger(__name__)

@dataclass
class PreprocessingResult:
    """Result from preprocessing pipeline"""
    success: bool
    processed_image_data: bytes
    techniques_applied: List[str]
    processing_time: float
    quality_improvement_estimate: float
    original_size: Tuple[int, int]
    processed_size: Tuple[int, int]
    error_message: Optional[str] = None

class PreprocessingEngine:
    """
    🧠 AI-Controlled Preprocessing Engine
    
    Automatically determines optimal preprocessing based on:
    - Document analysis results
    - Image quality assessment
    - Document type (claims, forms, receipts, etc.)
    - Processing challenges detected
    """
    
    def __init__(self):
        self.preprocessing_strategies = self._initialize_strategies()
        
    def _initialize_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize preprocessing strategies based on Zurich Challenge analysis"""
        
        return {
            "none": {
                "techniques": [],
                "expected_improvement": 0.0,
                "processing_time_ms": 0,
                "description": "No preprocessing - raw image"
            },

            "minimal": {
                "techniques": ["grayscale_conversion"],
                "expected_improvement": 0.05,
                "processing_time_ms": 50,
                "description": "Minimal preprocessing for clean digital documents"
            },
            
            "basic": {
                "techniques": [
                    "grayscale_conversion",
                    "noise_reduction", 
                    "deskewing",
                    "border_removal"
                ],
                "expected_improvement": 0.20,
                "processing_time_ms": 200,
                "description": "Basic cleanup for good quality scans"
            },
            
            "enhanced": {
                "techniques": [
                    "grayscale_conversion",
                    "noise_reduction",
                    "deskewing", 
                    "contrast_enhancement_clahe",
                    "sharpening",
                    "border_removal",
                    "morphological_opening"
                ],
                "expected_improvement": 0.40,
                "processing_time_ms": 500,
                "description": "Enhanced processing for forms, claims, receipts"
            },
            
            "aggressive": {
                "techniques": [
                    "grayscale_conversion",
                    "noise_reduction",
                    "deskewing",
                    "contrast_enhancement_clahe", 
                    "sharpening",
                    "binarization_otsu",
                    "morphological_opening",
                    "line_removal",
                    "border_removal",
                    "resize_optimization"
                ],
                "expected_improvement": 0.60,
                "processing_time_ms": 1000,
                "description": "Aggressive processing for poor quality scans"
            }
        }
    
    async def determine_preprocessing_level(
        self, 
        format_info: Any, 
        document_analysis: Optional[Any] = None
    ) -> str:
        """
        🧠 AI-powered preprocessing level determination
        
        Based on Zurich Challenge data analysis and document characteristics
        """
        
        logger.info(f"🔍 Determining preprocessing level for {format_info.format_type} document")
        
        # High preprocessing for poor quality scans (Zurich Challenge pattern)
        if (document_analysis and 
            hasattr(document_analysis, 'processing_challenges') and
            document_analysis.processing_challenges):
            
            poor_quality_indicators = [
                "poor_scan_quality", "faded_text", "skewed_document", 
                "low_contrast", "background_noise", "handwriting_mixed"
            ]
            
            if any(challenge in poor_quality_indicators 
                   for challenge in document_analysis.processing_challenges):
                logger.info("🚨 Poor quality detected - using aggressive preprocessing")
                return "aggressive"
        
        # Enhanced preprocessing for claims and forms (Zurich Challenge priority)
        if (document_analysis and 
            hasattr(document_analysis, 'document_type') and
            document_analysis.document_type in ["claim", "form", "receipt", "statement"]):
            logger.info("📋 Claims/forms document - using enhanced preprocessing")
            return "enhanced"
        
        # Enhanced preprocessing for image files (common in Zurich Challenge)
        if format_info.format_type == "image":
            # Check file size as quality indicator
            if format_info.file_size < 100 * 1024:  # < 100KB likely low quality
                logger.info("📸 Small image file - using enhanced preprocessing")
                return "enhanced"
            else:
                logger.info("📸 Image file - using basic preprocessing")
                return "basic"
        
        # Basic preprocessing for PDF scans
        if format_info.format_type == "pdf" and format_info.requires_ocr:
            logger.info("📄 PDF scan - using basic preprocessing")
            return "basic"
        
        # Minimal preprocessing for clean digital documents
        if format_info.format_type in ["office", "text", "web"]:
            logger.info("💻 Digital document - using minimal preprocessing")
            return "minimal"
        
        # Default to basic for unknown cases
        logger.info("❓ Unknown document type - using basic preprocessing")
        return "basic"
    
    async def preprocess_image(
        self, 
        image_data: bytes, 
        level: str = "auto",
        format_info: Optional[Any] = None,
        document_analysis: Optional[Any] = None,
        force_techniques: Optional[List[str]] = None
    ) -> PreprocessingResult:
        """
        🔧 Main preprocessing pipeline with AI-controlled optimization
        
        Args:
            image_data: Raw image bytes
            level: Preprocessing level or "auto" for AI decision
            format_info: File format information
            document_analysis: LLM document analysis
            force_techniques: Override specific techniques
            
        Returns:
            PreprocessingResult with processed image and metadata
        """
        
        start_time = time.time()
        
        try:
            # Convert bytes to OpenCV image
            nparr = np.frombuffer(image_data, np.uint8)
            original_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if original_image is None:
                return PreprocessingResult(
                    success=False,
                    processed_image_data=image_data,
                    techniques_applied=[],
                    processing_time=0.0,
                    quality_improvement_estimate=0.0,
                    original_size=(0, 0),
                    processed_size=(0, 0),
                    error_message="Failed to decode image"
                )
            
            original_size = (original_image.shape[1], original_image.shape[0])
            
            # Determine preprocessing level
            if level == "auto":
                level = await self.determine_preprocessing_level(format_info, document_analysis)

            # Handle "none" level - skip all preprocessing
            if level == "none":
                logger.info("Skipping preprocessing (level: none)")
                return PreprocessingResult(
                    success=True,
                    processed_image_data=image_data,  # Return original data
                    techniques_applied=[],
                    processing_time=time.time() - start_time,
                    quality_improvement_estimate=0.0,
                    original_size=original_size,
                    processed_size=original_size
                )

            # Get preprocessing strategy
            strategy = self.preprocessing_strategies.get(level, self.preprocessing_strategies["basic"])
            techniques = force_techniques or strategy["techniques"]

            logger.info(f"Applying {level} preprocessing with {len(techniques)} techniques")
            
            # Apply preprocessing techniques
            processed_image = original_image.copy()
            applied_techniques = []
            
            for technique in techniques:
                try:
                    processed_image = await self._apply_technique(processed_image, technique)
                    applied_techniques.append(technique)
                    logger.debug(f"Applied {technique}")
                except Exception as e:
                    logger.warning(f"Failed to apply {technique}: {str(e)}")
            
            # Convert back to bytes
            processed_size = (processed_image.shape[1], processed_image.shape[0])
            _, buffer = cv2.imencode('.png', processed_image)
            processed_image_data = buffer.tobytes()
            
            processing_time = time.time() - start_time
            
            logger.info(f"Preprocessing completed: {len(applied_techniques)} techniques in {processing_time:.3f}s")
            
            return PreprocessingResult(
                success=True,
                processed_image_data=processed_image_data,
                techniques_applied=applied_techniques,
                processing_time=processing_time,
                quality_improvement_estimate=strategy["expected_improvement"],
                original_size=original_size,
                processed_size=processed_size
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Preprocessing failed: {str(e)}")
            
            return PreprocessingResult(
                success=False,
                processed_image_data=image_data,
                techniques_applied=[],
                processing_time=processing_time,
                quality_improvement_estimate=0.0,
                original_size=(0, 0),
                processed_size=(0, 0),
                error_message=str(e)
            )
    
    async def _apply_technique(self, image: np.ndarray, technique: str) -> np.ndarray:
        """Apply specific preprocessing technique"""
        
        if technique == "grayscale_conversion":
            if len(image.shape) == 3:
                return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            return image
            
        elif technique == "noise_reduction":
            if len(image.shape) == 3:
                return cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
            else:
                return cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
                
        elif technique == "deskewing":
            return self._deskew_image(image)
            
        elif technique == "contrast_enhancement_clahe":
            return self._apply_clahe(image)
            
        elif technique == "sharpening":
            return self._apply_sharpening(image)
            
        elif technique == "binarization_otsu":
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            return binary
            
        elif technique == "morphological_opening":
            return self._apply_morphological_opening(image)
            
        elif technique == "line_removal":
            return self._remove_lines(image)
            
        elif technique == "border_removal":
            return self._remove_borders(image)
            
        elif technique == "resize_optimization":
            return self._optimize_resolution(image)
            
        else:
            logger.warning(f"Unknown preprocessing technique: {technique}")
            return image

    def _deskew_image(self, image: np.ndarray) -> np.ndarray:
        """Deskew tilted documents"""
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            # Find edges
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)

            # Find lines using Hough transform
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

            if lines is not None:
                # Calculate average angle
                angles = []
                for rho, theta in lines[:10]:  # Use first 10 lines
                    angle = theta * 180 / np.pi
                    if angle > 90:
                        angle = angle - 180
                    angles.append(angle)

                if angles:
                    median_angle = np.median(angles)

                    # Only rotate if angle is significant
                    if abs(median_angle) > 0.5:
                        h, w = image.shape[:2]
                        center = (w // 2, h // 2)
                        rotation_matrix = cv2.getRotationMatrix2D(center, median_angle, 1.0)
                        return cv2.warpAffine(image, rotation_matrix, (w, h),
                                            flags=cv2.INTER_CUBIC,
                                            borderMode=cv2.BORDER_REPLICATE)

            return image
        except Exception as e:
            logger.warning(f"Deskewing failed: {str(e)}")
            return image

    def _apply_clahe(self, image: np.ndarray) -> np.ndarray:
        """Apply CLAHE contrast enhancement"""
        try:
            if len(image.shape) == 3:
                # Convert to LAB color space
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                l, a, b = cv2.split(lab)

                # Apply CLAHE to L channel
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                l = clahe.apply(l)

                # Merge channels and convert back
                lab = cv2.merge([l, a, b])
                return cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            else:
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                return clahe.apply(image)
        except Exception as e:
            logger.warning(f"CLAHE failed: {str(e)}")
            return image

    def _apply_sharpening(self, image: np.ndarray) -> np.ndarray:
        """Apply unsharp mask sharpening"""
        try:
            # Gaussian blur
            blurred = cv2.GaussianBlur(image, (0, 0), 1.0)

            # Unsharp mask
            sharpened = cv2.addWeighted(image, 1.5, blurred, -0.5, 0)

            return sharpened
        except Exception as e:
            logger.warning(f"Sharpening failed: {str(e)}")
            return image

    def _apply_morphological_opening(self, image: np.ndarray) -> np.ndarray:
        """Apply morphological opening to clean up noise"""
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            # Create kernel
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))

            # Apply opening (erosion followed by dilation)
            opened = cv2.morphologyEx(gray, cv2.MORPH_OPEN, kernel)

            # Convert back to original format if needed
            if len(image.shape) == 3:
                return cv2.cvtColor(opened, cv2.COLOR_GRAY2BGR)
            else:
                return opened
        except Exception as e:
            logger.warning(f"Morphological opening failed: {str(e)}")
            return image

    def _remove_lines(self, image: np.ndarray) -> np.ndarray:
        """Remove horizontal and vertical lines (for forms)"""
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            # Remove horizontal lines
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
            horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)

            # Remove vertical lines
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
            vertical_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, vertical_kernel)

            # Combine line masks
            lines_mask = cv2.add(horizontal_lines, vertical_lines)

            # Remove lines from original
            result = cv2.subtract(gray, lines_mask)

            # Convert back to original format if needed
            if len(image.shape) == 3:
                return cv2.cvtColor(result, cv2.COLOR_GRAY2BGR)
            else:
                return result
        except Exception as e:
            logger.warning(f"Line removal failed: {str(e)}")
            return image

    def _remove_borders(self, image: np.ndarray) -> np.ndarray:
        """Remove dark borders and frames"""
        try:
            h, w = image.shape[:2]

            # Remove 2% border from each side
            border_size = min(int(h * 0.02), int(w * 0.02), 10)

            if border_size > 0:
                return image[border_size:h-border_size, border_size:w-border_size]

            return image
        except Exception as e:
            logger.warning(f"Border removal failed: {str(e)}")
            return image

    def _optimize_resolution(self, image: np.ndarray) -> np.ndarray:
        """Optimize image resolution for OCR (target 300 DPI equivalent)"""
        try:
            h, w = image.shape[:2]

            # Target size for optimal OCR (roughly 300 DPI equivalent)
            target_width = 2400  # Good for A4 page width

            if w < target_width * 0.7:  # If too small, upscale
                scale_factor = target_width / w
                new_width = int(w * scale_factor)
                new_height = int(h * scale_factor)

                return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

            elif w > target_width * 1.5:  # If too large, downscale
                scale_factor = target_width / w
                new_width = int(w * scale_factor)
                new_height = int(h * scale_factor)

                return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

            return image
        except Exception as e:
            logger.warning(f"Resolution optimization failed: {str(e)}")
            return image

# Global preprocessing engine instance
preprocessing_engine = PreprocessingEngine()
