service: rozie-mock-company-book-flight


provider:
  name: aws
  runtime: python3.9
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:Scan
        - dynamodb:DeleteItem
        - dynamodb:UpdateItem
        - dynamodb:Query
      Resource: "*"
  apiGateway:
    apiKeys:
      - ${self:service}-${self:provider.stage}-Api-Key

functions:
  flight-details:
    name: ${self:service}-${self:provider.stage}
    handler: index.lambda_handler
    description: function to manage the flight details
    environment:
      ENABLE_LOG: TRUE
      FLIGHT_DETAILS_TABLE: rozie-mock-airline-flight-details-${self:provider.stage}
      BOOKING_DETAILS_TABLE: rozie-mock-airline-flight-booking-${self:provider.stage}
      CUSTOMER_PROFILE_TABLE_NAME: rozie-mock-company-customer-profile-${self:provider.stage}
      CREATE_PNR_URL: https://d9hxof0tel.execute-api.ca-central-1.amazonaws.com/dev/createPNR
      CREATE_PNR_KEY: 1z8mRPQvzx3HH42Gzm1BC2OPG5gcLeBZ4E1eMEHa
      UPDATE_PNR_URL: https://d9hxof0tel.execute-api.ca-central-1.amazonaws.com/dev/updatePNR
      DELETE_PNR_URL: https://d9hxof0tel.execute-api.ca-central-1.amazonaws.com/dev/deletePNR
    events:
      - http:
          path: /book-flights
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /update-booking
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /delete-booking
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang

resources:
  extensions:
    ServerlessDeploymentBucket:
      Properties:
        PublicAccessBlockConfiguration:
          BlockPublicAcls: True
          BlockPublicPolicy: True
          IgnorePublicAcls: True
          RestrictPublicBuckets: True
  Resources:
    FlightBookingTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: pnr
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: pnr
        BillingMode: PAY_PER_REQUEST
        TableName: rozie-mock-airline-flight-booking-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True