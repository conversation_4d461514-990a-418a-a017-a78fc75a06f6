/* Zurich Dashboard - Zendesk Garden Inspired Styles */

:root {
  /* Zendesk Garden Color Palette */
  --zd-color-blue-600: #1f73b7;
  --zd-color-blue-700: #144a75;
  --zd-color-green-600: #038153;
  --zd-color-red-600: #d93f4c;
  --zd-color-yellow-600: #f79a3e;
  --zd-color-grey-100: #f8f9fa;
  --zd-color-grey-200: #e9ebed;
  --zd-color-grey-300: #d8dcde;
  --zd-color-grey-600: #68737d;
  --zd-color-grey-800: #2f3941;
  
  /* Zurich Brand Colors */
  --zurich-blue: #0066cc;
  --zurich-dark-blue: #003d7a;
  --zurich-light-blue: #e6f2ff;
  
  /* Spacing */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--zd-color-grey-800);
  background-color: var(--zd-color-grey-100);
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--zd-color-grey-200);
  border-top: 4px solid var(--zurich-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Page Management */
.page {
  min-height: 100vh;
}

.page.hidden {
  display: none !important;
}

/* Login Page */
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--zurich-blue) 0%, var(--zurich-dark-blue) 100%);
  padding: var(--space-lg);
}

.login-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-xl);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.logo {
  height: 48px;
  margin-bottom: var(--space-md);
}

.login-header h1 {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--zd-color-grey-800);
  margin-bottom: var(--space-sm);
}

.login-header p {
  color: var(--zd-color-grey-600);
  font-size: var(--font-size-md);
}

/* Form Styles */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: var(--space-sm);
  color: var(--zd-color-grey-800);
}

.form-group input[type="email"],
.form-group input[type="password"] {
  width: 100%;
  padding: var(--space-md);
  border: 2px solid var(--zd-color-grey-300);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-md);
  transition: border-color 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--zurich-blue);
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal !important;
}

.checkbox-label input[type="checkbox"] {
  margin-right: var(--space-sm);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-lg);
  border: 2px solid transparent;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-md);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: var(--space-sm);
}

.btn-primary {
  background-color: var(--zurich-blue);
  color: white;
  border-color: var(--zurich-blue);
}

.btn-primary:hover {
  background-color: var(--zurich-dark-blue);
  border-color: var(--zurich-dark-blue);
}

.btn-outline {
  background-color: transparent;
  color: var(--zurich-blue);
  border-color: var(--zurich-blue);
}

.btn-outline:hover {
  background-color: var(--zurich-light-blue);
}

.btn-full {
  width: 100%;
}

.login-footer {
  text-align: center;
  margin-top: var(--space-lg);
}

.forgot-password {
  color: var(--zurich-blue);
  text-decoration: none;
  font-size: var(--font-size-sm);
}

.forgot-password:hover {
  text-decoration: underline;
}

/* Dashboard Header */
.dashboard-header {
  background: white;
  border-bottom: 1px solid var(--zd-color-grey-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-lg);
  height: 64px;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-logo {
  height: 32px;
}

.header-left h1 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--zd-color-grey-800);
}

/* Navigation */
.header-nav {
  display: flex;
  gap: var(--space-sm);
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: transparent;
  border: none;
  border-radius: var(--radius-sm);
  color: var(--zd-color-grey-600);
  font-size: var(--font-size-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background-color: var(--zd-color-grey-100);
  color: var(--zd-color-grey-800);
}

.nav-btn.active {
  background-color: var(--zurich-light-blue);
  color: var(--zurich-blue);
  font-weight: 500;
}

.nav-icon {
  font-size: var(--font-size-lg);
}

/* User Menu */
.user-menu {
  position: relative;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: transparent;
  border: 1px solid var(--zd-color-grey-300);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-btn:hover {
  background-color: var(--zd-color-grey-100);
}

.user-avatar {
  width: 24px;
  height: 24px;
  background-color: var(--zurich-blue);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--zd-color-grey-200);
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-md);
  min-width: 160px;
  display: none;
  z-index: 1000;
}

.user-menu:hover .user-dropdown {
  display: block;
}

.dropdown-item {
  display: block;
  padding: var(--space-sm) var(--space-md);
  color: var(--zd-color-grey-800);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: var(--zd-color-grey-100);
}

.dropdown-divider {
  border: none;
  border-top: 1px solid var(--zd-color-grey-200);
  margin: var(--space-xs) 0;
}

/* Main Dashboard */
.dashboard-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-lg);
}

/* Content Pages */
.content-page {
  display: none;
}

.content-page.active {
  display: block;
}

/* Page Header */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-xl);
}

.page-header h2 {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--zd-color-grey-800);
}

.page-actions {
  display: flex;
  gap: var(--space-md);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.metric-card {
  background: white;
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--zd-color-grey-200);
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
}

.metric-header h3 {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--zd-color-grey-600);
}

.metric-icon {
  font-size: var(--font-size-xl);
}

.metric-value {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--zd-color-grey-800);
  margin-bottom: var(--space-sm);
}

.metric-change {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.metric-change.positive {
  color: var(--zd-color-green-600);
}

.metric-change.negative {
  color: var(--zd-color-red-600);
}

.metric-change.neutral {
  color: var(--zd-color-grey-600);
}

/* Content Sections */
.content-section {
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--zd-color-grey-200);
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg);
  border-bottom: 1px solid var(--zd-color-grey-200);
}

.section-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--zd-color-grey-800);
}

.section-actions {
  display: flex;
  gap: var(--space-md);
  align-items: center;
}

.search-input {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--zd-color-grey-300);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  width: 200px;
}

.filter-select {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--zd-color-grey-300);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  background: white;
}

/* Table Styles */
.table-container {
  overflow-x: auto;
}

.claims-table {
  width: 100%;
  border-collapse: collapse;
}

.claims-table th {
  background-color: var(--zd-color-grey-100);
  padding: var(--space-md);
  text-align: left;
  font-weight: 600;
  font-size: var(--font-size-sm);
  color: var(--zd-color-grey-600);
  border-bottom: 1px solid var(--zd-color-grey-200);
}

.claims-table td {
  padding: var(--space-md);
  border-bottom: 1px solid var(--zd-color-grey-200);
  font-size: var(--font-size-sm);
}

.claims-table tr:hover {
  background-color: var(--zd-color-grey-100);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.pending {
  background-color: rgba(247, 154, 62, 0.1);
  color: var(--zd-color-yellow-600);
}

.status-badge.approved {
  background-color: rgba(3, 129, 83, 0.1);
  color: var(--zd-color-green-600);
}

.status-badge.denied {
  background-color: rgba(217, 63, 76, 0.1);
  color: var(--zd-color-red-600);
}

.status-badge.processing {
  background-color: rgba(31, 115, 183, 0.1);
  color: var(--zd-color-blue-600);
}

/* Priority Badges */
.priority-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.priority-badge.high {
  background-color: rgba(217, 63, 76, 0.1);
  color: var(--zd-color-red-600);
}

.priority-badge.medium {
  background-color: rgba(247, 154, 62, 0.1);
  color: var(--zd-color-yellow-600);
}

.priority-badge.low {
  background-color: rgba(3, 129, 83, 0.1);
  color: var(--zd-color-green-600);
}

/* Action Buttons */
.action-btn {
  padding: var(--space-xs) var(--space-sm);
  border: 1px solid var(--zd-color-grey-300);
  border-radius: var(--radius-sm);
  background: white;
  color: var(--zd-color-grey-600);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: var(--space-xs);
}

.action-btn:hover {
  background-color: var(--zd-color-grey-100);
  color: var(--zd-color-grey-800);
}

/* Reports Page */
.filters-section {
  background: white;
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--zd-color-grey-200);
  display: flex;
  gap: var(--space-lg);
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  min-width: 150px;
}

.filter-group label {
  font-weight: 500;
  font-size: var(--font-size-sm);
  color: var(--zd-color-grey-600);
}

.filter-group select {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--zd-color-grey-300);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  background: white;
}

/* Charts */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-lg);
}

.chart-card {
  background: white;
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--zd-color-grey-200);
}

.chart-card h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--zd-color-grey-800);
  margin-bottom: var(--space-lg);
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--zd-color-grey-100);
  border-radius: var(--radius-sm);
  color: var(--zd-color-grey-600);
  font-style: italic;
}

/* Ticket View */
.ticket-container {
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--zd-color-grey-200);
  padding: var(--space-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 var(--space-md);
    flex-wrap: wrap;
    height: auto;
    min-height: 64px;
  }

  .header-nav {
    order: 3;
    width: 100%;
    justify-content: center;
    padding: var(--space-sm) 0;
    border-top: 1px solid var(--zd-color-grey-200);
  }

  .dashboard-main {
    padding: var(--space-md);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-md);
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .section-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-sm);
  }

  .search-input {
    width: 100%;
  }
}
