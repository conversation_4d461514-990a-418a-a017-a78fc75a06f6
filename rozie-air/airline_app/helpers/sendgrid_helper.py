import json
import requests

def post_request(url, body=None, username=None, password=None, headers=None):
    """
    This function invokes a POST method and waits for the result.
    """
    param = {"url": url, "timeout": 30, "headers": {"Content-Type": "application/json"}}
    if body:
        param["data"] = json.dumps(body)
    if username and password:
        param["auth"] = (username, password)
    if headers:
        param["headers"].update(headers)
    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            try:
                return "Failed", response.json()  
            except ValueError:  # Handle non-JSON responses
                return "Failed", {"error": response.text or "Unknown error occurred."}
        # Check if the response body is empty
        if not response.text.strip():  # Response is empty or whitespace
            return "Success", {"empty_phorest_result": True}        
        try:
            return "Success", response.json()
        except ValueError:  
            return "Failed", {"error": response.text or "Unknown error occurred."}

    except Exception as e:
        print_error_log("", e)
        return "Failed", {"error": str(e)}

def send_email(body):
    """
    This function used to create client entry
    """

    url = "https://20kvc5axmj.execute-api.ca-central-1.amazonaws.com/dev/sendEmail"

    try:
        status, result = post_request(url=url, body=body)
        if status == "Failed":
            print("send_appointment_email, Failed", result)
            return False
        return result
    except Exception as e:
        print(e)
        return False

# print(send_appointment_email({
#     "reference_id": "123",
#     "from": "<EMAIL>",
#     "to": "<EMAIL>",
#     "subject": "Brook booked you a customer!",
#     "html_body": "test body",
#     "send_separate": True
# }))