service: rozie-mock-api-wrapper


provider:
  name: aws
  runtime: nodejs18.x
  endpointType: regional
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:Scan
        - dynamodb:DeleteItem
        - dynamodb:UpdateItem
        - dynamodb:Query
      Resource: "*"
  apiGateway:
    apiKeys:
      - ${self:service}-${self:provider.stage}-Api-Key

functions:
  mock-api-wrapper:
    name: ${self:service}-${self:provider.stage}
    handler: index.lambda_handler
    description: wrapper function for response mapping of mock api's
    environment:
      GET_CUSTOMER_PNR_ENDPOINT: "https://d9hxof0tel.execute-api.ca-central-1.amazonaws.com/dev/getCustomerPNR"
      GET_CUSTOMER_PNR_APIKEY: "1z8mRPQvzx3HH42Gzm1BC2OPG5gcLeBZ4E1eMEHa"
      GET_BAGGAGE_ENDPOINT: "https://8xk9xo2za1.execute-api.ca-central-1.amazonaws.com/dev/getBaggage"
      GET_BAGGAGE_APIKEY: "5mbKV4WIbr5CjFiKls6Li6PQBs82KTve8DacsMQx"
      CASE_MANAGEMENT_ENDPOINT: "https://a0woh7doze.execute-api.ca-central-1.amazonaws.com/dev/listCases"
      CASE_MANAGEMENT_APIKEY: "mzfpavfA9q5Q85HZsEyAeovF1O1b2V89nXVHzfnc"
    events:
      - http:
          path: /pnrWrapper
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /baggageWrapper
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      - http:
          path: /caseManagementWrapper
          method: post
          private: true
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang
      
resources:
  extensions:
    ServerlessDeploymentBucket:
      Properties:
        PublicAccessBlockConfiguration:
          BlockPublicAcls: True
          BlockPublicPolicy: True
          IgnorePublicAcls: True
          RestrictPublicBuckets: True

