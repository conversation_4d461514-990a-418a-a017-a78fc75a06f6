from fastapi import APIRouter, Query, HTTPException
from typing import List
from pydantic import BaseModel
from airline_app.services.location_service import LocationService

router = APIRouter()
location_service = LocationService()

class LocationResponse(BaseModel):
    city: str
    country: str

@router.get("/search", response_model=List[LocationResponse])
async def search_locations(
    keyword: str = Query(..., description="Keyword to search for (e.g., city name, airport code)"),
    sub_type: str = Query("CITY", description="Type of location to search for (e.g., CITY, AIRPORT)")
):
    """
    Search for locations using the Amadeus Reference Data API.
    
    Args:
        keyword (str): The keyword to search for
        sub_type (str): The type of location to search for
        
    Returns:
        List[LocationResponse]: List of locations with city and country names
    """
    try:
        locations = await location_service.search_locations(keyword, sub_type)
        return locations
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 