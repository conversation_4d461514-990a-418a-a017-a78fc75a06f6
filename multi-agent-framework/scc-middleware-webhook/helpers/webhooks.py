import os

# import json
import time
import asyncio

# from random import randint
from twilio.twiml.voice_response import VoiceResponse, <PERSON>, <PERSON><PERSON>, Dial

# from twilio.request_validator import RequestValidator
from .logging_helper import print_info_log
from .dynamo_helper import (
    get_item_by_primary_key,
    delete_item,
    upsert_item,
    put_record_into_dynamodb,
)
from .twilio_util import (
    get_contact_attributes,
    update_contact_attribute,
    parse_twilio_body,
    reset_contact_attributes,
)
from .request_helper import sync_post_request, delayed_post_request, async_post_request

stylist_pronunciations = {
    "<PERSON><PERSON>": "<PERSON><PERSON>",
    "<PERSON><PERSON>": "<PERSON><PERSON>",
    "<PERSON><PERSON>": "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>": "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>": "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON>": "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>": "<PERSON><PERSON><PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON><PERSON>": "<PERSON><PERSON><PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON>": "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>": "<PERSON><PERSON><PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON><PERSON>": "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>": "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>": "<PERSON><PERSON><PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON>.": "<PERSON> <PERSON>.",
    "<PERSON>": "<PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON><PERSON>": "<PERSON><PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON> <PERSON>.": "<PERSON> <PERSON>.",
    "<PERSON> <PERSON>.": "<PERSON> <PERSON>.",
    "<PERSON><PERSON><PERSON>": "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>": "<PERSON><PERSON><PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON>": "<PERSON>",
    "<PERSON><PERSON>": "Mariah",
    "Marlene": "Marleni",
    "Mercedes": "Mercedes",
    "Natasha": "Natasha",
    "Nicole": "Nicole",
    "Remy": "Remy",
    "Ruben": "Ruben",
    "Sachi": "Sachi",
    "Samara": "Samara",
    "Sarah": "Sarah",
    "Stephanie": "Stephanie",
    "Uzma": "Oozma",
    "Olympia": "Olympia",
    "Jay": "Jay",
    "Donna": "Donna",
    "Janelle": "Janelle",
    "Irene": "Irene",
    "Keturah": "Keturah",
    "Tayo": "Taayo",
    "Sydne": "Sydne",
}


def check_for_lambda_result(initial_contact_id):
    result = get_item_by_primary_key(
        os.environ.get("SALON_RESULT_PROXY_TABLE"), "contact_id", initial_contact_id
    )
    print_info_log(initial_contact_id, "DDB result", result)
    if result:
        return result
    return False


def autogen_greeting_flow(event):
    """Greeting Message for Twilio"""
    start = time.time()
    body_attribute = parse_twilio_body(event.get("body"))
    conversation_sid = body_attribute.get("CallSid")
    print_info_log(conversation_sid, "greeting_flow : invoked", "")
    caller_phone = body_attribute.get("Caller")
    twilio_phone = body_attribute.get("Called")
    card_checker_result = get_item_by_primary_key(
        os.environ.get("CARD_CHECKER_TABLE"), "phone_number", twilio_phone
    )
    client_details = None  # get_client_details_salon(caller_phone)
    response = VoiceResponse()
    if card_checker_result.get("framework") in [
        "multi_agent_framework",
        "rozie_studio",
    ]:
        response.redirect(os.environ.get("AUTOGEN_INITIATE_HOOK"))
        print_info_log(conversation_sid, "greeting_flow : response", response)
        print_info_log(
            conversation_sid,
            "greeting_flow : completed, time taken",
            str(time.time() - start),
        )
        return {
            "statusCode": 200,
            "headers": {"Content-Type": "application/xml"},
            "body": str(response),
        }
    if client_details:
        returning_client_prompt = card_checker_result.get(
            "returningClientWelcomePrompt"
        )
        update_contact_attribute(
            conversation_sid,
            {
                "customer_name": client_details.get("fname"),
                "customer_id": client_details.get("phone_number"),
            },
        )
        first_name = client_details.get("fname")
        message = returning_client_prompt.replace("__firstName__", first_name)
        welcome_prompt = Say(
            message,
            language="en-US",
            voice="Google.en-US-Neural2-F",
        )
    else:
        new_client_prompt = card_checker_result.get("newClientWelcomePrompt")
        welcome_prompt = Say(
            new_client_prompt,
            language="en-US",
            voice="Google.en-US-Neural2-F",
        )
    response.append(welcome_prompt)
    offer_assistance_prompt = card_checker_result.get("offerAssistancePrompt")
    help_prompt = Say(
        offer_assistance_prompt,
        language="en-US",
        voice="Google.en-US-Neural2-F",
    )
    response.append(help_prompt)
    gather = Gather(
        speechTimeout=2,
        actionOnEmptyResult="true",
        input="speech",
        action=os.environ.get("AUTOGEN_INITIATE_HOOK"),
        method="POST",
        speechModel="experimental_conversations",
    )
    response.append(gather)
    print_info_log(conversation_sid, "greeting_flow : response", response)
    print_info_log(
        conversation_sid,
        "greeting_flow : completed, time taken",
        str(time.time() - start),
    )
    async_post_request(
        url=f"https://3nz38k3jg3.execute-api.ca-central-1.amazonaws.com/dev/autogen/callStatusChange?conversation_sid={conversation_sid}"
    )
    return {
        "statusCode": 200,
        "headers": {"Content-Type": "application/xml"},
        "body": str(response),
    }


def parse_middleware_response(data, phone_number, conversation_id):
    result = {
        "follow_up": "",
        "complete": False,
        "dynamic_keys": {},
    }
    result["complete"] = data.get("should_end_interaction", False)
    responses = data.get("response_map", {}).get("responses", {}).get("default", [])
    response_texts = []
    for response in responses:
        response_type = response.get("response_template", {}).get("response_type")

        if response_type == "text":
            if "<!DOCTYPE html>" not in response.get("response_template", {}).get(
                "text", ""
            ):
                response_texts.append(
                    response.get("response_template", {}).get("text", "")
                )

        elif response_type == "quick_reply":
            items = response.get("response_template", {}).get("items", [])
            labels = [item.get("label", "") for item in items]
            response_texts.append(", ".join(labels))

        elif response_type == "dynamic":
            ui_elements = response.get("response_template", {}).get("ui_elements", {})
            for key, element in ui_elements.items():
                result["dynamic_keys"][key] = element.get("text", "")

    result["follow_up"] = "\n".join(response_texts).strip()
    if not result["follow_up"]:
        result["follow_up"] = "Application Error Occurred"
        result["complete"] = True
    item = get_item_by_primary_key(
        os.environ.get("TWILIO_CONTACT_ATTRIBUTE_TABLE"), "contact_id", conversation_id
    )
    attributes = item.get("attributes", {})
    attributes.update(result["dynamic_keys"])
    item["attributes"] = attributes
    put_record_into_dynamodb(os.environ.get("TWILIO_CONTACT_ATTRIBUTE_TABLE"), item)
    return result


def autogen_initiate_call(event):
    """Starting point for Twilio Call"""
    start = time.time()
    body_attribute = parse_twilio_body(event.get("body"))
    conversation_sid = body_attribute.get("CallSid")
    print_info_log(conversation_sid, "initiate_call : invoked", "")
    caller_phone = body_attribute.get("Caller")
    twilio_phone = body_attribute.get("Called")
    speech_input = body_attribute.get("SpeechResult", None)
    contact_attributes = get_contact_attributes(conversation_sid)
    response = VoiceResponse()
    card_checker_result = get_item_by_primary_key(
        os.environ.get("CARD_CHECKER_TABLE"), "phone_number", twilio_phone
    )
    assigned_framework = card_checker_result.get("framework")
    params = {
        "chat_id": conversation_sid,
        "framework_to_used": assigned_framework,
        "channel_id": "VC",
        "channel_name": "voice",
        "customer_details": {"phone_number": caller_phone},
    }
    if assigned_framework == "multi_agent_framework":
        params["rosters_id"] = card_checker_result.get("rosters_id")
    elif assigned_framework == "rozie_studio":
        params["skill_id"] = card_checker_result.get("skill_id")
    elif assigned_framework == "rozie_studio_navigator":
        if not speech_input:
            if int(contact_attributes.get("noCustomerResponse", 0)) > 2:
                response.say(
                    "With out a response i can not continue, Thank you for calling, Goodbye.",
                    voice="Google.en-US-Neural2-F",
                    language="en-US",
                )
                # response.dial("+17183982603")
                response.hangup()
            else:
                response.say(
                    "Sorry I did'nt catch what you said, can you repeat again?",
                    voice="Google.en-US-Neural2-F",
                    language="en-US",
                )
                contact_attributes["noCustomerResponse"] = (
                    int(contact_attributes.get("noCustomerResponse", 0)) + 1
                )
                gather = Gather(
                    speechTimeout=2,
                    actionOnEmptyResult="true",
                    input="speech",
                    action=os.environ.get("AUTOGEN_INITIATE_HOOK"),
                    method="POST",
                    speechModel="experimental_conversations",
                )
                response.append(gather)
            update_contact_attribute(conversation_sid, contact_attributes)
            print_info_log(
                conversation_sid, "process_gathered_input_flow : response", response
            )
            print_info_log(
                conversation_sid,
                "process_gathered_input_flow : invoked, time taken",
                str(time.time() - start),
            )
            return {
                "statusCode": 200,
                "headers": {"Content-Type": "application/xml"},
                "body": str(response),
            }
        params["customer_message"] = speech_input
    _, result = sync_post_request(
        url=os.getenv("MIDDLEWARE_INITIATE_ULR"),
        data=params,
        headers={"access_token": os.getenv("AUTOGEN_ACCESS_TOKEN")},
    )
    result = parse_middleware_response(result, caller_phone, conversation_sid)
    contact_attributes = get_contact_attributes(conversation_sid)
    message = result.get("follow_up", "")
    for message_part in format_say_message(message):
        response.append(message_part)
    print_info_log(conversation_sid, "sync_post_request:AUTOGEN_INITIATE", "")

    contact_attributes["last_qus"] = speech_input
    contact_attributes["requestSent"] = "True"
    contact_attributes["getAgentResponseInvoked"] = "False"
    contact_attributes["noCustomerResponse"] = 0

    response.redirect(os.environ.get("AUTOGEN_GATHER_HOOK"))
    update_contact_attribute(conversation_sid, contact_attributes)
    print_info_log(conversation_sid, "initiate_call : response", response)
    print_info_log(
        conversation_sid,
        "initiate_call : completed, time taken",
        str(time.time() - start),
    )

    return {
        "statusCode": 200,
        "headers": {"Content-Type": "application/xml"},
        "body": str(response),
    }


def autogen_gather_input_flow(event):
    """Gathers customer input"""
    start = time.time()
    body_attribute = parse_twilio_body(event.get("body"))
    conversation_sid = body_attribute.get("CallSid")
    print_info_log(conversation_sid, "gather_input_flow : invoked", "")
    response = VoiceResponse()
    gather = Gather(
        speechTimeout=2,
        actionOnEmptyResult="true",
        input="speech",
        action=os.environ.get("AUTOGEN_PROCESS_GATHER_HOOK"),
        method="POST",
        speechModel="experimental_conversations",
    )
    response.append(gather)
    reset_contact_attributes(
        conversation_sid,
        ["requestSent", "getAgentResponseInvoked", "last_qus"],
    )
    delete_item(
        os.environ.get("SALON_RESULT_PROXY_TABLE"), "contact_id", conversation_sid
    )
    print_info_log(conversation_sid, "gather_input_flow : response", response)
    print_info_log(
        conversation_sid,
        "gather_input_flow : completed, time taken",
        str(time.time() - start),
    )
    return {
        "statusCode": 200,
        "headers": {"Content-Type": "application/xml"},
        "body": str(response),
    }


def autogen_process_gathered_input_flow(event):
    """
    This flow processes the gathered input
    """
    start = time.time()
    response = VoiceResponse()
    body_attribute = parse_twilio_body(event.get("body"))
    conversation_sid = body_attribute.get("CallSid")
    print_info_log(conversation_sid, "process_gathered_input_flow : invoked", "")
    caller_phone = body_attribute.get("Caller")
    twilio_phone = body_attribute.get("Called")
    speech_input = body_attribute.get("SpeechResult", None)
    contact_attributes = get_contact_attributes(conversation_sid)

    if not speech_input:
        if contact_attributes.get("last_qus"):
            speech_input = contact_attributes.get("last_qus")
        else:
            if int(contact_attributes.get("noCustomerResponse", 0)) > 2:
                response.say(
                    "With out a response i can not continue, redirecting to agent",
                    voice="Google.en-US-Neural2-F",
                    language="en-US",
                )
                # response.dial("+17183982603")
                response.hangup()
            else:
                response.say(
                    "Sorry I did'nt catch what you said, can you repeat again?",
                    voice="Google.en-US-Neural2-F",
                    language="en-US",
                )
                contact_attributes["noCustomerResponse"] = (
                    int(contact_attributes.get("noCustomerResponse", 0)) + 1
                )
                url = os.environ.get("AUTOGEN_GATHER_HOOK")
                response.redirect(url)
            update_contact_attribute(conversation_sid, contact_attributes)
            print_info_log(
                conversation_sid, "process_gathered_input_flow : response", response
            )
            print_info_log(
                conversation_sid,
                "process_gathered_input_flow : invoked, time taken",
                str(time.time() - start),
            )
            return {
                "statusCode": 200,
                "headers": {"Content-Type": "application/xml"},
                "body": str(response),
            }
    else:
        contact_attributes["last_qus"] = speech_input
        contact_attributes["noCustomerResponse"] = 0

    card_checker_result = get_item_by_primary_key(
        os.environ.get("CARD_CHECKER_TABLE"), "phone_number", twilio_phone
    )
    assigned_framework = card_checker_result.get("framework")
    params = {
        "chat_id": conversation_sid,
        "framework_to_used": assigned_framework,
        "channel_id": "VC",
        "channel_name": "voice",
        "customer_details": {"phone_number": caller_phone},
        "customer_message": speech_input,
    }
    if assigned_framework == "rozie_studio_navigator":
        if "live agent" in speech_input.lower():
            transfer_prompt = Say(
                "Please hold on for a moment. I am transferring you to a live agent now.",
                language="en-US",
                voice="Google.en-US-Neural2-F",
            )
            response.append(transfer_prompt)
            response.append(call_transfer_to_live_agent(caller_phone, conversation_sid))
            print_info_log(conversation_sid, "process_gathered_input_flow : response", response)
            print_info_log(
                conversation_sid,
                "process_gathered_input_flow : completed, time taken",
                str(time.time() - start),
            )
            return {
                "statusCode": 200,
                "headers": {"Content-Type": "application/xml"},
                "body": str(response),
            }
    status, result = sync_post_request(
        url=os.getenv("MIDDLEWARE_SEND_MESSAGE_ULR"),
        data=params,
        headers={"access_token": os.getenv("AUTOGEN_ACCESS_TOKEN")},
    )
    result = parse_middleware_response(result, caller_phone, conversation_sid)
    contact_attributes = get_contact_attributes(conversation_sid)
    print_info_log(conversation_sid, "sync_post_request:AUTOGEN_GET", result)

    if result.get("complete", False):
        if contact_attributes.get("attributes").get("Param_Return") == "LiveAgent":
            response.append(call_transfer_to_live_agent(caller_phone, conversation_sid))
        else:
            for message_part in format_say_message(result.get("follow_up", "")):
                response.append(message_part)
            response.say(
                "Thank you for calling, Goodbye.",
                # "Goodbye.",
                voice="Google.en-US-Neural2-F",
                language="en-US",
            )
            response.hangup()
    else:
        message = result.get("follow_up", "")
        for message_part in format_say_message(message):
            response.append(message_part)
        # response.say(message, voice="Google.en-US-Neural2-F", language="en-US")
        if contact_attributes.get("attributes", {}).get("Param_Return", "") == "LiveAgent":
            response.append(call_transfer_to_live_agent(caller_phone, conversation_sid))
        else:
            response.redirect(os.environ.get("AUTOGEN_GATHER_HOOK"))
    # delete_item(
    #     os.environ.get("SALON_RESULT_PROXY_TABLE"), "contact_id", conversation_sid
    # )
    contact_attributes["requestSent"] = "False"
    contact_attributes["getAgentResponseInvoked"] = "False"
    contact_attributes["noAgentResponse"] = 0
    update_contact_attribute(conversation_sid, contact_attributes)
    print_info_log(conversation_sid, "process_gathered_input_flow : response", response)
    print_info_log(
        conversation_sid,
        "process_gathered_input_flow : completed, time taken",
        str(time.time() - start),
    )
    return {
        "statusCode": 200,
        "headers": {"Content-Type": "application/xml"},
        "body": str(response),
    }


def autogen_error_handling(event):
    """Routed to this flow if twilio encountered any error"""
    start = time.time()
    body_attribute = parse_twilio_body(event.get("body"))
    conversation_sid = body_attribute.get("CallSid")
    print_info_log(conversation_sid, "error_handling : invoked", "")
    contact_attributes = get_contact_attributes(conversation_sid)

    response = VoiceResponse()
    error_type = contact_attributes.get("errorType", "")
    if error_type == "noCustomerResponse":
        message = "With out response i can not continue."
    else:
        message = "Sorry for the inconvenience, we encountered a technical issue."
    issue_prompt = Say(
        message,
        language="en-US",
        voice="Google.en-US-Neural2-F",
    )
    response.append(issue_prompt)

    last_prompt = Say(
        "Thank You for calling; good bye!",
        language="en-US",
        voice="Google.en-US-Neural2-F",
    )
    response.append(last_prompt)
    response.hangup()
    print_info_log(conversation_sid, "error_handling : response", response)
    print_info_log(
        conversation_sid,
        "error_handling : completed, time taken",
        str(time.time() - start),
    )
    return {
        "statusCode": 200,
        "headers": {"Content-Type": "application/xml"},
        "body": str(response),
    }


def format_say_message(input_string, max_length=300):
    for k, v in stylist_pronunciations.items():
        input_string = input_string.replace(k, v)
    words = input_string.split()
    current_line = ""
    result = []

    for word in words:
        if len(current_line) + len(word) + 1 <= max_length:
            current_line += word + " "
        else:
            say_message = Say(
                current_line.strip(),
                language="en-US",
                voice=os.getenv("VOICE", "Google.en-US-Neural2-F"),
            )
            result.append(say_message)
            current_line = word + " "

    if current_line:
        say_message = Say(
            current_line.strip(),
            language="en-US",
            voice=os.getenv("VOICE", "Google.en-US-Neural2-F"),
        )
        result.append(say_message)

    return result

def call_transfer_to_live_agent(phone_number, conversation_id):
    response = VoiceResponse()
    #set contact attribute for connect
    item = get_item_by_primary_key(
        os.environ.get("TWILIO_CONTACT_ATTRIBUTE_TABLE"), "contact_id", conversation_id
    )
    url = os.environ.get("SET_CONNECT_ATTRIBUTE")
    body = {
        "toPhoneNumber": phone_number,
        "attributes": item.get("attributes", {}),
    }
    headers = {
        "Content-Type": "application/json",
        "x-api-key": os.environ.get("SET_CONNECT_ATTRIBUTE_KEY")
    }

    _, result = sync_post_request(url=url, data=body, headers=headers)
    print("#123#", result)

    return Dial(os.environ.get("CONNECT_TRANSFER_NUMBER"))
