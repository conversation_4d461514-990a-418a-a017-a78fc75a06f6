"""
ZURICH AI EXPLAINABILITY ROUTER - FASTAPI ROUTER
AI-driven document highlight generation and explainability for claims
Integrates with existing claim data from Supabase and BAML for LLM orchestration
"""

from fastapi import APIRouter, HTTPException, Request, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
import logging
import json
import time
import uuid
import urllib.parse
from datetime import datetime
import os
import sys

# Import structured logging
from .structured_logger import StructuredLogger

# Supabase integration
from supabase import create_client, Client

# Initialize router and logging
router = APIRouter(prefix="/api/ai-explainability", tags=["AI Explainability"])
logger = logging.getLogger(__name__)
structured_logger = StructuredLogger("ai_explainability")

# Import BAML client for AI-powered highlight generation
try:
    # Add the baml_models directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    baml_path = os.path.join(project_root, 'baml_models')
    if baml_path not in sys.path:
        sys.path.append(baml_path)
    
    from baml_client import b
    logger.info("BAML client imported successfully for explainability")
except ImportError as e:
    logger.error(f"Failed to import BAML client: {e}")
    b = None

# Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("SUPABASE_ANON_KEY")
supabase: Optional[Client] = None

if supabase_url and supabase_key:
    supabase = create_client(supabase_url, supabase_key)
    logger.info("Supabase connected for AI explainability")

# ==================== REQUEST/RESPONSE MODELS ====================

class DocumentHighlight(BaseModel):
    """Individual document highlight with AI-generated explanation"""
    start: int = Field(..., description="Start character position in document")
    end: int = Field(..., description="End character position in document")
    text: str = Field(..., description="Highlighted text")
    color: str = Field(..., description="Hex color code")
    field: str = Field(..., description="Claim field this supports")
    explanation: str = Field(..., description="AI explanation")
    contribution_score: float = Field(..., ge=0, le=1, description="Contribution to decision")
    highlight_type: str = Field(..., description="Type of highlight")

class ColorLegend(BaseModel):
    """Color legend entry explaining what each color means"""
    color: str = Field(..., description="Hex color code")
    meaning: str = Field(..., description="What this color represents")
    examples: List[str] = Field(..., description="Examples of what gets highlighted")

class DocumentExplainability(BaseModel):
    """Complete explainability data for a document"""
    highlights: List[DocumentHighlight]
    color_legend: List[ColorLegend]
    summary: str = Field(..., description="AI summary of important findings")
    confidence_score: float = Field(..., ge=0, le=1, description="Confidence in analysis")
    processing_notes: List[str] = Field(..., description="Analysis notes")

class HighlightsRequest(BaseModel):
    """Request for generating document highlights"""
    claim_reference: str = Field(..., description="Claim reference to analyze")
    document_filename: Optional[str] = Field(None, description="Specific document to analyze")
    include_all_documents: bool = Field(True, description="Analyze all claim documents")

class HighlightsResponse(BaseModel):
    """Response containing AI-generated highlights"""
    success: bool
    claim_reference: str
    documents_analyzed: int
    highlights_generated: int
    explainability_data: List[DocumentExplainability]
    processing_time: float
    error_message: Optional[str] = None

# ==================== HELPER FUNCTIONS ====================

async def fetch_comprehensive_claim_data(claim_reference: str) -> Dict[str, Any]:
    """Fetch complete claim data from Supabase including all analysis levels"""
    if not supabase:
        raise HTTPException(
            status_code=503,
            detail="Supabase not configured - cannot fetch claim data"
        )
    
    try:
        # Fetch main claim record with correct column names
        claims_response = supabase.table('claims')\
            .select('*')\
            .eq('claim_reference', claim_reference)\
            .execute()
        
        if not claims_response.data:
            raise HTTPException(
                status_code=404,
                detail=f"Claim {claim_reference} not found in database"
            )
        
        claim_data = claims_response.data[0]
        
        # Fetch attachments and OCR data
        attachments_response = supabase.table('attachments')\
            .select('*')\
            .eq('claim_reference', claim_reference)\
            .execute()
        
        attachments = attachments_response.data or []
        
        return {
            "claim_data": claim_data,
            "attachments": attachments,
            "email_subject": claim_data.get("email_subject", ""),
            "email_body": claim_data.get("email_body", ""),
            "level01_analysis": json.dumps(claim_data.get("01_level_analysis") or {}),
            "level02_analysis": json.dumps(claim_data.get("02_level_analysis") or {}),
            "level03_analysis": json.dumps(claim_data.get("03_level_analysis") or {}),
            "level04_analysis": json.dumps(claim_data.get("04_level_analysis") or {}),
            "ocr_texts": [att.get("ocr_text", "") for att in attachments if att.get("ocr_text")]
        }
        
    except Exception as e:
        logger.error(f"Error fetching claim data for {claim_reference}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch claim data: {str(e)}"
        )

def prepare_document_text_for_analysis(attachments: List[Dict], filename: str = None) -> List[Dict[str, str]]:
    """Prepare document texts for AI analysis - extract from zurich_ocr_response structure"""
    documents = []
    
    for attachment in attachments:
        try:
            # Get OCR text from the attachment
            ocr_text = attachment.get("ocr_text", "")
            if not ocr_text or not ocr_text.strip():
                logger.info(f"Skipping attachment {attachment.get('id', 'unknown')} - no OCR text available")
                continue
            
            # Parse the OCR JSON structure
            ocr_data = json.loads(ocr_text)
            
            # Get the nested zurich_ocr_response
            zurich_response = ocr_data.get('zurich_ocr_response', {})
            results = zurich_response.get('results', [])
            
            if not results:
                logger.info(f"Skipping attachment {attachment.get('id', 'unknown')} - no results in OCR data")
                continue
            
            # Extract each document from the results
            for result in results:
                if not result.get('success', False):
                    continue
                    
                doc_filename = result.get('filename', f"Document_{len(documents)+1}")
                extracted_text = result.get('extracted_text', '')
                doc_confidence = result.get('confidence', 0.0)
                
                # URL decode the filename for display
                display_filename = urllib.parse.unquote(doc_filename)
                
                # Skip if filtering by specific filename and this doesn't match
                if filename and display_filename != filename:
                    continue
                
                # Skip if no extracted text
                if not extracted_text or not extracted_text.strip():
                    logger.info(f"Skipping document {display_filename} - no extracted text")
                    continue
                
                documents.append({
                    "filename": display_filename,
                    "text": extracted_text,
                    "content_type": attachment.get("content_type", ""),
                    "file_size": len(extracted_text),
                    "confidence": doc_confidence,
                    "original_attachment_id": attachment.get("id")
                })
                logger.info(f"Prepared document for analysis: {display_filename} ({len(extracted_text)} chars)")
                
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Error processing attachment {attachment.get('id', 'unknown')}: {str(e)}")
            continue
    
    logger.info(f"Total documents prepared for analysis: {len(documents)}")
    return documents

# ==================== API ENDPOINTS ====================

@router.post("/highlights", response_model=HighlightsResponse)
async def generate_document_highlights(request: HighlightsRequest):
    """
    Generate AI-driven document highlights and explainability data
    
    This endpoint:
    1. Fetches comprehensive claim data from Supabase
    2. Uses AI (via BAML) to analyze documents and generate highlights
    3. Returns highlights with color legend, explanations, and field mappings
    4. Supports both single document and multi-document analysis
    
    Note: Processing timeout is set to 5 minutes to accommodate AI analysis time
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    # Set a processing timeout (5 minutes for AI processing)
    MAX_PROCESSING_TIME = 300  # 5 minutes in seconds
    
    # Log request start
    structured_logger.log_request_start(
        request_id,
        "/api/ai-explainability/highlights",
        len(str(request.dict()).encode('utf-8'))
    )
    
    try:
        # Check if BAML client is available
        if b is None:
            raise HTTPException(
                status_code=500,
                detail="AI service unavailable - BAML client not properly initialized"
            )
        
        logger.info(f"Processing explainability request for claim {request.claim_reference}")
        
        # Fetch comprehensive claim data
        claim_data = await fetch_comprehensive_claim_data(request.claim_reference)
        

        # Prepare documents for analysis
        documents = prepare_document_text_for_analysis(
            claim_data["attachments"], 
            request.document_filename
        )
        
        if not documents:
            raise HTTPException(
                status_code=404,
                detail=f"No documents found for claim {request.claim_reference}"
            )
        
        explainability_results = []
        total_highlights = 0
        
        # Generate highlights for each document
        for doc in documents:
            try:
                # Check if we're approaching timeout
                elapsed_time = time.time() - start_time
                if elapsed_time > MAX_PROCESSING_TIME - 30:  # Leave 30 seconds buffer
                    logger.warning(f"Approaching timeout, stopping processing after {elapsed_time:.1f}s")
                    break
                    
                logger.info(f"Analyzing document: {doc['filename']} (elapsed: {elapsed_time:.1f}s)")
                
                # Call BAML function for AI-driven highlight generation
                result = b.GenerateDocumentHighlights(
                    document_text=doc["text"],
                    document_filename=doc["filename"],
                    claim_reference=request.claim_reference,
                    email_subject=claim_data["email_subject"],
                    email_body=claim_data["email_body"],
                    level01_analysis=claim_data["level01_analysis"],
                    level02_analysis=claim_data["level02_analysis"],
                    level03_analysis=claim_data["level03_analysis"],
                    level04_analysis=claim_data["level04_analysis"],
                    ocr_texts=claim_data["ocr_texts"]
                )
                
                # Convert BAML result to response model
                explainability_data = DocumentExplainability(
                    highlights=[
                        DocumentHighlight(
                            start=h.start,
                            end=h.end,
                            text=h.text,
                            color=h.color,
                            field=h.field,
                            explanation=h.explanation,
                            contribution_score=h.contribution_score,
                            highlight_type=h.highlight_type
                        ) for h in result.highlights
                    ],
                    color_legend=[
                        ColorLegend(
                            color=cl.color,
                            meaning=cl.meaning,
                            examples=cl.examples
                        ) for cl in result.color_legend
                    ],
                    summary=result.summary,
                    confidence_score=result.confidence_score,
                    processing_notes=result.processing_notes
                )
                
                explainability_results.append(explainability_data)
                total_highlights += len(result.highlights)
                
                logger.info(f"Generated {len(result.highlights)} highlights for {doc['filename']}")
                
            except Exception as e:
                logger.error(f"Error processing document {doc['filename']}: {str(e)}")
                # Continue with other documents, but log the error
                structured_logger.log_error(request_id, f"document_analysis_error", str(e))
        
        processing_time = time.time() - start_time
        
        # Log successful completion
        structured_logger.log_request_complete(
            request_id,
            processing_time * 1000,
            True,
            f"Generated {total_highlights} highlights for {len(documents)} documents"
        )
        
        logger.info(f"Successfully generated {total_highlights} highlights for {len(documents)} documents in {processing_time:.2f}s")
        
        return HighlightsResponse(
            success=True,
            claim_reference=request.claim_reference,
            documents_analyzed=len(documents),
            highlights_generated=total_highlights,
            explainability_data=explainability_results,
            processing_time=processing_time
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"Unexpected error generating highlights: {str(e)}"
        
        logger.error(error_msg)
        structured_logger.log_request_complete(
            request_id,
            processing_time * 1000,
            False,
            error_msg
        )
        
        return HighlightsResponse(
            success=False,
            claim_reference=request.claim_reference,
            documents_analyzed=0,
            highlights_generated=0,
            explainability_data=[],
            processing_time=processing_time,
            error_message=error_msg
        )

@router.get("/health")
async def health_check():
    """Health check for AI explainability service"""
    try:
        status = {
            "service": "ai-explainability",
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "baml_client": "available" if b is not None else "unavailable",
            "supabase_client": "connected" if supabase is not None else "disconnected"
        }
        
        # Test BAML client if available
        if b is not None:
            status["baml_functions"] = ["GenerateDocumentHighlights", "GenerateMultiDocumentHighlights"]
        
        return status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "service": "ai-explainability",
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        )

@router.get("/info")
async def service_info():
    """Get information about the AI explainability service"""
    return {
        "service": "Zurich AI Explainability Service",
        "version": "1.0.0",
        "description": "AI-driven document highlight generation and explainability for insurance claims",
        "features": [
            "Real-time AI highlight generation",
            "Color-coded explainability",
            "Bidirectional field mapping",
            "Multi-document analysis",
            "SHAP/LIME-style explanations"
        ],
        "endpoints": {
            "/highlights": "Generate AI-driven document highlights",
            "/health": "Service health check",
            "/info": "Service information"
        },
        "integration": {
            "supabase": "Real-time claim data fetching",
            "baml": "LLM orchestration and prompt management",
            "openai": "GPT-4o for highlight generation"
        }
    } 