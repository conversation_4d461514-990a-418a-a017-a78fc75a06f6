"""
Module for handling flight details CRUD operations
"""

import json
import os
from datetime import datetime, timedelta
from helpers.logging_helper import get_log_level, set_logging_level, print_info_log
from helpers.decorator_helper import exception_handler
from helpers.dynamo_helper import (
    get_item_by_primary_key,
    put_table_item,
    scan_table_with_filter,
    upsert_item,
    delete_item,
)
from helpers.response_helper import format_response


@exception_handler
def lambda_handler(event, context):
    """
    Lambda handler for flight details crud operation APIs
    """
    set_logging_level(get_log_level())
    print_info_log("", "Flight Details api invoked", event)
    http_method = event.get("httpMethod", "")
    path = event.get("path", "")
    if http_method == "GET" and path == "/get_flights":
        return get_scheduled_flights(event)
    elif http_method == "GET" and path == "/get_flight_status":
        return get_scheduled_flight_details(event)
    elif event.get("detail-type", "") == "Scheduled Event":
        return create_schedule(event)
    
    #to generate 80 days flight schedule
    # generate_80_day_flight_schedule()

    return format_response(400, None, "Invalid request")


def create_schedule(event):
    """
    Create a new flight schedule record in the database
    """
    date_for_schedule = datetime.strptime(event["time"], "%Y-%m-%dT%H:%M:%SZ") + timedelta(days=81)
    # schedule_data = json.loads(event.get("body", "{}"))
    # date_for_schedule = schedule_data.get("date") #2024-10-02, %Y-%m-%d
    # date_for_schedule = datetime.strptime(date_for_schedule, "%Y-%m-%d")
    flights_data = scan_table_with_filter(os.environ["FLIGHT_DETAILS_TABLE"])

    for flight in flights_data:
        schedule_details = flight.get("scheduleConfig")
        if schedule_details.get("scheduleType") == "daily":
            generate_flight_schedule(flight, date_for_schedule, schedule_details)
        elif schedule_details.get("scheduleType") == "weekly":
            day = date_for_schedule.strftime("%A")
            if day.lower() in schedule_details.get("days"):
                generate_flight_schedule(flight, date_for_schedule, schedule_details)
    return format_response(200, schedule_details, "Schedule created successfully")


def generate_flight_schedule(flight_data, date_for_schedule, schedule_config):
    """
    Generate daily flight schedule for a given flight and date
    """
    try:
        schedule_id = (
            "SH"
            + str(date_for_schedule.strftime("%y%m%d"))
            + flight_data.get("flight_id")
        )
        arrival_time = datetime.strptime(schedule_config.get("arrivalTime"), "%H:%M")
        departure_time = datetime.strptime(
            schedule_config.get("departureTime"), "%H:%M"
        )
        arrival_time = datetime.combine(date_for_schedule, arrival_time.time())
        departure_time = datetime.combine(date_for_schedule, departure_time.time())
        setting_config = flight_data.get("settingConfig")
        for r, rv in setting_config.items():
            for c, cv in rv.items():
                setting_config[r][c]["price"] = flight_data.get("basePrice")
        schedule_details = {
            "scheduleId": schedule_id,
            "flight_id": flight_data.get("flight_id"),
            "date": date_for_schedule.strftime("%Y-%m-%d"),
            "arrivalTimestamp": arrival_time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
            "departureTimestamp": departure_time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
            "arrivalAirport": flight_data.get("arrivalAirport"),
            "arrivalAirportTerminal": flight_data.get("arrivalAirportTerminal"),
            "departureAirport": flight_data.get("departureAirport"),
            "departureAirportTerminal": flight_data.get("departureAirportTerminal"),
            "seating": flight_data.get("settingConfig"),
            "flightStatus": "scheduled"
        }

        put_table_item(
            os.environ["FLIGHT_SCHEDULE_TABLE"], schedule_details, "scheduleId"
        )

        return "Success"
    except Exception as error:
        print_info_log("", "Error in generating daily flight schedule", error)
        return "Failed"


def get_scheduled_flights(event):
    """
    Get the list of scheduled flights for a given date range
    """
    query_params = event.get("queryStringParameters", {})
    departure = query_params.get("departure", "")
    # TODO change location to location id
    if departure == "":
        return format_response(400, None, "Please Provide From Location")
    arrival = query_params.get("arrival", "")
    if arrival == "":
        return format_response(400, None, "Please Provide To Location")

    date = query_params.get("date", "")

    filter_expression_dict = {
        "arrivalAirport": arrival,
        "departureAirport": departure,
        "date": date,
        "flightStatus": "scheduled"
    }
    projection_expression_list = [
        "scheduleId",
        "flight_id",
        "date",
        "arrivalTimestamp",
        "departureTimestamp",
        "arrivalAirport",
        "arrivalAirportTerminal",
        "departureAirport",
        "departureAirportTerminal"
    ]

    data = scan_table_with_filter(
        os.environ["FLIGHT_SCHEDULE_TABLE"],
        filter_expression_dict,
        projection_expression_list,
    )

    return format_response(200, data, "Success")

def get_scheduled_flight_details(event):
    """
    This method will return the flight details for a given schedule id
    """
    query_params = event.get("queryStringParameters", {})
    schedule_id = query_params.get("schedule_id", "")
    if schedule_id == "":
        return format_response(400, None, "Please Provide schedule_id")
    data = get_item_by_primary_key(
        os.environ["FLIGHT_SCHEDULE_TABLE"], "scheduleId", schedule_id
    )
    data_to_share = {
        "scheduleId": data.get("scheduleId"),
        "flight_id": data.get("flight_id"),
        "date": data.get("date"),
        "arrivalTimestamp": data.get("arrivalTimestamp"),
        "departureTimestamp": data.get("departureTimestamp"),
        "arrivalAirport": data.get("arrivalAirport"),
        "arrivalAirportTerminal": data.get("arrivalAirportTerminal"),
        "departureAirport": data.get("departureAirport"),
        "departureAirportTerminal": data.get("departureAirportTerminal"),
        "flightStatus": data.get("flightStatus")
    }
    return format_response(200, data_to_share, "Success")

def generate_80_day_flight_schedule():
    day = datetime.now()
    day = day + timedelta(days=1)
    for _ in range(80):
        create_schedule({"time": str(day.strftime("%Y-%m-%dT%H:%M:%SZ"))})
        day = day + timedelta(days=1)
