/* eslint-disable no-case-declarations */
const { scheduleFlightStatus } = require("./staus-helper/flight-schedule-table.js")
/**
 * AWS Lambda handler for managing PNR operations. 
 * This function updates status of diffent flight details 
 
 * @param {object} event - The event object representing the API request, including body and resource path.
 * @returns {object} - An API response object containing the result of the operation or an error message.
 */

const lambda_handler = async (event) => {
	try {
		const scheduleFlightStatusResult =  await scheduleFlightStatus();
	} catch (error) {
		console.log(error)
	}
}

module.exports = {
	lambda_handler,
}
