import json
import os
import requests
import logging
from typing import Dict, Any, Optional
import openai
import dateutil.parser
import pytz
from datetime import datetime, timedelta
from airline_app.core import config
from airline_app.services.base_service import BaseApiService

logger = logging.getLogger(__name__)

class FlightStatusService(BaseApiService):
    def __init__(self):
        super().__init__()
        self.template = self._load_template_response()

    def _load_template_response(self) -> Dict[str, Any]:
        """
        Load the template response from file.
        """
        try:
            # Get the project root directory (two levels up from this file)
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            template_path = os.path.join(project_root, "airline_app/sample_responses", "amadeus_flight_template.json")
            with open(template_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading template response: {str(e)}")
            raise
    
    def get_flight_status(self, carrier_code: str, flight_number: str,
                         scheduled_departure_date: str, use_real_data: bool = True) -> Dict[str, Any]:
        """
        Get flight status from Amadeus API or generate synthetic data.
        
        Args:
            carrier_code: Airline carrier code (e.g., 'AF')
            flight_number: Flight number (e.g., '1234')
            scheduled_departure_date: Scheduled departure date in YYYY-MM-DD format
            use_real_data: Whether to use real API data or generate synthetic data
        
        Returns:
            Flight status data in Amadeus API format
        """
        request_params = {
            'carrier_code': carrier_code,
            'flight_number': flight_number,
            'scheduled_departure_date': scheduled_departure_date
        }

        if not use_real_data:
            logger.info("Using synthetic data for flight status")
            return self._generate_with_openai(self.template, request_params)

        try:
            # Try to get real data from Amadeus
            return self._get_from_amadeus(request_params)
        except Exception as e:
            logger.warning(f"Failed to get real flight status: {str(e)}")
            logger.info("Falling back to synthetic data")
            return self._generate_with_openai(self.template, request_params)

    def _get_from_amadeus(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Get flight status from Amadeus API."""
        endpoint = f"{self.base_url}/v2/schedule/flights"
        
        query_params = {
            'carrierCode': params['carrier_code'],
            'flightNumber': params['flight_number'],
            'scheduledDepartureDate': params['scheduled_departure_date']
        }

        try:
            response = requests.get(
                endpoint,
                headers=self._get_headers(),
                params=query_params
            )
            
            if response.status_code == 401:
                # Token expired, refresh and retry
                self._handle_unauthorized()
                return self._get_from_amadeus(params)
            
            response.raise_for_status()
            data = response.json()
            self._inject_status(data)
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting flight status from Amadeus: {str(e)}")
            raise
    
    def _generate_with_openai(self, template: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate synthetic flight status using OpenAI.
        """
        logger.info("Generating synthetic flight status")
        
        prompt = f"""
        Generate a realistic flight status response based on the following parameters:
        - Carrier Code: {params['carrier_code']}
        - Flight Number: {params['flight_number']}
        - Scheduled Departure Date: {params['scheduled_departure_date']}

        Use this template as a base and modify it to match the parameters:
        {json.dumps(template, indent=2)}

        Instructions:
        1. Update all flight details to match the carrier and flight number
        2. Update dates and times to match the scheduled departure date
        3. Ensure all IDs and references are unique
        4. Maintain the same structure as the template
        5. Make the response realistic and consistent
        6. Include realistic delays and status updates

        Return ONLY the modified JSON without any explanations or markdown formatting.
        """
        
        try:
            response = self.openai_client.chat.completions.create(
                model=config.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a flight status specialist. Your task is to generate realistic flight status data that matches the patterns of real API responses. Return only valid JSON without explanations."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.7,
                response_format={"type": "json_object"}
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            logger.error(f"Error generating synthetic flight status: {str(e)}")
            return template

    # ---- Flight status calculation and injection ----
    def _calculate_flight_status(self, departure_time_str: str, arrival_time_str: Optional[str] = None) -> str:
        """
        Determine flight status based on current EDT time compared to departure and arrival times.
        Statuses: ON TIME, DELAYED, IN AIR, ARRIVED
        """
        try:
            user_timezone = pytz.timezone("America/New_York")
            now = datetime.now(user_timezone)

            dep_time_obj = dateutil.parser.parse(departure_time_str).astimezone(user_timezone)
            arr_time_obj = dateutil.parser.parse(arrival_time_str).astimezone(user_timezone) if arrival_time_str else None

            # Threshold: delayed if flight hasn't departed 30+ minutes past departure time
            delay_threshold = dep_time_obj + timedelta(minutes=30)

            if now < dep_time_obj:
                return "ON TIME"
            elif now >= dep_time_obj and now < delay_threshold:
                return "DELAYED"
            elif arr_time_obj and dep_time_obj <= now < arr_time_obj:
                return "IN AIR"
            elif arr_time_obj and now >= arr_time_obj:
                return "ARRIVED"
            else:
                return "UNKNOWN"
        except Exception as e:
            logger.warning(f"Could not parse times. Departure: {departure_time_str}, Arrival: {arrival_time_str}. Error: {str(e)}")
            return "UNKNOWN"

    def _inject_status(self, data: Dict[str, Any]) -> None:
        """
        Add calculated flight status into the main flight object only.
        """
        if not data.get("data"):
            return

        flight = data["data"][0]
        departure_time = None
        arrival_time = None

        for point in flight.get("flightPoints", []):
            if "departure" in point and "timings" in point["departure"]:
                for timing in point["departure"]["timings"]:
                    if timing.get("value"):
                        departure_time = timing["value"]
                        break
            if "arrival" in point and "timings" in point["arrival"]:
                for timing in point["arrival"]["timings"]:
                    if timing.get("value"):
                        arrival_time = timing["value"]
                        break
            if departure_time and arrival_time:
                break

        if not departure_time:
            return

        status = self._calculate_flight_status(departure_time, arrival_time)

        flight["status"] = status

        if "meta" not in data:
            data["meta"] = {}
        data["meta"]["status"] = status

# Create singleton instance
flight_status_service = FlightStatusService()

def get_flight_status_service():
    """
    Get the singleton flight status service instance.
    """
    return flight_status_service 