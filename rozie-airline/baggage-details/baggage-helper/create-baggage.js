const { ApiResp } = require("../helpers/api-helper.js")
const { putDynamodbTableItem, retrieveDynamoEntry } = require("../helpers/dynamo-helper.js")

/**
 * @function createBaggage
 * @description This function creates a baggage entry in a DynamoDB table based on the provided data.
 * 
 * @param {Object} body - The request body containing baggage details.
 * @param {string} body.pnr_id - The PNR ID for the baggage.
 * @param {number} body.weight - The weight of the baggage.
 * @param {string} body.color - The color of the baggage.
 * @param {string} body.type - The type of baggage (e.g., cabin, checked).
 * @param {string} [body.flight_schedule_id] - The flight schedule ID, optional if not provided.
 * 
 * @returns {Promise<Object>} - The response object indicating the result of the baggage creation.
 * 
 * @throws {Error} - Throws an error if there is an issue during the baggage creation process.
 */

const createBaggage = async (body) => {
    try {
        console.log("function: createBaggage body:", body)
        const { pnr_id, weight, color, type, flight_schedule_id } = body

        if (!pnr_id) {
            return ApiResp({ status: "Error", message: "PNR ID is missing" }, 200)
        }

        let temp_flight_schedule_id = flight_schedule_id

        if (!temp_flight_schedule_id) {
            const pnrDetails = await retrieveDynamoEntry(process.env.PNR_TABLE_NAME, "pnr_id", pnr_id)

            if (pnrDetails && pnrDetails.flight_schedule_id) {
                temp_flight_schedule_id = pnrDetails.flight_schedule_id
            } else {
                return ApiResp({ status: "Error", message: "Unable to find flight schedule id" }, 404)
            }
        }

        const timestamp = Date.now().toString(36)
        const baggage_id = timestamp.slice(-5)
        const baggage_status = "Checked-In"
        const created_date = new Date().toISOString()

        const baggageData = {
            baggage_id,
            pnr_id,
            baggage_status,
            created_date,
            weight,
            color,
            type,
            flight_schedule_id: temp_flight_schedule_id
        }

        const response = await putDynamodbTableItem(process.env.BAGGAGE_TABLE_NAME, baggageData)

        console.log("function: createBaggage response:", response)

        if (response) {
            const responseBody = {
                baggage_id,
                pnr_id,
                baggage_status,
                created_date,
                weight,
                color,
                type,
                flight_schedule_id: temp_flight_schedule_id
            }
            return ApiResp({ status: "Success", message: "Baggage created successfully", response: responseBody }, 200)
        } else {
            return ApiResp({ status: "Error", message: "Internal server error while creating baggage" }, 500)
        }
    } catch (error) {
        console.error("Error in createBaggage:", error)
        return ApiResp({ status: "Error", message: "Internal server error while creating baggage" }, 500)
    }
}

module.exports = {
    createBaggage
}