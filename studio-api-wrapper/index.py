import requests
import os
import json
import traceback
from helpers.logging_helper import (
    get_log_level,
    set_logging_level,
    print_info_log,
)
from helpers.decorator_helper import exception_handler
from helpers.baggage_status_wrapper import baggage_wrapper_api, baggage_wrapper_api_info
from helpers.case_status_wrapper import case_status_wrapper, case_status_wrapper_info
from helpers.click_to_connect_wrapper import click_to_connect_wrapper, click_to_connect_wrapper_info

@exception_handler
def lambda_handler(event, context):
    try:
        set_logging_level(get_log_level())
        print_info_log("", "Invoked, Event", event)
        if event["resource"] == "/SendOTPToPhone":
            body = json.loads(event["body"])
            phoneNumber = body["concepts"]["PhoneNumber"]["PhoneNumber"]
            url = (
                "https://ul9clbm6vl.execute-api.ca-central-1.amazonaws.com/dev/sendOtp"
            )
            payload = json.dumps({"recipient_phone": phoneNumber, "flag": "sms"})
            headers = {
                "x-api-key": "vQPMvPdpCH737uICUJPz86LcLmerIJld9wHXDac1",
                "Content-Type": "application/json",
            }
            response = requests.request("POST", url, headers=headers, data=payload)
            if response.status_code == 200:
                result = {
                    "statusCode": 200,
                    "body": json.dumps(
                        {
                            "api_result_type": "success",
                            "concepts": {"Status": {"Status": "success"}},
                        }
                    ),
                }
                print_info_log("","result",result)
                return result
            result = {
                "statusCode": 400,
                "body": json.dumps(
                    {
                        "api_result_type": "failure",
                        "concepts": {"Status": {"Status": "failure"}},
                    }
                ),
            }
            print_info_log("", "result", result)
            return result
        if event["resource"] == "/SendOTPToPhone/info":
            return {
                "statusCode": 200,
                "body": json.dumps(
                    {
                        "api_name": "Send OTP",
                        "api_description": "This API sends OTP.",
                        "input_concepts": {
                            "PhoneNumber": "true",
                        },
                        "output_concepts": {"Status": "true"},
                    }
                ),
            }
        if event["resource"] == "/ValidateOTPForPhone":
            body = json.loads(event["body"])
            phoneNumber = body["concepts"]["PhoneNumber"]["PhoneNumber"]
            otp = body["concepts"]["OTP"]["OTP"]
            url = "https://ul9clbm6vl.execute-api.ca-central-1.amazonaws.com/dev/validateOtp"

            payload = json.dumps({"recipient_phone": phoneNumber, "otp": otp})
            headers = {
                "x-api-key": "vQPMvPdpCH737uICUJPz86LcLmerIJld9wHXDac1",
                "Content-Type": "application/json",
            }
            response = requests.request("POST", url, headers=headers, data=payload)
            if response.status_code == 200:
                response_json = response.json()
                if response_json["message"] == "OTP is valid":
                    status = "valid"
                elif response_json["message"] == "OTP is invalid":
                    status = "invalid"
                else:
                    status = "expired"
                result = {
                    "statusCode": 200,
                    "body": json.dumps(
                        {
                            "api_result_type": "success",
                            "concepts": {"Status": {"Status": status}},
                        }
                    ),
                }
                print_info_log("","result",result)
                return result
            result = {
                "statusCode": 400,
                "body": json.dumps(
                    {
                        "api_result_type": "invalid",
                        "concepts": {"Status": {"Status": "invalid"}},
                    }
                ),
            }
            print_info_log("", "result", result)
            return result
        if event["resource"] == "/ValidateOTPForPhone/info":
            return {
                "statusCode": 200,
                "body": json.dumps(
                    {
                        "api_name": "Validate OTP",
                        "api_description": "This API Validates the OTP.",
                        "input_concepts": {"PhoneNumber": "true", "OTP": "true"},
                        "output_concepts": {"Status": "true"},
                    }
                ),
            }
        if event["resource"] == "/GetCustomerProfile":
            body = json.loads(event["body"])
            phoneNumber = body["concepts"].get("PhoneNumber", {}).get("PhoneNumber", "")
            customerId = body["concepts"].get("CustomerId", {}).get("CustomerId", "")
            url = (
                "https://6tvdvyr6jl.execute-api.ca-central-1.amazonaws.com/dev/getCustomerProfile"
            )
            payload = json.dumps(
                {"phone_number": phoneNumber, "customer_id": customerId}
            )
            headers = {
                "x-api-key": "zE2qnbBkv38CHG2Bwj4vs8B1njD8kIua5vKsfhnn",
                "Content-Type": "application/json",
            }
            response = requests.request("POST", url, headers=headers, data=payload)
            if response.status_code == 200:
                response_json = response.json()
                print_info_log("","INFO", response_json)
                if response_json["message"] == "profile found":
                    status = "success"
                else:
                    status = "failure"
                result = {
                    "statusCode": 200,
                    "body": json.dumps(
                        {
                            "api_result_type": "success",
                            "concepts": {
                                "Status": {"Status": status},
                                "PhoneNumber": {
                                    "PhoneNumber": (
                                        response_json["data"]["phone_number"]
                                        if status == "success"
                                        else ""
                                    )
                                },
                                "CustomerId": {
                                    "CustomerId": (
                                        response_json["data"]["customer_id"]
                                        if status == "success"
                                        else ""
                                    )
                                },
                            },
                        }
                    ),
                }
                print_info_log("","result",result)
                return result
            result = {
                "statusCode": 400,
                "body": json.dumps(
                    {
                        "api_result_type": "failure",
                        "concepts": {
                            "Status": {"Status": "failure"},
                            "PhoneNumber": {"PhoneNumber": ""},
                            "CustomerId": {"CustomerId": ""},
                        },
                    }
                ),
            }
            print_info_log("", "result", result)
            return result
        if event["resource"] == "/GetCustomerProfile/info":
            return {
                "statusCode": 200,
                "body": json.dumps(
                    {
                        "api_name": "Customer profile API",
                        "api_description": "This API returns the customer profile.",
                        "input_concepts": {
                            "PhoneNumber": "false",
                            "CustomerId": "false",
                        },
                        "output_concepts": {
                            "Status": "true",
                            "PhoneNumber": "false",
                            "CustomerId": "false",
                        },
                    }
                ),
            }
        if event["resource"] == "/GetBaggageStatus":
            return baggage_wrapper_api(event)
        if event["resource"] == "/GetBaggageStatus/info":
            return baggage_wrapper_api_info()        
        if event["resource"] == "/GetCaseStatus":
            return case_status_wrapper(event)
        if event["resource"] == "/GetCaseStatus/info":
            return case_status_wrapper_info()
        if event["resource"] == "/ClickToConnect":
            return click_to_connect_wrapper(event)
        if event["resource"] == "/ClickToConnect/info":
            return click_to_connect_wrapper_info()
    except Exception as exception:
        print_info_log("", "Error", str(traceback.format_exc()))
        return {
            "statusCode": 400,
            "body": json.dumps({"api_result_type": "failure", "concepts": {}}),
        }
