<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claim Tracker - Simple Timeline</title>
    <link rel="stylesheet" href="style.css">
    <!-- Supabase Client Library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="claim-header">
            <div class="claim-search">
                <h1>Claim Tracker</h1>
                <div class="search-container">
                    <input type="text" id="claimReferenceInput" placeholder="Enter claim reference (e.g., CLM-85228383)" value="CLM-85228383">
                    <button id="trackClaimBtn" class="track-btn">Track Claim</button>
                    <button id="zendeskSupportBtn" class="support-btn" title="Open Support Dashboard">
                        🎫 Support
                    </button>
                </div>
                <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                    <span>Loading claim information...</span>
                </div>
            </div>
            <div class="claim-info" id="claimInfoSection" style="display: none;">
                <div class="claim-details" id="claimDetails">
                    <!-- Claim details will be populated dynamically -->
                </div>
            </div>
            
            <!-- Progress Bar -->
            <div class="progress-container">
                <div class="progress-header">
                    <span class="progress-text">Progress</span>
                    <span class="progress-percentage" id="progressPercentage">30%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </header>

        <!-- Timeline Section -->
        <main class="timeline-section" id="timelineSection" style="display: none;">
            <h2>Process Timeline</h2>
            <div class="timeline" id="timeline">
                <!-- Timeline items will be populated by JavaScript -->
            </div>
        </main>

        <!-- Error Section -->
        <div class="error-section" id="errorSection" style="display: none;">
            <div class="error-card">
                <h3>⚠️ Claim Not Found</h3>
                <p id="errorMessage">Please check your claim reference and try again.</p>
                <button id="retryBtn" class="retry-btn">Try Again</button>
            </div>
        </div>



        <!-- Detail Modal -->
        <div class="modal-overlay" id="modalOverlay">
            <div class="modal">
                <div class="modal-header">
                    <h3 id="modalTitle">Step Details</h3>
                    <button class="modal-close" id="modalClose">&times;</button>
                </div>
                <div class="modal-content" id="modalContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>