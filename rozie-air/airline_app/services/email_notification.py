from airline_app.services.pnr import get_pnr_details_by_pnr_id
from airline_app.services.customer import get_customer_details_by_id
from airline_app.helpers.sendgrid_helper import send_email

from airline_app.common.constants import BCC_EMAIL


def send_email_boarding_pass(pnr_id, customer_id):
    customer_details = get_customer_details_by_id(customer_id)
    if not customer_details:
        return None
    pnr_details = get_pnr_details_by_pnr_id(pnr_id)
    if not pnr_details:
        pnr_details = pnr_details[0]
        return None
    
    body = f"""
    <html>
    <body>
        <h1>Boarding Pass</h1>
        <p>Dear {customer_details['first_name']} {customer_details['last_name']},</p>
        <p>Your boarding pass details are as follows:</p>
            {pnr_details}
        <p>Enjoy your flight!</p>
    </body>
    </html>
    """
    bcc_list = BCC_EMAIL
    if customer_details["email"] in bcc_list:
        bcc_list.remove(customer_details["email"])
    email_config = {
        "reference_id": pnr_id+customer_id,
        "from": "Nina<<EMAIL>>",
        "to": customer_details["email"],
        "bcc": bcc_list,
        "subject": "Boarding Pass",
        "html_body": body,
        "send_separate": True
    }
    send_email(email_config)
    return {"status": "Email sent successfully"}

def send_email_prohibited_item_doc_link(customer_id):
    customer_details = get_customer_details_by_id(customer_id)
    if not customer_details:
        return None
    body = f"""
    <html>
    <body>
        <h1>Prohibited Item Document</h1>
        <p>Dear {customer_details['first_name']} {customer_details['last_name']},</p>
        <p>Please find the prohibited item document attached for your reference.</p>
        <p>
            <a href="https://www.philippineairlines.com/ph/en/before-you-fly/baggage-information/restricted-items.html" target="_blank">
                Prohibited and Restricted Items - Philippine Airlines
            </a>
        </p>
        <p>Thank you for your cooperation.</p>
    </body>
    </html>
    """
    bcc_list = BCC_EMAIL
    if customer_details["email"] in bcc_list:
        bcc_list.remove(customer_details["email"])
    email_config = {
        "reference_id": customer_id,
        "from": "Nina<<EMAIL>>",
        "to": customer_details["email"],
        "bcc": bcc_list,
        "subject": "Prohibited Item Document",
        "html_body": body,
        "send_separate": True
    }
    send_email(email_config)
    return {"status": "Email sent successfully"}

def send_email_ticket(pnr_id):
    pnr_details = get_pnr_details_by_pnr_id(pnr_id)
    if not pnr_details:
        return None
    pnr_details = pnr_details[0]
    customer_id = pnr_details["customer_id"]
    customer_details = get_customer_details_by_id(customer_id)
    if not customer_details:
        return None
    
    body = f"""
    <html>
    <body>
        <h1>Flight Ticket</h1>
        <p>Dear {customer_details['first_name']} {customer_details['last_name']},</p>
        <p>Your ticket details are as follows:</p>
            {pnr_details}
        <p>Enjoy your flight!</p>
    </body>
    </html>
    """
    bcc_list = BCC_EMAIL
    if customer_details["email"] in bcc_list:
        bcc_list.remove(customer_details["email"])
    email_config = {
        "reference_id": pnr_id+customer_id,
        "from": "Nina<<EMAIL>>",
        "to": customer_details["email"],
        "bcc": bcc_list,
        "subject": "Boarding Pass",
        "html_body": body,
        "send_separate": True
    }
    send_email(email_config)
    return {"status": "Email sent successfully"}
