"""
Helper module for formatting Lambda responses.
"""

import json
from decimal import Decimal

class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)

def format_response(status_code, body, message=None):
    """
    Helper function to format the Lambda response.

    Args:
        status_code (int): HTTP status code of the response.
        body (dict or list or str): The body of the response, typically in JSON format.
        message (str, optional): An optional message to include in the response. Defaults to None.

    Returns:
        dict: The formatted response ready to be returned from the Lambda function.
    """
    response = {
        "statusCode": status_code,
        "body": json.dumps({
            "message": message,
            "data": body
        }, cls=DecimalEncoder),
        "headers": {
            "Content-Type": "application/json"
        }
    }
    return response
