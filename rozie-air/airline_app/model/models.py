from pydantic import BaseModel
from typing import List, Optional, Dict


class FlightDetails(BaseModel):
    flight_number: str
    airline: str
    departure_airport: str
    arrival_airport: str
    date: str
    time: str


class Passenger(BaseModel):
    first_name: str
    last_name: str
    seat: str
    passenger_class: str
    checked_in: bool
    additional_services: Optional[List[str]]
    price: Optional[int]


class PNR(BaseModel):
    pnr: str
    booking_date: str
    payment_status: str
    flight_details: FlightDetails
    passengers: List[Passenger]


class Customer(BaseModel):
    customer_id: str
    first_name: str
    last_name: str
    date_of_birth: Optional[str] = ""
    gender: Optional[str] = ""
    email: str
    phone_number: str


class ActivePNRDetails(BaseModel):
    customer_details: Customer
    active_pnrs: list[PNR]


# Pydantic models
class SeatConfig(BaseModel):
    window: Optional[List[str]] = []
    middle: Optional[List[str]] = []
    aisle: Optional[List[str]] = []


class ClassConfig(BaseModel):
    base_price: float
    seats: SeatConfig
    rows: List[int]


class FlightRequest(BaseModel):
    flight_id: str
    airline: str
    arrival_airport: str
    departure_airport: str
    flight_number: str
    seat_config: Dict[str, ClassConfig]

class CaseCreation(BaseModel):
    customer_id: str
    case_title: str
    case_description: str
    case_status: str
    case_notify_via: str

class BoardingPassEmailNotification(BaseModel):
    customer_id: str
    pnr_id: str