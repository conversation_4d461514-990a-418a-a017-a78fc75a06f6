This is a test text file for testing the override functionality.

The override_native_extraction option should force this TXT file to be processed through OCR engines instead of native text extraction.

This allows users to apply OCR processing configurations (like Gemini Vision, Google Document AI, or Tesseract) to text files when needed.

Key features:
- Force OCR processing for TXT files
- Use specified OCR engine configuration
- Apply post-processing and preprocessing
- Bypass native text extraction

Test scenarios:
1. Normal processing (override_native_extraction: false) - should use native extraction
2. Override processing (override_native_extraction: true) - should use OCR engine
