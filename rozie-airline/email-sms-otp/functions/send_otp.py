import os
from random import randint
from time import time
import hashlib
import json
from helpers.decorator_helper import exception_handler
from helpers.logging_helper import print_info_log
from helpers.response_helper import format_response
from helpers.dynamo_helper import (
    put_table_item,
    update_table_item,
    get_resource_item_by_partition_key,
    get_resource_item_by_primary_key
)

from .send_email import send_email_handler
from .send_sms import send_sms_handler

OTP_TABLE = os.environ["OTP_TABLE"]
enable_logs = os.environ.get("ENABLE_LOGS")
PINPOINT_APPLICATION_ID = os.environ.get("PINPOINT_ID")
CONFIG_TABLE = os.environ.get("CONFIG_TABLE")


def generate_hashed_code():
    """
    Creates an OTP and encrypts it.

    Returns:
        tuple: A tuple containing the plain OTP and its hashed value.
    """
    code = randint(100001, 999999)
    hashed_code = hashlib.md5(str(code).encode()).hexdigest()
    return code, hashed_code


def put_otp_in_table(Id, hashed_code):
    """Function to put the hashed OTP value into table with TTL.

    Args:
        Id (string): phone number/email of the user. Can be a combination of both
        hashed_code (string): Hashed value of OTP.
    """
    code_expire_at = int(time()) + 600
    item_dict = {
        "id": Id,
        "hashedCode": hashed_code,
        "expiresAt": code_expire_at,
    }

    put_result, key_dict = put_table_item(
        OTP_TABLE,
        item_dict,
        "id",
        None,
    )
    if put_result == "Update":
        result = update_table_item(OTP_TABLE, key_dict, item_dict)
        print_info_log(
            "",
            "OTP Updated",
            result
        )
    else:
        print_info_log(
            "",
            "OTP Inserted",
            ""
        )


def send_otp_to_sms(event):
    """Function to send the otp to customer

    Args:
        phone_number (string): phone number of caller
        code (string): value of OTP code
    """
    code = event["code"]
    msg = f"Your Authentication OTP is : {code}"
    event["message"] = msg
    result = send_sms_handler(event)
    print_info_log(
        "",
        "Send SMS, Result",
        result
    )


def send_otp_to_email(event):
    code = event["code"]
    msg = f"Your Authentication OTP is : {code}"
    event["html_body"] = msg
    event["subject"] = "OTP"
    if event:
        result = send_email_handler(event)
        print_info_log(
            "",
            "Send EMAIL, Result",
            result
        )
    else:
        print_info_log(
            "",
            "Send EMAIL, Result",
            "Phone number not found in DynamoDB",
        )


@exception_handler
def send_otp_handler(event):
    """
    This function is used to generate an OTP, temporarily store it in the database,
    and send it to the user's email or phone number or both.

    Args:
        event (dict): The event data.
        context (dict): The context data.
    """
    print_info_log(
        "",
        "generate_otp invoked, Event",
        event
    )    
    send_sms_flag = False
    send_email_flag = False
    Id = None
    if "recipient_phone" in event and "to" in event:
        if "flag" in event:
            if event["flag"] == "both":
                Id = event["recipient_phone"]
                send_sms_flag = True
                send_email_flag = True
            elif event["flag"] == "sms":
                Id = event["recipient_phone"]
                send_sms_flag = True
            else:
                Id = event["to"][0]
                send_email_flag = True
        else:
            Id = event["recipient_phone"]
            send_sms_flag = True
            send_email_flag = True
    elif "recipient_phone" in event:
        Id = event["recipient_phone"]
        send_sms_flag = True
    else:
        Id = event["to"][0]
        send_email_flag = True
        

    # phone_number = body["recipient_phone"] or body["to"][0]
    global_config = get_resource_item_by_partition_key(
    CONFIG_TABLE, "organizationId", "1"
    )
    
    # Get SMS and Email flags from global config
    # send_sms_flag = body.get("sendSMS")
    # send_email_flag = body.get("sendEmail")
    
    # Generate OTP and hash
    code, hashed_code = generate_hashed_code()
    event["code"] = code
    # Store OTP in table
    put_otp_in_table(Id, hashed_code)

    # Send OTP via SMS if required
    if send_sms_flag:
        send_otp_to_sms(event)

    # Send OTP via Email if required
    if send_email_flag:
        send_otp_to_email(event)

    print_info_log(
        "",
        "generate_otp completed",
        "OTP sent successfully"
    )

    # return {"statusCode": 200}
    response = format_response(200, {}, "OTP sent successfully!")
    return response
