"""
This is helper file for pinpoint service.
"""

import boto3
from botocore.exceptions import ClientError
from helpers.logging_helper import log_exception

pinpoint_client = boto3.client("pinpoint")


def send_sms_via_pinpoint(application_id, origination_number, destination_number, message):
    """
    send sms to customer number
    """
    try:
        response = pinpoint_client.send_messages(
            ApplicationId=application_id,
            MessageRequest={
                "Addresses": {destination_number: {"ChannelType": "SMS"}},
                "MessageConfiguration": {
                    "SMSMessage": {
                        "Body": message,
                        "MessageType": "PROMOTIONAL",
                        "OriginationNumber": origination_number,
                    }
                },
            },
        )
        return response["MessageResponse"]["Result"][destination_number]["MessageId"]
    except ClientError:
        log_exception("Couldn't send message.")
        return {
            "statusCode": 400
        }
