###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
from typing import Dict, List, Optional, Union, cast
from typing_extensions import Literal

import baml_py

from . import _baml
from .types import Checked, Check


class LlmResponseParser:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeClaimLevel01(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.Level01Analysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AnalyzeClaimLevel01",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.Level01Analysis, parsed)
    
    def AnalyzeCoverageLevel02(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.Level02CoverageAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AnalyzeCoverageLevel02",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.Level02CoverageAnalysis, parsed)
    
    def AssessDataCompleteness(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.DataCompletenessAssessment:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AssessDataCompleteness",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.DataCompletenessAssessment, parsed)
    
    def AssessPriorityAndRisk(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.PriorityRiskAssessment:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AssessPriorityAndRisk",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.PriorityRiskAssessment, parsed)
    
    def ClassifyZurichEmail(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.ZurichEmailClassificationResult:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ClassifyZurichEmail",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.ZurichEmailClassificationResult, parsed)
    
    def ClassifyZurichEmailFallback(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.ZurichEmailClassificationResult:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ClassifyZurichEmailFallback",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.ZurichEmailClassificationResult, parsed)
    
    def EnhanceWithSparkNLP(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.SparkNlpEnhancedData:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "EnhanceWithSparkNLP",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.SparkNlpEnhancedData, parsed)
    
    def ExtractLevel03FaultFactors(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.LiabilityFactorExtraction:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ExtractLevel03FaultFactors",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.LiabilityFactorExtraction, parsed)
    
    def ExtractLevel04QuantumDetails(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.QuantumDamageExtraction:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ExtractLevel04QuantumDetails",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.QuantumDamageExtraction, parsed)
    
    def ExtractResume(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.Resume:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ExtractResume",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.Resume, parsed)
    
    def GenerateDocumentHighlights(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> List[_baml.types.ColorLegend]:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "GenerateDocumentHighlights",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(List[_baml.types.ColorLegend], parsed)
    
    def GenerateMultiDocumentHighlights(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> List[_baml.types.ColorLegend]:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "GenerateMultiDocumentHighlights",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(List[_baml.types.ColorLegend], parsed)
    
    def ResearchCanadianLegalPrecedents(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.CanadianLegalAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ResearchCanadianLegalPrecedents",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.CanadianLegalAnalysis, parsed)
    


class LlmStreamParser:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeClaimLevel01(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.Level01Analysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AnalyzeClaimLevel01",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.Level01Analysis, parsed)
    
    def AnalyzeCoverageLevel02(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.Level02CoverageAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AnalyzeCoverageLevel02",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.Level02CoverageAnalysis, parsed)
    
    def AssessDataCompleteness(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.DataCompletenessAssessment:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AssessDataCompleteness",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.DataCompletenessAssessment, parsed)
    
    def AssessPriorityAndRisk(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.PriorityRiskAssessment:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AssessPriorityAndRisk",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.PriorityRiskAssessment, parsed)
    
    def ClassifyZurichEmail(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.ZurichEmailClassificationResult:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ClassifyZurichEmail",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.ZurichEmailClassificationResult, parsed)
    
    def ClassifyZurichEmailFallback(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.ZurichEmailClassificationResult:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ClassifyZurichEmailFallback",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.ZurichEmailClassificationResult, parsed)
    
    def EnhanceWithSparkNLP(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.SparkNlpEnhancedData:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "EnhanceWithSparkNLP",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.SparkNlpEnhancedData, parsed)
    
    def ExtractLevel03FaultFactors(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.LiabilityFactorExtraction:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ExtractLevel03FaultFactors",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.LiabilityFactorExtraction, parsed)
    
    def ExtractLevel04QuantumDetails(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.QuantumDamageExtraction:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ExtractLevel04QuantumDetails",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.QuantumDamageExtraction, parsed)
    
    def ExtractResume(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.Resume:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ExtractResume",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.Resume, parsed)
    
    def GenerateDocumentHighlights(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> List[_baml.partial_types.ColorLegend]:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "GenerateDocumentHighlights",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(List[_baml.partial_types.ColorLegend], parsed)
    
    def GenerateMultiDocumentHighlights(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> List[_baml.partial_types.ColorLegend]:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "GenerateMultiDocumentHighlights",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(List[_baml.partial_types.ColorLegend], parsed)
    
    def ResearchCanadianLegalPrecedents(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.CanadianLegalAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ResearchCanadianLegalPrecedents",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.CanadianLegalAnalysis, parsed)
    


__all__ = ["LlmResponseParser", "LlmStreamParser"]