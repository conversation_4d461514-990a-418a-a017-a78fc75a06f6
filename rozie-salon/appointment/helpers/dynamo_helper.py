"""
This module contains DynamoDB helper functions used in the lambda functions.

Functions:
 - put_record_into_dynamodb
 - get_item_by_primary_key
 - update_ddb_table_item
"""

import boto3
from boto3.dynamodb.conditions import Key, Attr
from botocore.exceptions import <PERSON>lient<PERSON><PERSON><PERSON>
from .logging_helper import print_info_log, print_error_log

dynamodb = boto3.resource("dynamodb")


def get_item_by_keys(table_name, primary_key_id, primary_key_value, sort_key_id, sort_key_value):
    """
    Gets an item from the specified DynamoDB table using both primary key and sort key. 

    Args:
        table (string): The name of the DynamoDB table.
        primary_key_id (string): The primary key attribute name of the item.
        primary_key_value (string): The primary key value of the item.
        sort_key_id (string, optional): The sort key attribute name of the item.
        sort_key_value (string, optional): The sort key value of the item.

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    table = dynamodb.Table(table_name)
    try:
        key = {primary_key_id: primary_key_value, sort_key_id: sort_key_value}
        response = table.get_item(Key=key)
        print("response 123",response)
        return response.get("Item", {})
    except Exception as error:
        print_error_log("", error)
        raise error



def update_ddb_table_item(table_name, key_dict, item_dict):
    """
    update a specific item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        key_dict (dict): key dictionary
        item_dict (dict): dictionary of attribute and value to update

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)
        for _key in key_dict:
            del item_dict[_key]

        update_expression = ",".join(
            list(map(lambda key: f"{key} = :{key}", list(item_dict.keys())))
        )  # converts keys in 'key = :key' format
        expression_attribute_values = {}
        for key in item_dict:
            expression_attribute_values[f":{key}"] = item_dict[key]
        param = {
            "Key": key_dict,
            "UpdateExpression": f"SET {update_expression}",
            "ExpressionAttributeValues": expression_attribute_values,
        }
        table.update_item(**param)
        return "Success"
    except Exception as exception:
        print_error_log("", "Error while putting item from table")
        print_error_log("", exception)
        return "Error"


def put_table_item(table_name, item_dict, primary_key, sort_key=None):
    """
    Gets an item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        item_dict (dict): dictionary of attribute and value to update
        primary_key: "primary key to check if item exists"
        sort_key: "sort key to check if item exists"

    Returns:
        String : A string indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)
        condition_expression = f"attribute_not_exists({primary_key})"

        if sort_key:
            condition_expression = f"attribute_not_exists({primary_key}) and attribute_not_exists({sort_key})"
        param = {"Item": item_dict, "ConditionExpression": condition_expression}
        table.put_item(**param)
        return "Success", {}
    except ClientError as exception:
        if exception.response["Error"]["Code"] == "ConditionalCheckFailedException":
            key_dict = {primary_key: item_dict[primary_key]}
            if sort_key:
                key_dict[sort_key] = item_dict[sort_key]
            print_error_log("", "key found in table")
            return "Update", key_dict
        print_error_log("", "Error while putting in table")
        return "Error", {}


def upsert_item(table_name, item_dict, primary_key, sort_key=None):
    """
    Updated item is already exists and creates if not.

    Args:
        table_name (string): The DynamoDB table name.
        item_dict (dict): dictionary of attribute and value to update
        primary_key: "primary key to check if item exists"
        sort_key: "sort key to check if item exists"

    Returns:
        String : A string indicating the success or failure of the operation.
    """

    status, key_dict = put_table_item(table_name, item_dict, primary_key, sort_key)

    if status == "Update":
        status = update_ddb_table_item(table_name, key_dict, item_dict)

    return status


def query_table_with_filter(
    table_name, branch_id, start_time, end_time, staff_id=None
):
    """
    Query the DynamoDB table with branch_id as the partition key and 
    start_time_client_id as the sort key.

    Args:
        table_name (string) : table name
        branch_id (string) : the ID of the branch to filter.
        start_time (string) : the lower bound for the start time (ISO 8601 string).
        end_time (string) : the upper bound for the end time (ISO 8601 string).
        staff_id (string, optional) : the ID of the staff (optional).

    Returns:
        list: A list of items from the DynamoDB table that match the key conditions.
    """
    try:
        table = dynamodb.Table(table_name)


        # Query based on branch_id and range filter on start_time_client_id
        key_condition = Key("branch_id").eq(branch_id) & \
                        Key("start_time_client_id").between(f"{start_time}_", f"{end_time}_")
        
        # Query parameters
        params = {
            "KeyConditionExpression": key_condition
        }


        # Optional filter for staff_id
        if staff_id:
            filter_expression = Attr("staffId").eq(staff_id)
            params["FilterExpression"] = filter_expression

        # Query the table
        items = []
        while True:
            response = table.query(**params)

            items.extend(response.get("Items", []))
            last_evaluated_key = response.get("LastEvaluatedKey")

            if not last_evaluated_key:
                break
            params["ExclusiveStartKey"] = last_evaluated_key

        return {"message": "Data fetched successfully", "data": items} if items else {"message": "appointment not found", "data": None}

    except Exception as error:
        raise error


def delete_item(table_name, key_dict):
    """
    Delete an item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        key_dict (dict): A dictionary containing the primary key(s) of the item to delete.

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)
        table.delete_item(Key=key_dict)
        return "Success"
    except Exception as error:
        print_error_log("", error)
        return "Error"