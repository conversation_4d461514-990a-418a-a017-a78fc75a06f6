from fastapi import APIRouter, HTTPException, Depends, Body
from typing import Dict, Any, List
import logging
from services.flight_order_service import get_flight_order_service

router = APIRouter()
logger = logging.getLogger(__name__)

class FlightOffer:
    def __init__(self, **data):
        self.id = data.get('id')
        self.validatingAirlineCodes = data.get('validatingAirlineCodes', [])
        self.itineraries = data.get('itineraries', [])
        self.price = data.get('price', {})
        self.travelerPricings = data.get('travelerPricings', [])

class Traveler:
    def __init__(self, **data):
        self.id = data.get('id')
        self.dateOfBirth = data.get('dateOfBirth')
        self.name = data.get('name', {})
        self.gender = data.get('gender')
        self.contact = data.get('contact', {})
        self.documents = data.get('documents', [])

class FlightOrderData:
    def __init__(self, **data):
        self.type = data.get('type')
        self.flightOffers = [FlightOffer(**offer) for offer in data.get('flightOffers', [])]
        self.travelers = [Traveler(**traveler) for traveler in data.get('travelers', [])]
        self.remarks = data.get('remarks')
        self.ticketingAgreement = data.get('ticketingAgreement')
        self.contacts = data.get('contacts', [])

class FlightOrderRequest:
    def __init__(self, **data):
        self.data = FlightOrderData(**data.get('data', {}))

@router.post("/flight-order")
async def create_flight_order(
    payload: Dict[str, Any] = Body(...),
    order_service=Depends(get_flight_order_service)
) -> Dict[str, Any]:
    """
    Create a flight order using a JSON body with 'flightOffers' and 'travelers'.
    """
    try:
        data = payload.get('data', {})
        if not data.get('flightOffers') or not data.get('travelers'):
            raise HTTPException(status_code=400, detail="'flightOffers' and 'travelers' are required.")
        result = order_service.create_order(
            flight_offer=data['flightOffers'][0],
            travelers=data['travelers'],
            remarks=data.get('remarks'),
            ticketing_agreement=data.get('ticketingAgreement'),
            contacts=data.get('contacts')
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))




