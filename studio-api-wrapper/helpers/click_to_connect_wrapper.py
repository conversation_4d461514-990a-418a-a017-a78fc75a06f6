import json
import requests
from .logging_helper import print_info_log


def click_to_connect_wrapper(event):
    """
    This function is used to call the baggage status API and return the result
    """
    body = json.loads(event["body"])
    phone_number = body["concepts"]["PhoneNumber"]["PhoneNumber"]
    username = body["concepts"]["Name"]["Name"]
    url = "https://a851gftt3k.execute-api.ca-central-1.amazonaws.com/dev/click-to-connect"
    payload = json.dumps(
        {
            "toPhoneNumber": phone_number,
            "attributes":{
                "name": username
            }
        }
    )
    headers = {
        "x-api-key": "sjEzgc9jww5gvb7hwTX2F9jgfN1RrDco247fppnA",
        "Content-Type": "application/json",
    }
    response = requests.request("POST", url, headers=headers, data=payload)
    print_info_log("", "response: status", response.status_code)
    if response.status_code == 200:
        response_json = response.json()
        print_info_log("", "INFO", response_json)
        result = {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "api_result_type": "success",
                    "concepts": {"Status": {"Status": "success"}},
                }
            ),
        }
        print_info_log("", "result", result)
        return result
    result = {
        "statusCode": 400,
        "body": json.dumps(
            {
                "api_result_type": "failure",
                "concepts": {"Status": {"Status": "failure"}},
            }
        ),
    }
    print_info_log("", "result", result)
    return result


def click_to_connect_wrapper_info():
    """
    Info API for Click To Connect API
    """
    return {
        "statusCode": 200,
        "body": json.dumps(
            {
                "api_name": "Click To Connect",
                "api_description": "This API Initiate Callback from AWS Connect.",
                "input_concepts": {"PhoneNumber": "true", "Name": "true"},
                "output_concepts": {"Status": "true"}
            }
        ),
    }
