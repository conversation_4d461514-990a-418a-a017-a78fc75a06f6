const fs = require("fs")
const path = require("path")

// File paths
const inputFilePath = path.join(__dirname, "../data/airline_code.json")
const outputFilePath = path.join(__dirname, "../data/airline_code_items_count.json")

// Read and convert the data
function convertAirlineCodes() {
	try {
		// Read the JSON file
		const rawData = fs.readFileSync(inputFilePath, "utf-8")
		const airlineCodes = JSON.parse(rawData)

		// Convert to the desired format
		const convertedData = Object.entries(airlineCodes).map(([airportName, airportCode]) => {
			return {
				airportName,
				airportCode
			}
		})

		// Write the converted data to a new JSON file
		fs.writeFileSync(outputFilePath, JSON.stringify(convertedData, null, 2), "utf-8")

		// Track and log the number of objects
		console.log("Conversion completed. Output saved to airline_code_items.json")
		console.log(`Number of objects in the generated file: ${convertedData.length}`)
	} catch (error) {
		console.error("Error processing files:", error)
	}
}

// Run the function
convertAirlineCodes()
